<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-webpage-customer</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.7.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-webpage-customer-provider</artifactId>
    <packaging>war</packaging>

    <dependencies>

        <!-- spring begin-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <!-- spring end -->

        <!-- cep -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-cep-spring-plugin</artifactId>
            <version>${fs-cep-spring-plugin.version}</version>
        </dependency>

        <!-- mongoDB -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
            <version>${mongo-spring-support.version}</version>
        </dependency>

        <!-- Dubbo -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- perf4j -->
        <dependency>
            <groupId>org.perf4j</groupId>
            <artifactId>perf4j</artifactId>
            <version>${perf4j.version}</version>
        </dependency>

        <!--ping monitor-->
        <dependency>
            <groupId>com.fxiaoke.pms</groupId>
            <artifactId>ping-monitor</artifactId>
            <version>${ping-monitor.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-dubbo-rest-plugin</artifactId>
            <version>${dubbo2rest.version}</version>
        </dependency>

        <!--配置中心引用包-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-webpage-customer-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-qixin-enterprise-converter</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <!-- common-logging 实际调用slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <!-- java.util.logging 实际调用slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>
        <!-- log4j实际调用slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <!-- 支持日志级别动态调整 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>
        <!-- 支持错误日志抽样上报日志中心 -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
            <version>${metrics-oss.version}</version>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
            <version>${protostuff.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- 测试 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-uc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.fakemongo</groupId>
            <artifactId>fongo</artifactId>
            <version>2.1.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-rest-client-common-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss.netty</groupId>
            <artifactId>netty</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-todo-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-common</artifactId>
            <version>0.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-enterpriserelation-rest-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ow2.util.bundles</groupId>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-objgroup-manage-common</artifactId>
            <version>${fs-qixin-objgroup-manage-common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                    <groupId>org.ow2.util.bundles</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-timezone-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>i18n-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
            <version>${fs-enterpriserelation-rest-api.version}</version>
        </dependency>
        <dependency>
            <artifactId>httpcore</artifactId>
            <groupId>org.apache.httpcomponents</groupId>
            <version>4.4.3</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.fs-user-extension</groupId>
            <artifactId>fs-user-extension-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-outer-util</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-relation-rest</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
            <version>${rocketmq-support.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-url</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-sandbox-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protostuff-runtime-proto</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-webpage-customer-designer</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-cep-spring-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>rpc-trace</artifactId>
            <version>${rpc-trace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-appcenter-rest-api</artifactId>
            <version>1.1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-hosts-record</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-sysdb</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>

        <!-- Resteasy begin -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
        </dependency>
        <!-- Resteasy end -->

        <!-- rest begin -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-common-api</artifactId>
            <version>6.6.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>i18n-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-core</artifactId>
            <version>6.6.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-replay</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-enterpriserelation</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-appcenter-rest-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-qixin-enterprise-converter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- rest end -->

        <!-- 应用中心接口api -->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-center-api</artifactId>
            <version>1.0.72-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-servicelib-fsi</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 数据引用关系的module -> 目前用于自定义页面对自定义组件的引用 -->
        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-reference-relationship</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <!--CRM通知接口 -> 主要用于获取未读 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-notify-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 统一功能权限的util -->
        <dependency>
            <groupId>fs-qixin-util</groupId>
            <artifactId>fs-qixin-permission-unified</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-appcenter-rest-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-webpage-customer-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-uc-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.k8s</groupId>
            <artifactId>fs-k8s-java-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-bizconf-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-api</artifactId>
            <version>8.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-service-api-support</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fcp-biz-server</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.el</groupId>
                    <artifactId>javax.el-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>

        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-restful-common</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-restful-client</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-restful-common</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-privilege-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mds-event</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-datastore</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-admin-api</artifactId>
        </dependency>
        <!--auth新接口jar包-->
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-auth-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-auth-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-rocketmq-support</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-linkapp-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-open-msg-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
            <version>2.0.12-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-common</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>fs-rocketmq-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-relation-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-autn-clent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-enterprise-linkapp-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>dubbo-restful-plugin</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>

        <!-- 文件服务依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-stone-commons-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>fs-webpage-customer-provider</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <configuration>
                    <server>tomcat-development-server</server>
                    <port>9299</port>
                    <path>/</path>
                    <uriEncoding>UTF-8</uriEncoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>com/facishare/webpage/webpage/customer/controller/model/*</exclude>
                        <exclude>com/facishare/webpage/webpage/customer/rest/arg/*</exclude>
                        <exclude>com/facishare/webpage/webpage/customer/rest/result/*</exclude>
                        <exclude>com/facishare/webpage/webpage/customer/api/model/*</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
