package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.paas.CheckOpenStatsArg;
import com.facishare.webpage.customer.controller.model.arg.paas.ResetUserMainChannelArg;
import com.facishare.webpage.customer.controller.model.arg.paas.SaveUserMainChannelArg;
import com.facishare.webpage.customer.controller.model.result.paas.CheckOpenStatsResult;
import com.facishare.webpage.customer.controller.model.result.paas.ResetUserMainChannelResult;
import com.facishare.webpage.customer.controller.model.result.paas.SaveUserMainChannelResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/11/13
 */
public interface UserWebMainChannelAction {
    /**
     * 保存用户自定义数据
     *
     * @param userInfo 身份信息
     * @param arg      入参
     * @return
     */
    SaveUserMainChannelResult saveUserWebMainChannelMenu(UserInfo userInfo,
                                                         ClientInfo clientInfo,
                                                         SaveUserMainChannelArg arg);

    /**
     * 重置用户主导航数据
     *
     * @param userInfo   身份信息
     * @param clientInfo 端
     * @param arg        入参
     * @return
     */
    ResetUserMainChannelResult resetUserMainChannelMenu(UserInfo userInfo,
                                                        ClientInfo clientInfo,
                                                        ResetUserMainChannelArg arg);
    CheckOpenStatsResult checkOpenStats( UserInfo userInfo,
                                         ClientInfo clientInfo,
                                         CheckOpenStatsArg arg);

}
