package com.facishare.webpage.customer.permission;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("menuPermissionServiceImpl")
public class MenuPermissionServiceImpl implements MenuPermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MenuPermissionServiceImpl.class);

    private List<PermissionService> permissionServices;

    @Autowired
    public void setPermissionServices(List<PermissionService> permissionServices) {
        this.permissionServices = permissionServices;
    }

    @Override
    public List<Menu> filterMenusWithPermission(int tenantId,String ea, int employeeId,int enterpriseRegisterSource, List<Menu> menus,Set<String> licenseVersion) {
        List<FilterResult> filterResults = permissionServices.parallelStream().map(permissionService -> {
            FilterArg arg = new FilterArg();
            arg.setEnterpriseAccount(ea);
            arg.setTenantId(tenantId);
            arg.setEmployeeId(employeeId);
            arg.setMenus(Lists.newArrayList(menus));
            arg.setLicenseVersions(licenseVersion);
            arg.setEnterpriseRegisterSource(enterpriseRegisterSource);
            LOGGER.info(permissionService.getClass().getSimpleName()+"-arg:{},size:{}", JSON.toJSONString(arg),arg.getMenus().size());
            FilterResult filterResult = permissionService.filterMenusWithPermission(arg);
            LOGGER.info(permissionService.getClass().getSimpleName()+"-size:{}",filterResult.getMenus().size());
            return filterResult;
        }).collect(Collectors.toList());

        FilterResult reduceResult =filterResults.stream().filter(filterResult -> !CollectionUtils.isEmpty(filterResult.getMenus()))
                .reduce((a, b) -> {
                    a.getMenus().retainAll(b.getMenus());
                    return a;
                }).get();

        return reduceResult.getMenus();
    }
}
