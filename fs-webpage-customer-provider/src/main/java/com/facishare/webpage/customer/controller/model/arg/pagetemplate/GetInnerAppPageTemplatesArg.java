package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/1/9
 */
@Data
public class GetInnerAppPageTemplatesArg implements Serializable {

    private String appId;

    public void valid() throws WebPageException {
        if(StringUtils.isEmpty(appId)){
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

}
