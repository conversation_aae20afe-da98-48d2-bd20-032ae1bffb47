package com.facishare.webpage.customer.controller.model.result.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class SetEmployeeCurrentHomePageLayoutResult implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("Success")
    private boolean success;

}
