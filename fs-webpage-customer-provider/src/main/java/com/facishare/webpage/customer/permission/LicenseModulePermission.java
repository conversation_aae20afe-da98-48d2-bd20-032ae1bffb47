package com.facishare.webpage.customer.permission;

import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service("licenseModulePermission")
public class LicenseModulePermission implements PermissionService {

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;


    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {

        FilterResult filterResult = new FilterResult();
        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        filterResult.setMenus(allowedMenus);

        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getTenantPrivilege() != null
                && !CollectionUtils.isEmpty(menu.getTenantPrivilege().getLicenseProductCodes())).collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);

        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(arg.getTenantId()));
        licenseContext.setUserId(String.valueOf(arg.getEmployeeId()));
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult moduleResult = licenseClient.queryModule(queryModuleArg);
        if (moduleResult == null || CollectionUtils.isEmpty(moduleResult.getResult())) {
            return filterResult;
        }
        Set<String> moduleCodes = moduleResult.getResult().stream().map(moduleInfoPojo -> moduleInfoPojo.getModuleCode()).collect(Collectors.toSet());
        List<Menu> remainMenus = checkMenus.stream().filter(menu -> menu.getTenantPrivilege().getLicenseModuleCodes()
                .stream().anyMatch(moduleCode -> moduleCodes.contains(moduleCode))).collect(Collectors.toList());
        allowedMenus.addAll(remainMenus);
        return filterResult;
    }
}
