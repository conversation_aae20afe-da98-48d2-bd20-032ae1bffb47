package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19.
 */
@Data
@Builder
public class UpdateUserPageTempleStatusArg extends BaseArg {
    private String templeId;
    private Integer status;

    @Override
    public void valid() throws WebPageException {
        if (StringUtils.isEmpty(templeId) || ObjectUtils.isEmpty(status) || status < -1 || status > 1) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
