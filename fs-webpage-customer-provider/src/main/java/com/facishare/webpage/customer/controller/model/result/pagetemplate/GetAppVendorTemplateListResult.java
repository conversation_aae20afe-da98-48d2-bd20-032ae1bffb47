package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.controller.model.UserAppPageTemplate;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.enterpriserelation2.result.EnterpriseInfoResult;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2019/12/11
 */
@Data
public class GetAppVendorTemplateListResult implements Serializable {

    private List<UserAppPageTemplate> userAppPageTemplateList = Lists.newArrayList();

    @JsonProperty("enterpriseInfo")
    private Map<Integer, EnterpriseInfoResult> enterpriseInfo;

    private boolean hasOutUser = true;

    private String defaultTemplateId;

}
