package com.facishare.webpage.customer.permission;

import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.facishare.webpage.customer.remote.RoleService;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service("rolePermissionService")
public class RolePermissionService implements PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RolePermissionService.class);

    @Resource
    private com.facishare.organization.adapter.api.permission.service.PermissionService orgPermissionService;

    @Autowired
    RoleService roleService;

    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {

        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        FilterResult filterResult = new FilterResult();
        filterResult.setMenus(allowedMenus);

        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getPersonPrivilege() != null && !CollectionUtils.isEmpty(menu.getPersonPrivilege().getRoleCodes())).collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);

        BatchGetRoleCodesByEmployeeIds.Argument argument = new BatchGetRoleCodesByEmployeeIds.Argument();
        List<String> roleCodes = new ArrayList<>();
        try {
            if (WebPageGraySwitch.isAllowUseNewAuthInterfaceGray(arg.getTenantId())) {
                roleCodes = roleService.getRoleIdsByUser(arg.getTenantId(), arg.getEmployeeId(), "facishare-system");
            } else {
                argument.setEmployeeIds(Lists.newArrayList(arg.getEmployeeId()));
                argument.setAppId("facishare-system");
                argument.setEnterpriseId(arg.getTenantId());
                argument.setCurrentEmployeeId(arg.getEmployeeId());
                BatchGetRoleCodesByEmployeeIds.Result result = orgPermissionService.batchGetRoleCodesByEmployeeIds(argument);
                if (result == null || result.getEmployeeIdRolesMap() == null) {
                    LOGGER.warn("batchGetRoleCodesByEmployeeIds is null arg:{}", argument);
                    return filterResult;
                }
                roleCodes = result.getEmployeeIdRolesMap().get(String.valueOf(arg.getEmployeeId()));
            }

            List<String> finalRoleCodes = roleCodes;
            List<Menu> remainMenus = checkMenus.stream().filter(menu -> menu.getPersonPrivilege().getRoleCodes().stream().anyMatch(roleCode -> finalRoleCodes.contains(roleCode))).collect(Collectors.toList());
            allowedMenus.addAll(remainMenus);
        } catch (Exception e) {
            LOGGER.error("batchGetRoleCodesByEmployeeIds arg:{},error:{}", argument, e.getMessage(), e);
        }

        return filterResult;
    }
}
