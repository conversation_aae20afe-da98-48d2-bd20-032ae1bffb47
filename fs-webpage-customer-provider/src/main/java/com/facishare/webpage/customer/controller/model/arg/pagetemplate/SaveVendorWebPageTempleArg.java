package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class SaveVendorWebPageTempleArg implements Serializable {

    private String templateId;

    private String layoutId;

    public void valid() throws WebPageException {
        if (StringUtils.isEmpty(layoutId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

}
