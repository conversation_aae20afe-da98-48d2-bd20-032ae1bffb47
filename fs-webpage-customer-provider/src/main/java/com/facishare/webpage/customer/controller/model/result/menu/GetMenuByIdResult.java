package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.controller.model.MenuTempleVO;
import com.facishare.webpage.customer.controller.model.TenantMenuItemVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/26
 */
@Data
public class GetMenuByIdResult implements Serializable {

    private MenuTempleVO objectData;

    private List<TenantMenuItemVO> detailMenuItems;

    private List<String> roleIdList;

    private List<Scope> scopeList;

}
