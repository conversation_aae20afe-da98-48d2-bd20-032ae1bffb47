package com.facishare.webpage.customer.controller.model;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.model.MenuItem;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhangyu on 2020/10/26
 */
@Data
@Builder
public class MenuTemplateDto {

    private String menuId;
    private int tenantId;
    private int appType;
    private String appId;
    private int employeeId;
    private List<MenuItem> menuItemList;
    private String name;
    private String description;
    private List<String> roleIdList;
    private List<Scope> scopeList;
    private String sourceType;
    private Boolean isShowMenuIcon = true;

    private Boolean hiddenQuickCreate = false;


}
