package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.paas.*;
import com.facishare.webpage.customer.controller.model.result.paas.*;

/**
 * Created by zhangyu on 2020/11/12
 */
public interface PaaSAppAction {

    /**
     * 创建PaaS应用
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CreatePaaSAppResult createPaaSApp(UserInfo userInfo, ClientInfo clientInfo, CreatePaaSAppArg arg);

    /**
     * 更新PaaS应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    UpdatePaaSAppResult updatePaaSApp(UserInfo userInfo, ClientInfo clientInfo, UpdatePaaSAppArg arg);

    /**
     * 管理后台获取PaaS应用
     *
     * @param userInfo
     * @param arg
     * @returnw
     */
    GetPaaSAppByAppIdResult getPaaSAppByAppId(UserInfo userInfo, ClientInfo clientInfo, GetPaaSAppByAppIdArg arg);

    GetPaaSAppConfigResult getPaaSAppConfig(UserInfo userInfo, ClientInfo clientInfo, GetPaaSAppConfigArg arg);

    /**
     * 启用PaaS应用
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    EnablePaaSAppResult enablePaaSApp(UserInfo userInfo, ClientInfo clientInfo, EnablePaaSAppArg arg);

    /**
     * 停用PaaS应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DisablePaaSAppResult disablePaaSApp(UserInfo userInfo, EnablePaaSAppArg arg);

    /**
     * 删除PaaS应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DeletePaaSAppResult deletePaaSApp(UserInfo userInfo, DeletePaaSAppArg arg);

    /**
     * 获取应用列表
     *
     * @param userInfo
     * @param clientInfo
     * @return
     */
    GetAppListResult getAppList(UserInfo userInfo, ClientInfo clientInfo);

    /**
     * check PaaS应用是否具备启用的条件
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CheckPaaSAppResult checkPaaSApp(UserInfo userInfo, ClientInfo clientInfo, CheckPaaSAppArg arg);

    /**
     * 获取PaaS应用的icon
     *
     * @param userInfo
     * @return
     */
    GetPaaSIconResult getPaaSIconList(UserInfo userInfo);

    /**
     * 将paasAppIcon刷库
     */
    void setPaasIconToCPath(SetApathToCpathArg arg) throws InterruptedException;

    /**
     * 获取企信状态
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetAppStatusResult getQiXinAppStatus(UserInfo userInfo, ClientInfo clientInfo, GetAppStatusArg arg);

}
