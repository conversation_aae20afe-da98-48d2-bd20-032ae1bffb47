package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.BatchGetCustomCountArg;
import com.facishare.webpage.customer.controller.model.arg.GetCustomCountArg;
import com.facishare.webpage.customer.controller.model.result.BatchGetCustomCountResult;
import com.facishare.webpage.customer.controller.model.result.GetCustomCountResult;

/**
 * Created by <PERSON><PERSON> on 19/9/24.
 */
public interface CustomCountAction {
    GetCustomCountResult getCustomCount(UserInfo userInfo, OuterUserInfo outerUserInfo, GetCustomCountArg arg);

    BatchGetCustomCountResult  batchGetCustomCount(UserInfo userInfo, OuterUserInfo outerUserInfo, BatchGetCustomCountArg arg);
}
