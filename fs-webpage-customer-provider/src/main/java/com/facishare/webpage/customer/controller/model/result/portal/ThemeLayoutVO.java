package com.facishare.webpage.customer.controller.model.result.portal;

import com.facishare.webpage.customer.dao.entity.ThemeLayoutEntity;
import com.facishare.webpage.customer.model.LayoutStructure;
import lombok.Data;

import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/4.
 */
@Data
public class ThemeLayoutVO {
    private String id;
    private String apiName;
    private String name;
    private LayoutStructure layoutStructure;
    private String themeTemplateApiName;
    private String sourceType;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;

    public static ThemeLayoutVO of(ThemeLayoutEntity entity) {
        ThemeLayoutVO vo = new ThemeLayoutVO();
        vo.setId(entity.getId());
        vo.setApiName(entity.getApiName());
        vo.setName(entity.getName());
        vo.setLayoutStructure(Objects.isNull(entity.getLayoutStructure()) ? null : new LayoutStructure(entity.getLayoutStructure()));
        vo.setThemeTemplateApiName(entity.getThemeTemplateApiName());
        vo.setSourceType(entity.getSourceType());
        vo.setCreatorId(entity.getCreatorId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdaterId(entity.getUpdaterId());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }
}
