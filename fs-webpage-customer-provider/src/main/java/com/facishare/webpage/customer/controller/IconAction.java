package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.GetIconListArg;
import com.facishare.webpage.customer.controller.model.result.paas.GetIconResult;

public interface IconAction {
    /**
     * 获取icon
     * @param userInfo
     * @return
     */
    GetIconResult getIconList(UserInfo userInfo, GetIconListArg arg);
}
