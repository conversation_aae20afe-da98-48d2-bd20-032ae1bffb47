package com.facishare.webpage.customer.controller.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.UserMenuItem;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14.
 */
@Data
public class UserMenuTempleVo implements Serializable {

    //菜单id
    @JSONField(name = "M1")
    private String id;

    @JSONField(name = "M2")
    private String displayName;

    @JSONField(name = "M3")
    private List<UserMenuItem> items = Lists.newArrayList();

    //是否系统预制CRM菜单
    @JSONField(name = "M4")
    private Boolean isSystem = false;

    //是否当前客户默认选择
    @JSONField(name = "M5")
    private Boolean isCurrent = false;

    //菜单折叠时，是否显示菜单图标
    @JSONField(name = "M6")
    private Boolean isShowMenuIcon = true;

    @JSONField(name = "M7")
    private Boolean hiddenQuickCreate = false;

    @JSONField(name = "M8")
    private Boolean dealHomeFlag = false;


}
