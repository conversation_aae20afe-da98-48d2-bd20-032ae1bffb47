package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.api.model.PageTemplate;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/9/19
 */
@Data
public class GetAppPageTemplesResult implements Serializable {

    @JsonProperty("appPageTempleList")
    private List<PageTemplate> appPageTempleList;
}
