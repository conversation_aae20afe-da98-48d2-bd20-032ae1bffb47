package com.facishare.webpage.customer.controller.model.result.homepage;

import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class GetHomePageLayoutListResult implements Serializable {
    @SerializedName("HomePageLayoutList")
    private List<HomePageLayoutTO> homePageLayoutList = Lists.newArrayList();

}
