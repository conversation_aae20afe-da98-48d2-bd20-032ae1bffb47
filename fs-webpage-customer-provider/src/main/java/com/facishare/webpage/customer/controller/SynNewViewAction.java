package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

/**
 * Created by she<PERSON> on 19/9/24.
 */
public interface SynNewViewAction {
    /**
     * 获取迁移状态
     *
     * @param userInfo
     * @param getSynNewViewStatusArg
     * @return
     */
    GetSynNewViewStatusResult getSynNewViewStatus(UserInfo userInfo, GetSynNewViewStatusArg getSynNewViewStatusArg);

    /**
     * 开始迁移
     *
     * @param userInfo
     * @param SynNewViewArg
     * @return
     */
    SynNewViewResult synNewView(UserInfo userInfo, ClientInfo clientInfo,SynNewViewArg SynNewViewArg);

    /**
     * 确认迁移
     * @param userInfo
     * @param confirmSynNewViewArg
     * @return
     */
    ConfirmSynNewViewResult confirmSynNewView(UserInfo userInfo, ConfirmSynNewViewArg confirmSynNewViewArg);

    /**
     * 取消迁移
     *
     * @param userInfo
     * @param cancelSynNewViewArg
     * @return
     */
    CancelSynNewViewResult cancelSynNewView(UserInfo userInfo, CancelSynNewViewArg cancelSynNewViewArg);


    /**
     * 任意修改迁移状态：仅用于开发人员调试使用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    void updateSynNewViewStatus(UserInfo userInfo, UpdateSynNewViewStatusArg arg);


    /**
     *  迁移失败后重置迁移状态为待迁移
     *
     * @param userInfo
     * @param
     * @return
     */
    void resetSynNewViewStatus(UserInfo userInfo,ResetSynNewViewStatusArg arg);


    /**
     *    确认迁移之后回滚迁移
     *    1、将企业从新版crm的灰度移除
     *    2、将企业迁移状态改为迁移完成待确认
     *    3、走取消迁移的逻辑
     *
     * @param userInfo
     * @param
     * @return
     */
    RollbackSynNewViewResult rollbackSynNewVieww(UserInfo userInfo,RollBackSynNewViewArg arg);


    AutomaticSynNewViewResult automaticSynNewView(UserInfo userInfo, AutomaticSynNewViewArg arg);

}
