package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.result.CommonlyUsedMenuResult;
import com.facishare.webpage.customer.controller.model.arg.menu.*;
import com.facishare.webpage.customer.controller.model.result.menu.*;

import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9.
 */
public interface UserMenuAction {

    /**
     * 获取互联菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param outerUserInfo
     * @param arg
     * @return
     */
    GetUserMenuByAppIdResult getUserMenuByAppId(UserInfo userInfo, ClientInfo clientInfo, OuterUserInfo outerUserInfo, GetUserMenuByAppIdArg arg);

    /**
     * 获取互联支持飘数的菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param outerUserInfo
     * @param arg
     * @return
     */
    GetUserMenuByAppIdResult getSupportUnReadUserMenuByAppId(UserInfo userInfo, ClientInfo clientInfo, OuterUserInfo outerUserInfo, GetUserMenuByAppIdArg arg);

    /**
     * 根据id获取企业内的菜单数据
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserMenuByIdResult getUserMenuById(UserInfo userInfo, ClientInfo clientInfo, GetUserMenuByIdArg arg);

    /**
     * 根据id获取企业内的支持飘数的菜单数据
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserMenuByIdResult getSupportUnReadUserMenuById(UserInfo userInfo, ClientInfo clientInfo, GetUserMenuByIdArg arg);

    /**
     * 获取crm菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserMenusResult getUserMenus(UserInfo userInfo, ClientInfo clientInfo, GetUserMenusArg arg);


    GetUserMenuModelResult getUserMenuModel(UserInfo userInfo, ClientInfo clientInfo, OuterUserInfo outerUserInfo, GetUserMenuModelArg arg);

    SaveUserMenuModelResult saveUserMenuModel(UserInfo userInfo, ClientInfo clientInfo, OuterUserInfo outerUserInfo, SaveUserMenuModelArg arg);

    /**
     * check菜单状态
     *
     * @param userInfo
     * @param arg
     * @return
     */
    CheckMenuStatusResult checkMenuStatus(UserInfo userInfo, CheckMenuStatusArg arg);

    /**
     * 创建菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CreateUserMenuResult createUserMenu(UserInfo userInfo, ClientInfo clientInfo, CreateUserMenuArg arg);

    /**
     * 删除菜单
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DeleteUserMenuResult deleteUserMenu(UserInfo userInfo, DeleteUserMenuArg arg);

    /**
     * 判断版本权限
     *
     * @param userInfo
     * @param apiNames
     * @return
     */
    Map<String, Map<String, Boolean>> checkVersionPrivilege(UserInfo userInfo, Set<String> apiNames);

    /**
     * 创建个人通用菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CommonlyUsedMenuResult createCommonlyUsedMenu(UserInfo userInfo, ClientInfo clientInfo, CreateCommonlyUsedMenuArg arg);

    /**
     * 查询通用菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CommonlyUsedMenuResult queryCommonlyUsedMenu(UserInfo userInfo, ClientInfo clientInfo, QueryCommonlyUsedMenuArg arg);

    /**
     * 获取互联应用的接口
     *
     * @param userInfo
     * @param arg
     * @return
     */
    GetCrossMenuResult getUserCrossMenu(UserInfo userInfo, GetCrossMenuArg arg);

    /**
     * 获取PaaS应用的菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetPaaSUserMenusResult getPaaSUserMenus(UserInfo userInfo, ClientInfo clientInfo, GetPaaSUserMenusArg arg);

    /**
     * 设置默认菜单
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SetCurrentMenuResult setCurrentMenu(UserInfo userInfo, SetCurrentMenuArg arg);

    /**
     * save user menu
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    SaveUserMenuResult saveUserMenu(UserInfo userInfo, ClientInfo clientInfo, SaveUserMenuArg arg);

    /**
     * save user isFoldMenu status
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SaveUserFoldMenuStatusResult saveUserFoldMenuStatus(UserInfo userInfo, OuterUserInfo outerUserInfo, SaveUserFoldMenuStatusArg arg);

    GetUserFoldMenuStatusResult getUserFoldMenuStatus(UserInfo userInfo, OuterUserInfo outerUserInfo, GetUserFoldMenuStatusArg arg);


}
