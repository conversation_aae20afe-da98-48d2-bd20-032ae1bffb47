package com.facishare.webpage.customer.controller.model.result.bar;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class GetUtilityBarIconResult implements Serializable {

    private List<UtilityBarIcon> utilityBarIconList;

    @Data
    public static class UtilityBarIcon implements Serializable {
        @JsonProperty("iconIndex")
        private String iconIndex;
        @JsonProperty("iconAddress")
        private String iconAddress;
    }
}
