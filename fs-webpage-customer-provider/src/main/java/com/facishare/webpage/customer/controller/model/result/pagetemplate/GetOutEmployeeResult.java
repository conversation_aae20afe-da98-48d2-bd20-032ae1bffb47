package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/12/29
 */
@Data
public class GetOutEmployeeResult implements Serializable {

    private List<OutEmployee> data = Lists.newArrayList();

    @Data
    public static class OutEmployee implements Serializable {

        private Long outUserId;

        private Long outTenantId;

        private String name;

        private String profileImage;

        private String nameSpell;

        private String gender;
    }

}
