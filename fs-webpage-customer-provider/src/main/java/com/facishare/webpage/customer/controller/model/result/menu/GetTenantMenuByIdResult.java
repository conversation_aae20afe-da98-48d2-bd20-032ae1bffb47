package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.controller.model.TenantMenuItemVO;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9.
 */
@Data
public class GetTenantMenuByIdResult extends BaseResult {
    @SerializedName("tenantMenuItems")
    private List<TenantMenuItemVO> tenantMenuItemVOS;

    @SerializedName("isShowMenuIcon")
    private Boolean isShowMenuIcon = true;
    @SerializedName("hiddenQuickCreate")
    private Boolean hiddenQuickCreate = false;
    @SerializedName("dealHomeFlag")
    private Boolean dealHomeFlag = false;
}
