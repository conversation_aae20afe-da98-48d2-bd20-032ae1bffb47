package com.facishare.webpage.customer.controller.model.arg;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;


@Data
public class QueryCustomerMenuListArg implements Serializable {
    /**
     * 自定义页面Id
     */
    private String pageTemplateId;
    /**
     * 0企业内
     * 1互联
     */
    private int applyType;
    /**
     * 菜单类型
     */
    private int menuType;

    /**
     * 对象apiName
     */
    //private String objectApiName;

    /**
     * 对象业务类型
     */
    //private String objectRecordTypeApiName;
    public void valid() {
        if (StringUtils.isEmpty(pageTemplateId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);

        }
    }
}