package com.facishare.webpage.customer.controller.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/4.
 */
@Data
public class DefaultMenuVO implements Serializable {
    @SerializedName("api_name")
    private String apiName;
    @SerializedName("display_name")
    private String displayName;
    @SerializedName("icon_index")
    private Integer iconIndex;
    @SerializedName("is_active")
    private Boolean isActive;
    @SerializedName("searchWords")
    private List<String> searchWords;
}
