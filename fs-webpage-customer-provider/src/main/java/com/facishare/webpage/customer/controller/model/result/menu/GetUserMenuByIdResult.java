package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.api.model.UserMenuItem;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/8 9:58 上午
 */
@Data
public class GetUserMenuByIdResult implements Serializable {

    @SerializedName("items")
    private List<UserMenuItem> items = Lists.newArrayList();
    @SerializedName("configinfo")
    private Map<String, Object> configInfo;

    //菜单折叠时，是否显示菜单图标
    @SerializedName("isShowMenuIcon")
    private Boolean isShowMenuIcon = true;

    @SerializedName("hiddenQuickCreate")
    private Boolean hiddenQuickCreate = false;

    @SerializedName("incomplete")
    private boolean incomplete = false;

}
