package com.facishare.webpage.customer.controller.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonResult {
    private boolean success;

    public static CommonResult successResult() {
        return CommonResult.builder().success(true).build();
    }
}
