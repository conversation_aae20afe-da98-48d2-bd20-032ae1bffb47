package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.menu.FindSystemMenuDataByApiNamesArg;
import com.facishare.webpage.customer.controller.model.arg.menu.HiddenMenusArg;
import com.facishare.webpage.customer.controller.model.arg.menu.MenuInitAddObjectArg;
import com.facishare.webpage.customer.controller.model.arg.menu.MenuInitAddObjectsArg;
import com.facishare.webpage.customer.controller.model.result.menu.FindSystemMenuByApiNamesResult;
import com.facishare.webpage.customer.controller.model.result.menu.HiddenMenusResult;
import com.facishare.webpage.customer.controller.model.result.menu.MenuInitAddObjectResult;
import com.facishare.webpage.customer.controller.model.result.menu.MenuInitAddObjectsResult;

/**
 * Created by <PERSON>hangyu on 2020/3/24
 */
public interface InitMenuAction {

    MenuInitAddObjectResult menuAddItem(UserInfo userInfo, MenuInitAddObjectArg arg);

    MenuInitAddObjectsResult menuAddObjects(UserInfo userInfo, MenuInitAddObjectsArg arg);

    HiddenMenusResult hiddenMenus(UserInfo userInfo, HiddenMenusArg arg);

    FindSystemMenuByApiNamesResult findSystemMenuByApiNames(UserInfo userInfo, FindSystemMenuDataByApiNamesArg arg);

}
