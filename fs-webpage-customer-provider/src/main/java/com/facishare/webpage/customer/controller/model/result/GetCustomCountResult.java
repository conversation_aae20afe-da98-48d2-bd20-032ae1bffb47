package com.facishare.webpage.customer.controller.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by shecheng on 19/9/25.
 */
@Data
public class GetCustomCountResult implements Serializable{
    @Tag(1)
    @JSONField(name = "M1")
    private int notReadCount;

    @Tag(2)
    @JSONField(name = "M2")
    private int notDealCount;

    @Tag(3)
    @JSONField(name = "M3")
    private boolean notReadFlag;

}
