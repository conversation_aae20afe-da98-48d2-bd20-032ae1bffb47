package com.facishare.webpage.customer.controller.model.result.paas;

import com.facishare.webpage.customer.api.model.AppItemVO;
import com.facishare.webpage.customer.api.model.PaaSAppConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
public class GetAppListResult implements Serializable {

    private List<AppItemVO> appItemVOList;

    private boolean hasPlatformAddPermission;

    private boolean hasCustomAddPermission;

    private List<PaaSAppConfig> appConfigList;

}
