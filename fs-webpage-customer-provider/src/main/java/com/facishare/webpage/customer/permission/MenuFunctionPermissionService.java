package com.facishare.webpage.customer.permission;

import com.facishare.organization.adapter.api.permission.model.CheckHasManageAbility;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service("menuFunctionPermissionService")
public class MenuFunctionPermissionService implements PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MenuFunctionPermissionService.class);


    @Resource
    private com.facishare.organization.adapter.api.permission.service.PermissionService permissionService;

    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {

        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        FilterResult filterResult = new FilterResult();
        filterResult.setMenus(allowedMenus);

        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getPersonPrivilege() != null &&
                !CollectionUtils.isEmpty(menu.getPersonPrivilege().getFunctionCode())&&
                menu.getPersonPrivilege().getFunctionCode().contains("manager_index")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkMenus)) {
            return filterResult;
        }
        CheckHasManageAbility.Argument argument = new CheckHasManageAbility.Argument();
        argument.setEnterpriseId(arg.getTenantId());
        argument.setCurrentEmployeeId(arg.getEmployeeId());
        try {
            //这里只是校验了这个人有没有管理权限  并没有直接校验菜单配置的功能权限
            CheckHasManageAbility.Result result = permissionService.checkHasAbilityForNewManagement(argument);
            if (!result.getHasAbility()) {
                allowedMenus.removeAll(checkMenus);
            }
        } catch (Exception e) {
            LOGGER.error("checkHasAbilityForNewManagement argument:{},error:{}", argument, e.getMessage(), e);
        }
        return filterResult;
    }
}
