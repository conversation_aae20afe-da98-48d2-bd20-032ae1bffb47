package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;

/**
 * 互联应用action
 */
public interface LinkAppAction {
    /**
     * 创建自定义互联应用
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CreateLinkAppResult createLinkApp(UserInfo userInfo, ClientInfo clientInfo, CreateLinkAppArg arg);

    /**
     * 更新Link应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    UpdateLinkAppResult updateLinkApp(UserInfo userInfo, ClientInfo clientInfo,UpdateLinkAppArg arg);

    /**
     * 管理后台获取Link应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    GetLinkAppByAppIdResult getLinkAppByAppId(UserInfo userInfo, ClientInfo clientInfo, GetLinkAppByAppIdArg arg);

    /**
     * 启用Link应用
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    EnableLinkAppResult enableLinkApp(UserInfo userInfo, ClientInfo clientInfo, EnableLinkAppArg arg);

    /**
     * 停用Link应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DisableLinkAppResult disableLinkApp(UserInfo userInfo, EnableLinkAppArg arg);

    /**
     * 删除Link应用
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DeleteLinkAppResult deleteLinkApp(UserInfo userInfo, DeleteLinkAppArg arg);

    /**
     * 获取应用列表
     *
     * @param userInfo
     * @param clientInfo
     * @return
     */
    GetLinkAppListResult getLinkAppList(UserInfo userInfo, ClientInfo clientInfo);

    GetUpLinkAppListResult getUpLinkAppList(UserInfo userInfo, ClientInfo clientInfo);


    /**
     * check Link应用是否具备启用的条件
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    CheckLinkAppResult checkLinkApp(UserInfo userInfo, ClientInfo clientInfo, CheckLinkAppArg arg);

}
