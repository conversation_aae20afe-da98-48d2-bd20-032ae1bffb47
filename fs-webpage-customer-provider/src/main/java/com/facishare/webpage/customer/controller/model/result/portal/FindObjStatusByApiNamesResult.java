package com.facishare.webpage.customer.controller.model.result.portal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON>Hui
 * @Data : 2025/3/24
 * @Description :
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FindObjStatusByApiNamesResult {
	private Map<String, ObjStatus> objStatus;	// api -> ret
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class ObjStatus{
		boolean deleted;	// 对象已经被删除
		boolean disabled;	// 对象已经被禁用
		boolean masterDetail;
		boolean removed;	// 被从当前应用业务对象列表移除
	}
}
