package com.facishare.webpage.customer.controller.model.result.customer;

import com.facishare.webpage.customer.model.CustomerMenu;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetCustomerMenuListResult implements Serializable {

    private List<CustomerMenu> customerMenuList;

}
