package com.facishare.webpage.customer.permission;

import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.fxiaoke.appcenter.restapi.arg.QueryComponentsByFsUserArg;
import com.fxiaoke.appcenter.restapi.enums.AppAccessTypeEnum;
import com.fxiaoke.appcenter.restapi.model.vo.UserCanViewListVO;
import com.fxiaoke.appcenter.restapi.service.OpenFsUserAppViewService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("appOpenPermissionService")
public class AppOpenPermissionService implements PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppOpenPermissionService.class);

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;

    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {
        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        List<Menu> checkMenus = allowedMenus.stream()
                .filter(menu -> menu.getPersonPrivilege() != null && !Strings.isNullOrEmpty(menu.getPersonPrivilege().getAppId()))
                .collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);

        Set<String> appIds = queryAllAllowedAppId(arg.getEnterpriseAccount(), arg.getTenantId(), arg.getEmployeeId());
        List<Menu> remainAllowedMenus = checkMenus.stream().filter(menu -> appIds.contains(menu.getPersonPrivilege().getAppId())).collect(Collectors.toList());
        allowedMenus.addAll(remainAllowedMenus);
        FilterResult result = new FilterResult();
        result.setMenus(allowedMenus);
        return result;
    }

    private Set<String> queryAllAllowedAppId(String ea, int enterpriseId, int employeeId) {
        QueryComponentsByFsUserArg fsUserArg = new QueryComponentsByFsUserArg();
        try {
            com.fxiaoke.appcenter.restapi.common.HeaderObj headerObj = com.fxiaoke.appcenter.restapi.common.HeaderObj.newInstance(String.valueOf(enterpriseId));
            fsUserArg.setFsUserVO(ea, employeeId, null);
            fsUserArg.setVersion("1.3");
            fsUserArg.setAppAccessTypeEnum(AppAccessTypeEnum.ANDROID);
            com.fxiaoke.appcenter.restapi.common.BaseResult<List<UserCanViewListVO>> baseResult = openFsUserAppViewService.queryComponentsByFsUser(headerObj, fsUserArg);
            return baseResult.getResult().stream().map(userCanViewListVO -> userCanViewListVO.getAppId()).collect(Collectors.toSet());
        } catch (Exception e) {
            LOGGER.error("queryAllAllowedAppId arg:{},error:{}", fsUserArg, e.getMessage(), e);
        }
        return Sets.newHashSet();
    }

}
