package com.facishare.webpage.customer.controller.model.result.menu;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.controller.model.UserMenuTempleVo;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class GetUserMenusResult extends BaseResult {

    @JSONField(name = "M1")
    @SerializedName("menus")
    private List<UserMenuTempleVo> userMenuTempleVos = Lists.newLinkedList();


    @JSONField(name = "M2")
    @SerializedName("configinfo")
    private Map<String, Object> configInfo;

    @JSONField(name = "M3")
    @SerializedName("incomplete")
    private boolean incomplete = false;

}
