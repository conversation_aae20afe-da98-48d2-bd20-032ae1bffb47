package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.component.GetComponentArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetCrossNavigationMenuArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetInnerNavigationMenuArg;
import com.facishare.webpage.customer.controller.model.result.component.GetComponentResult;
import com.facishare.webpage.customer.controller.model.result.component.GetCrossNavigationMenuResult;
import com.facishare.webpage.customer.controller.model.result.component.GetInnerNavigationMenuResult;

/**
 * Created by zhangyu on 2020/6/17
 */
public interface ComponentAction {

    /**
     * 管理后台 对象详情页   移动端独立布局配置中的  导航容器组件    点击任意页签之后  查询页签列表
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetComponentResult getComponentList(UserInfo userInfo, ClientInfo clientInfo, GetComponentArg arg);

    /**
     * 获取互联的导航组件中的菜单
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetCrossNavigationMenuResult getCrossNavigationMenu(UserInfo userInfo, ClientInfo clientInfo, GetCrossNavigationMenuArg arg);

    /**
     * 获取企业内导航组件中的菜单
     *
     * @param userInfo
     * @param clientInfo
     * @return
     */
    GetInnerNavigationMenuResult getInnerNavigationMenu(UserInfo userInfo, ClientInfo clientInfo, GetInnerNavigationMenuArg arg);

}
