package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.controller.model.DefaultMenuVO;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON>yi on 2019/9/23.
 */
@Data
public class GetTenantDefaultMenuDetailByAppIdResult implements Serializable{
    @SerializedName("menuDataList")
    private List<DefaultMenuVO> defaultMenuVOS;
}
