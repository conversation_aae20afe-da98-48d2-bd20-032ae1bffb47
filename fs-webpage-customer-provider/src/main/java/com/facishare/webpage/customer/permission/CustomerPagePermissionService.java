package com.facishare.webpage.customer.permission;

import com.facishare.webpage.customer.api.model.arg.GetPageTemplatesArg;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.result.GetPageTemplatesResult;
import com.facishare.webpage.customer.api.service.TenantPageTempleService;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service("dataPermissionService")
public class CustomerPagePermissionService implements PermissionService {

    public static final int PAGE_DATA_TYPE = 1;

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerPagePermissionService.class);

    @Resource
    private TenantPageTempleService tenantPageTempleService;


    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {
        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getDataPrivilege() != null && menu.getDataPrivilege().getDataType() > 0).collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);
        List<Menu> remainMenus = checkMenus.stream().filter(menu -> checkMenuDataExist(arg.getTenantId(), arg.getEmployeeId(), menu)).collect(Collectors.toList());
        allowedMenus.addAll(remainMenus);
        FilterResult result = new FilterResult();
        result.setMenus(allowedMenus);
        return result;
    }

    private boolean checkMenuDataExist(int tenantId, int employeeId, Menu menu) {
        GetPageTemplatesArg arg = new GetPageTemplatesArg();
        try {
            int dataType = menu.getDataPrivilege().getDataType();
            switch (dataType) {
                case PAGE_DATA_TYPE:
                    arg.setDownTenantId(tenantId);
                    arg.setDownEmployeeId(employeeId);
                    arg.setAppId(menu.getDataPrivilege().getAppId());
                    arg.setType("web");
                    GetPageTemplatesResult result = tenantPageTempleService.getPageTemplates(arg);
                    return !CollectionUtils.isEmpty(result.getPageTemplates());
                default:
                    return true;
            }
        } catch (Exception e) {
            LOGGER.error("getPageTemplates arg:{},error:{}", arg, e.getMessage(), e);

        }
        return false;
    }
}
