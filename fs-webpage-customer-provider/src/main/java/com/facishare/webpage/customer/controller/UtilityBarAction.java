package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarForManageArg;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarForUserArg;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarIconArg;
import com.facishare.webpage.customer.controller.model.arg.bar.SaveUtilityBarForManageArg;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarForManageResult;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarForUserResult;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarIconResult;
import com.facishare.webpage.customer.controller.model.result.bar.SaveUtilityBarForManageResult;

/**
 * <AUTHOR>
 */
public interface UtilityBarAction {

    /**
     * 管理侧获取工具栏
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUtilityBarForManageResult getUtilityBarForManage(UserInfo userInfo, ClientInfo clientInfo, GetUtilityBarForManageArg arg);

    /**
     * 用户侧获取工具栏
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUtilityBarForUserResult getUtilityBarForUser(UserInfo userInfo, OuterUserInfo outerUserInfo, ClientInfo clientInfo, GetUtilityBarForUserArg arg);

    /**
     * 获取工具栏Icon
     *
     * @param userInfo
     * @param arg
     * @return
     */
    GetUtilityBarIconResult getUtilityBarIcon(UserInfo userInfo, GetUtilityBarIconArg arg);

    /**
     * 保存工具栏数据
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SaveUtilityBarForManageResult saveUtilityBarForManage(UserInfo userInfo, ClientInfo clientInfo, SaveUtilityBarForManageArg arg);

}
