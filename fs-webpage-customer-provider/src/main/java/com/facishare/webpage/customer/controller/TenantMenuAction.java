package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.core.IndexIcon;
import com.facishare.webpage.customer.controller.model.DefaultMenuVO;
import com.facishare.webpage.customer.controller.model.MenuRoleInfo;
import com.facishare.webpage.customer.controller.model.arg.menu.*;
import com.facishare.webpage.customer.controller.model.result.menu.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;


/**
 * Created by zhangyi on 2019/9/9.
 */
public interface TenantMenuAction {

    GetTenantMenuByIdResult getTenantMenuDetailById(UserInfo userInfo, ClientInfo clientInfo, GetTenantMenuDetailByIdArg arg);

    GetCrossMenuByIdResult getCrossMenuById(UserInfo userInfo, ClientInfo clientInfo, GetCrossMenuByIdArg arg);

    UpdateTenantMenuResult updateTenantMenu(UserInfo userInfo, ClientInfo clientInfo, UpdateTenantMenuArg arg);

    GetTenantDefaultMenuDetailByAppIdResult getTenantDefaultMenuDetailByAppId(UserInfo userInfo, ClientInfo clientInfo, GetTenantDefaultMenuDetailByAppIdArg arg);

    List<DefaultMenuVO> menuItemList(UserInfo userInfo, ClientInfo clientInfo, MenuItemListArg arg);

    MenuViewResult menuView(UserInfo userInfo, ClientInfo clientInfo, MenuViewArg arg);

    GetMenuByIdResult getMenuById(UserInfo userInfo, ClientInfo clientInfo, GetMenuByIdArg arg);

    MenuListResult menuList(UserInfo userInfo, ClientInfo clientInfo, MenuListArg arg);

    ValidateMenuLimitResult validateMenuLimit(ValidateMenuLimitArg arg, UserInfo userInfo);

    ValidateMenuNameResult validateMenuName(ValidateMenuNameArg arg, UserInfo userInfo);

    List<MenuRoleInfo> roleList(RoleListArg arg, UserInfo userInfo);

    CreateMenuResult createMenu(UserInfo userInfo, CreateMenuArg arg, ClientInfo clientInfo);

    DisableMenuResult disableMenu(DisableMenuArg arg, ClientInfo clientInfo, UserInfo userInfo);

    EnableMenuResult enableMenu(EnableMenuArg arg, ClientInfo clientInfo, UserInfo userInfo);

    DeleteMenuResult deleteMenu(DeleteMenuArg arg, UserInfo userInfo);

    QueryTenantConfigResult queryTenantConfig(QueryTenantConfigArg arg, UserInfo userInfo);

    OpenTenantConfigResult openTenantConfig(OpenTenantConfigArg arg, UserInfo userInfo);

    CloseTenantConfigResult closeTenantConfig(CloseTenantConfigArg arg, UserInfo userInfo);

    ValidateApiNameResult validateApiName(ValidateApiNameArg arg, UserInfo userInfo);

    Map<String, Map<String, String>> getIconPaths(GetIconPathsArg arg, UserInfo userInfo);

    List<IndexIcon> queryIconData(QueryIconDataArg arg, UserInfo userInfo);

    GetInitMenuItemsResult getInitMenuItems(UserInfo userInfo, ClientInfo clientInfo, GetInitMenuItemsArg arg);

    GetInitMenuItemsResult getInitMenuItemsWithShow(UserInfo userInfo, ClientInfo clientInfo, GetInitMenuItemsArg arg);

    GetCusPageResult getCusMenuItems(UserInfo userInfo);

    TenantUIResult getTenantUiConfig(UserInfo userInfo, ClientInfo clientInfo, TenantUIArg arg);
}