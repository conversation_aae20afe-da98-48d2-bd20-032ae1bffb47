package com.facishare.webpage.customer.permission;


import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service("licenseVersionService")
public class LicenseVersionService implements PermissionService{


    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {
        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getTenantPrivilege() != null &&
                !CollectionUtils.isEmpty(menu.getTenantPrivilege().getLicenseProductCodes())).collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);
        List<Menu> remainMenus = checkMenus.stream().filter(menu -> menu.getTenantPrivilege().getLicenseProductCodes()
                .stream().anyMatch(productCode -> arg.getLicenseVersions().contains(productCode))).collect(Collectors.toList());
        allowedMenus.addAll(remainMenus);
        FilterResult filterResult = new FilterResult();
        filterResult.setMenus(allowedMenus);
        return filterResult;
    }
}
