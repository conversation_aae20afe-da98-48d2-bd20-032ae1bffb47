package com.facishare.webpage.customer.controller.model.result.homepage;

import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/11
 */
@Data
public class GetUserHomePageLayoutResult implements Serializable {
    @SerializedName("HomePageLayout")
    private HomePageLayoutTO homePageLayout;
}
