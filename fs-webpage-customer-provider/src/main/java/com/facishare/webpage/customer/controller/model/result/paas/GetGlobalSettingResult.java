package com.facishare.webpage.customer.controller.model.result.paas;

import com.facishare.webpage.customer.model.AppGlobalSettings;
import com.facishare.webpage.customer.model.WebGlobalSettings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetGlobalSettingResult {
    /**
     * web端全局设置
     */
    private WebGlobalSettings webGlobalSettings;
    /**
     * 移动端全局设置
     */
    private AppGlobalSettings appGlobalSettings;
}
