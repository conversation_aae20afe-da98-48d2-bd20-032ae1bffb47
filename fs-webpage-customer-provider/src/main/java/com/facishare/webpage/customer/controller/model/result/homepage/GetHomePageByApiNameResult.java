package com.facishare.webpage.customer.controller.model.result.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/2/13
 */
@Data
public class GetHomePageByApiNameResult implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("homePageDataList")
    private List<HomePageData> homePageDataList = Lists.newArrayList();

    @Data
    public static class HomePageData implements Serializable{
        @JSONField(name = "M1")
        @SerializedName("layoutId")
        private String layoutId;
        @JSONField(name = "M2")
        @SerializedName("name")
        private String name;
        @JSONField(name = "M3")
        @SerializedName("isCurrentPage")
        private boolean isCurrentPage;
    }

}
