package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.portal.FindMenuByApiNameArg;
import com.facishare.webpage.customer.controller.model.arg.portal.FindObjStatusByApiNamesArg;
import com.facishare.webpage.customer.controller.model.arg.portal.FindSiteByApiNameArg;
import com.facishare.webpage.customer.controller.model.arg.portal.FindSiteBySiteIdArg;
import com.facishare.webpage.customer.controller.model.result.portal.FindMenuByApiNameResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindObjStatusByApiNamesResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindSiteResult;

/**
 * Created by zhouwr on 2024/11/6.
 */
public interface SiteViewAction {
    FindSiteResult findSiteByApiName(UserInfo userInfo, ClientInfo clientInfo, FindSiteByApiNameArg arg);

    FindSiteResult findSiteBySiteId(UserInfo userInfo, ClientInfo clientInfo, FindSiteBySiteIdArg arg);

    FindMenuByApiNameResult findMenuByApiName(UserInfo userInfo, ClientInfo clientInfo, FindMenuByApiNameArg arg);

	FindObjStatusByApiNamesResult findObjStatusByApiNames(UserInfo userInfo, ClientInfo clientInfo, OuterUserInfo outerUserInfo, FindObjStatusByApiNamesArg arg);
}
