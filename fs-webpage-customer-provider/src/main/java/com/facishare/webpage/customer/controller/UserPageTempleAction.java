package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.*;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.*;
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult;


/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9.
 */
public interface UserPageTempleAction {

    /**
     * web 端下游用户拉取所有列表，指定上游
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserWebPageResult getUserWebPage(UserInfo userInfo,
                                        OuterUserInfo outerUserInfo,
                                        ClientInfo clientInfo,
                                        GetUserWebPageArg arg);

    /**
     * app 端下游用户拉取所有列表，指定上游
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUserAppPageResult getUserAppPage(UserInfo userInfo,
                                        OuterUserInfo outerUserInfo,
                                        ClientInfo clientInfo,
                                        GetUserAppPageArg arg);

    /**
     * app 端拉取所有适用于该角色的模板，不指定上游
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetAppVendorTemplateListResult getAppVendorTemplateList(UserInfo userInfo,
                                                            ClientInfo clientInfo,
                                                            GetAppVendorTemplateListArg arg);

    /**
     * web 端拉取所有适用于该角色的模板，不指定上游
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetWebVendorTemplateListResult getWebVendorTemplateList(UserInfo userInfo,
                                                            ClientInfo clientInfo,
                                                            GetWebVendorTemplateListArg arg);

    /**
     * 获取外部人员身份
     *
     * @param userInfo
     * @param arg
     * @return
     */
    GetOutEmployeeResult getOutEmployeeList(UserInfo userInfo,
                                            GetOutEmployeeArg arg);

    /**
     * app 端，企业内用户态接口
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetInnerAppPageTemplatesResult getInnerAppPageTemplate(UserInfo userInfo,
                                                           ClientInfo clientInfo,
                                                           GetInnerAppPageTemplatesArg arg);

    /**
     * app 端下游用户拉取所有列表，指定上游
     *
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetCrossAppPageTemplatesResult getCrossAppPageTemplate(UserInfo userInfo,
                                                           OuterUserInfo outerUserInfo,
                                                           ClientInfo clientInfo,
                                                           GetCrossAppPageTemplatesArg arg);

    /**
     * web 端，企业内用户态接口
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetInnerWebPageTemplatesResult getInnerWebPageTemplates(UserInfo userInfo,
                                                            ClientInfo clientInfo,
                                                            GetInnerWebPageTemplatesArg arg);

    /**
     * 获取互联个人信息
     * 备注：临时替互联提供，需要互联正式接过去
     *
     * @param userInfo
     * @param outerUserInfo
     * @return
     */
    GetDownstreamEmployeeInfoResult getDownstreamEmployeeInfo(UserInfo userInfo,
                                                              OuterUserInfo outerUserInfo);

    /**
     * save user pageTemplate
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SaveWebUserPageTemplateResult saveWebUserPageTemplate(UserInfo userInfo,
                                                          SaveWebUserPageTemplateArg arg);

    /**
     * 删除个人级模板
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DeleteUserPageTemplateResult deleteUserPageTemplate(UserInfo userInfo,
                                                        DeleteUserPageTemplateArg arg);

    /**
     * 修改个人级页面模板的状态
     * @param userInfo
     * @param arg
     * @return
     */
    UpdateUserPageTempleStatusResult updateUserPageTempleStatus(UserInfo userInfo,
                                                        UpdateUserPageTempleStatusArg arg);

    /**
     * 校验个人级页面模板个数是否已达上限
     * @param userInfo
     * @param arg
     * @return
     */
    CheckCanAddUserPagetempletResult checkCanAddUserPageTemplet(UserInfo userInfo,
                                                                CheckCanAddUserPageTempletArg arg);

    /**
     * 查询个人级页面模板详情
     * @param userInfo
     * @param arg
     * @return
     */
    GetUserPageTemplateByIdResult getUserPageTempleById(UserInfo userInfo,
                                                                GetUserPageTemplateByIdArg arg);

    SetDefaultTemplateIdResult setDefaultTemplateId(UserInfo userInfo,
                                                    OuterUserInfo outerUserInfo,
                                                    ClientInfo clientInfo,
                                                    SetDefaultTemplateIdArg arg);

    }
