package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.api.model.Scope;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2020/1/9
 */
@Data
public class GetInnerAppPageTemplatesResult implements Serializable {

    private List<String> appPageIdList = Lists.newArrayList();
    private List<UserInnerAppPageTemplate> userInnerAppPageTemplates;
    /**
     * 是否允许该应用 使用个人级应用视图
     */
    private boolean useUserPageTempleFlag = false;
    private String defaultTemplateId;
    private String plugins;

    @Data
    @Builder
    public static class UserInnerAppPageTemplate implements Serializable {
        private String appPageId;
        private String name;

        private String pageTemplateId;
        private String description;
        private String menuId;
        /**
         * 是否支持员工编辑
         */
        private boolean canCustom;
        /**
         * 优先级
         */
        private int priorityLevel;
        /**
         * 是否恢复到租户配置
         */
        private boolean resetTenantConfig;
        /**
         * 模板类型
         * 1：个人
         * 2、租户
         * 3、系统级
         */
        private int pageTemplateType;

        private List<Scope> scopeList;

        private List<String> scopeNames;

        private String createName;

        private int creatorId;

        private Long createTime;

        private String updaterName;

        private int updaterId;

        private Long updateTime;

        private int status;

        public String fromWebPageTemplateId;

        public Boolean hasBeenSynToApp = false;
        private String sourceType;

    }

}
