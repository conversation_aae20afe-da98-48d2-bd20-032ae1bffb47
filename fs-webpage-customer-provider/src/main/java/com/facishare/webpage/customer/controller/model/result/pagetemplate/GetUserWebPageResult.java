package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.controller.model.UserWebPageTemplate;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.enterpriserelation2.result.EnterpriseInfoResult;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19.
 */
@Data
public class GetUserWebPageResult extends BaseResult {

    @JsonProperty("userWebPageTemplateList")
    private List<UserWebPageTemplate> userWebPageTemplateList = Lists.newArrayList();
    @JsonProperty("appName")
    private String appName;
    @JsonProperty("icon")
    private String icon;
    /**
     * 上游企业ei：tenantInfo
     */
    @JsonProperty("enterpriseInfo")
    private Map<Integer, EnterpriseInfoResult> enterpriseInfo;

    @JsonProperty("defaultTemplateId")
    private String defaultTemplateId;
}
