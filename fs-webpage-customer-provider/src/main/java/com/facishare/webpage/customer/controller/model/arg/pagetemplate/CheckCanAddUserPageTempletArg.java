package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
@Builder
public class CheckCanAddUserPageTempletArg implements Serializable {
    @SerializedName("appId")
    private String appId;

    @SerializedName("type")
    private String type;
}
