package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.controller.model.UserWebPageTemplate;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.enterpriserelation2.result.EnterpriseInfoResult;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2019/12/11
 */
@Data
public class GetWebVendorTemplateListResult implements Serializable {

    private List<UserWebPageTemplate> userWebPageTemplateList = Lists.newArrayList();

    private boolean hasOutUser = true;
    /**
     * 当前互联应用的名称
     */
    private String crossAppName;

    /**
     * 上游企业ei：tenantInfo
     *
     */
    private Map<Integer, EnterpriseInfoResult> enterpriseInfo;

    private String defaultTemplateId;
}
