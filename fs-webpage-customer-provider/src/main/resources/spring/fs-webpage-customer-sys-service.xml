<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- paas应用系统库 -->
    <bean id="paasAppDataService" class="com.facishare.webpage.customer.system.datasource.PaasAppDataServiceImpl"/>
    <bean id="paasAppConfig" class="com.facishare.qixin.sysdb.config.LicenseSysConfig" init-method="init">
        <property name="configName" value="fs-webpage-paas-app-system-config"/>
    </bean>

    <import resource="classpath:spring/license-client.xml"/>

    <bean id="paasAppSystemDataService" class="com.facishare.qixin.sysdb.serivce.SysDataByLicenseServiceImpl">
        <property name="licenseSysConfig" ref="paasAppConfig"/>
        <property name="dataSourceService" ref="paasAppDataService"/>
    </bean>

    <!-- 首页系统库 -->
    <bean id="homePageDataService" class="com.facishare.webpage.customer.system.datasource.HomePageDataServiceImpl"/>
    <bean id="homePageConfig" class="com.facishare.qixin.sysdb.config.SysTemplateConfig" init-method="init">
        <property name="configName" value="fs-webpage-homePage-pagetemplate"/>
        <property name="contactCharacter" value="_"/>
    </bean>
    <bean id="homePageSystemDataService" class="com.facishare.qixin.sysdb.serivce.SystemDataServiceImpl">
        <property name="sysTemplateConfig" ref="homePageConfig"/>
        <property name="dataSourceService" ref="homePageDataService"/>
    </bean>

    <!-- 应用模板系统库 -->
    <bean id="pageTemplateDataService" class="com.facishare.webpage.customer.system.datasource.PageTemplateDataServiceImpl"/>
    <bean id="appTemplateConfig" class="com.facishare.qixin.sysdb.config.SysTemplateConfig" init-method="init">
        <property name="configName" value="fs-webpage-app-pagetemplate"/>
        <property name="contactCharacter" value="_"/>
    </bean>
    <bean id="appSystemDataService" class="com.facishare.qixin.sysdb.serivce.SystemDataServiceImpl">
        <property name="sysTemplateConfig" ref="appTemplateConfig"/>
        <property name="dataSourceService" ref="pageTemplateDataService"/>
    </bean>

    <!-- 菜单系统库 -->
    <bean id="menuDataService" class="com.facishare.webpage.customer.system.datasource.MenuDataServiceImpl"/>
    <bean id="menuTemplateConfig" class="com.facishare.qixin.sysdb.config.SysTemplateConfig" init-method="init">
        <property name="configName" value="fs-webpage-menu-pagetemplate"/>
        <property name="contactCharacter" value="_"/>
    </bean>
    <bean id="menuSystemDataService" class="com.facishare.qixin.sysdb.serivce.SystemDataServiceImpl">
        <property name="sysTemplateConfig" ref="menuTemplateConfig"/>
        <property name="dataSourceService" ref="menuDataService"/>
    </bean>


</beans>