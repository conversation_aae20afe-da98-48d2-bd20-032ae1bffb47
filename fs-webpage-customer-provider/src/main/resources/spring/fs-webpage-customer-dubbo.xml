<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}"/>
    <dubbo:provider filter="tracerpc"/>

    <dubbo:protocol id="dubbo" name="dubbo" port="30001"/><!--//30001-->

    <dubbo:consumer filter="tracerpc"/>
    <dubbo:reference id="quotaService"
                     interface="com.facishare.open.app.pay.api.service.QuotaService"
                     protocol="dubbo" retries="0" timeout="15000" check="false"/>
    <dubbo:reference id="pageTemplateService"
                     interface="com.fxiaoke.api.service.PageTemplateService"
                     protocol="dubbo" retries="0" timeout="15000" check="false"/>
    <dubbo:service interface="com.facishare.webpage.customer.api.service.HomePageService" ref="homePageService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.webpage.customer.api.service.TenantPageTempleService"
                   ref="tenantPageTempleService"
                   protocol="dubbo"/>

    <dubbo:service interface="com.facishare.webpage.customer.api.service.MenusRegisterService"
                   ref="menusRegisterService"
                   protocol="dubbo"/>

    <dubbo:service interface="com.facishare.webpage.customer.api.console.ConsoleHomePageService"
                   ref="consoleHomePageService"
                   protocol="dubbo"/>

    <dubbo:service interface="com.facishare.webpage.customer.api.console.MenuService"
                   ref="menuService"
                   protocol="dubbo"/>

    <dubbo:service interface="com.facishare.webpage.customer.api.console.PageTempleService"
                   ref="pageTempleService"
                   protocol="dubbo"/>

    <dubbo:service interface="com.facishare.webpage.customer.api.service.MainChannelService"
                   ref="mainChannelService"
                   protocol="dubbo"/>


    <dubbo:reference id="todoBizService" interface="com.facishare.todo.api.service.TodoBizService"
                     protocol="dubbo" retries="0" timeout="10000" check="false"/>

    <dubbo:reference id="outerRoleService" interface="com.facishare.er.api.service.OuterRoleService" protocol="dubbo" version="1.0.0"/>



</beans>