<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="homePageService" class="com.facishare.webpage.customer.facade.HomePageServiceImpl"/>

    <bean id="menusRegisterService" class="com.facishare.webpage.customer.facade.MenusRegisterServiceImpl"/>

    <bean id="tenantPageTempleService" class="com.facishare.webpage.customer.facade.TenantPageTempleServiceImpl"/>

    <bean id="mainChannelService" class="com.facishare.webpage.customer.facade.MainChannelServiceImpl"/>

    <bean id="homePageBaseService" class="com.facishare.webpage.customer.service.impl.HomePageBaseServiceImpl"/>
    <bean id="tenantMenuService" class="com.facishare.webpage.customer.service.impl.TenantMenuServiceImpl"/>
    <bean id="userMenuService" class="com.facishare.webpage.customer.service.impl.UserMenuServiceImpl"/>
    <bean id="remoteService" class="com.facishare.webpage.customer.service.impl.RemoteServiceImpl"/>
    <bean id="userPageTempleService" class="com.facishare.webpage.customer.service.impl.UserPageTempleServiceImpl"/>
    <bean id="tenantPageTempleBaseService"
          class="com.facishare.webpage.customer.service.impl.TenantPageTempleBaseServiceImpl"/>
    <bean id="employeeConfigBaseService"
          class="com.facishare.webpage.customer.service.impl.EmployeeConfigBaseServiceImpl"/>
    <bean id="userHomePageBaseService" class="com.facishare.webpage.customer.service.impl.UserHomePageBaseServiceImpl"/>
    <bean id="employeeConfigCommonService"
          class="com.facishare.webpage.customer.common.EmployeeConfigCommonServiceImpl"/>

    <bean id="menuFilterService" class="com.facishare.webpage.customer.service.impl.MenuFilterServiceImpl"/>

    <bean id="groupService" class="com.facishare.webpage.customer.remote.impl.GroupServiceImpl"/>

    <bean id="roleService" class="com.facishare.webpage.customer.remote.impl.RoleServiceImpl"/>

    <bean id="objectService" class="com.facishare.webpage.customer.remote.impl.ObjectServiceImpl" init-method="init"/>

    <bean id="roleNameService" class="com.facishare.webpage.customer.remote.impl.RoleNameServiceImpl"/>

    <bean id="consoleHomePageService" class="com.facishare.webpage.customer.console.ConsoleHomePageServiceImpl"/>

    <bean id="menuService" class="com.facishare.webpage.customer.console.MenuServiceImpl"/>

    <bean id="pageTempleService" class="com.facishare.webpage.customer.console.PageTempleServiceImpl"/>

    <bean id="homePageCommonService" class="com.facishare.webpage.customer.service.impl.HomePageCommonServiceImpl"/>

    <bean id="mainChannleService" class="com.facishare.webpage.customer.facade.MainChannelServiceImpl"/>

    <bean id="replayMessageProcessService"
          class="com.facishare.webpage.customer.service.impl.ReplayMessageProcessImpl"/>
    <bean id="requestReplayConsumer" class="com.facishare.replay.mq.consumer.RequestReplayConsumer" init-method="init"
          destroy-method="close">
        <property name="replayMessageProcessService" ref="replayMessageProcessService"/>
    </bean>
    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <bean id="webMainChannelMetaDataService"
          class="com.facishare.webpage.customer.metadata.WebMainChannelMetaDataServiceImpl">
        <property name="webAppServices">
            <list>
                <ref bean="paaSWebAppService"/>
                <ref bean="crossWebAppService"/>
                <ref bean="innerWebAppService"/>
                <ref bean="configWebAppService"/>
            </list>

        </property>
    </bean>

</beans>