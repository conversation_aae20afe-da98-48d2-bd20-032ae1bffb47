<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">


    <!-- 业务组件 -->
    <bean id="udObjRestResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.common.resource.UdObjRestResource">
        <property name="factory" ref="qixinRestServiceProxyFactory"/>
    </bean>

    <bean id="transScenesResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.common.resource.TransScenesResource">
        <property name="factory" ref="qixinRestServiceProxyFactory"/>
    </bean>

    <bean id="newPaaSApiBusV3Resource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.common.resource.WebPagePaaSApiBusV3Resource">
        <property name="factory" ref="qixinRestServiceProxyFactory"/>
    </bean>

    <bean id="webPagePaasOrgGroupResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.webpage.customer.common.resource.WebPagePaasOrgGroupResource">
        <property name="factory" ref="qixinRestServiceProxyFactory"/>
    </bean>
</beans>