<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd
         http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd">

    <aop:aspectj-autoproxy proxy-target-class="true">
        <aop:include name="webPageServiceAspect"/>
        <aop:include name="webPageControllerAspect"/>
        <aop:include name="serviceProfiler"/>
        <aop:include name="webPageConsoleAspect"/>
        <aop:include name="webPageRestAspect"/>
    </aop:aspectj-autoproxy>

    <bean id="webPageServiceAspect" class="com.facishare.webpage.customer.aop.WebPageServiceAspect"/>
    <bean id="webPageControllerAspect" class="com.facishare.webpage.customer.aop.WebPageControllerAspect"/>
    <!--蜂眼配置-->
    <bean id="webPageServiceProfiler" class="com.facishare.webpage.customer.aop.WebPageServiceProfiler"/>
    <bean id="webPageConsoleAspect" class="com.facishare.webpage.customer.aop.WebPageConsoleAspect"/>
    <bean id="webPageRestAspect" class="com.facishare.webpage.customer.aop.WebPageRestAspect"/>
    <aop:config proxy-target-class="true">
        <aop:aspect ref="webPageServiceProfiler">
            <aop:around method="profile" pointcut="execution(* com.facishare.webpage.customer.controller.impl.*.*(..))"/>
            <aop:around method="profile" pointcut="execution(* com.facishare.webpage.customer.*.*(..))"/>
        </aop:aspect>
    </aop:config>
</beans>