<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">

    <appender name="ErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/error.log</File>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId}%msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                groovy,
                org.apache.rocketmq,
                org.groovy,
                io.netty,
                }%n
            </pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>15</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <appender name="AccessLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/access-aop.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/access-aop.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="DubboLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/dubbo.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/dubbo.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="CustomerLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/customer.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/customer.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="RequestLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/request.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/request.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 压力测试日志，已废弃 -->
    <!--<appender name="ReplayLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/replay.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/replay.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>3</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>-->

    <appender name="PerfLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/perf.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/perf.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="ConsoleLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/console.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/console.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="RestLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/rest.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/rest.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>7</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- 异步输出日志避免阻塞服务-->
    <appender name="AsyncAccessLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="AccessLog"/>
    </appender>
    <appender name="AsyncCustomerLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="CustomerLog"/>
    </appender>
    <appender name="AsyncRequestLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="RequestLog"/>
    </appender>
    <appender name="AsyncDubboLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>1000</queueSize>
        <appender-ref ref="DubboLog"/>
    </appender>
    <appender name="AsyncRestLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="RestLog"/>
    </appender>
    <appender name="AsyncPerfLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>1000</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="PerfLog"/>
    </appender>
    <appender name="AsyncConsoleLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>1000</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="ConsoleLog"/>
    </appender>
    <appender name="AsyncErrorLog" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>2048</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="ErrorLog"/>
    </appender>

    <logger name="org.perf4j.TimingLogger" level="INFO" additivity="false">
        <appender-ref ref="AsyncPerfLog"/>
    </logger>

    <logger name="com.facishare.qixin.common.monitor.SlowLog" level="INFO" additivity="false">
        <appender-ref ref="AsyncPerfLog"/>
    </logger>

    <!-- 压力测试日志，已废弃 -->
    <!--<logger name="com.facishare.webpage.customer.service.impl.ReplayMessageProcessImpl" level="INFO" additivity="false">
        <appender-ref ref="ReplayLog"/>
    </logger>-->

    <logger name="com.facishare.fcp.util.StopWatch" level="INFO" additivity="false">
        <appender-ref ref="AsyncPerfLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer.aop.WebPageServiceAspect" level="INFO" additivity="false">
        <appender-ref ref="AsyncDubboLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer.aop.WebPageControllerAspect" level="INFO" additivity="false">
        <appender-ref ref="AsyncAccessLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer.aop.WebPageConsoleAspect" level="INFO" additivity="false">
        <appender-ref ref="AsyncConsoleLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer.aop.WebPageRestAspect" level="INFO" additivity="false">
        <appender-ref ref="AsyncRestLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer" level="INFO" additivity="false">
        <appender-ref ref="AsyncCustomerLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <logger name="com.facishare.webpage.customer.aop.RequestFilter" level="INFO" additivity="false">
        <appender-ref ref="AsyncRequestLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="AsyncCustomerLog"/>
        <appender-ref ref="AsyncErrorLog"/>
    </root>
</configuration>