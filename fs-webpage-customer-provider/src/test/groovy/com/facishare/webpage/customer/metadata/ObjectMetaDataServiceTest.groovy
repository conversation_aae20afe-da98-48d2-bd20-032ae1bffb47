package com.facishare.webpage.customer.metadata

import com.facishare.converter.EIEAConverter
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.Constant
import com.facishare.webpage.customer.api.model.DataSourceEnv
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription
import com.facishare.webpage.customer.common.CheckService
import com.facishare.webpage.customer.config.AppMenuConfig
import com.facishare.webpage.customer.constant.MenuType
import com.facishare.webpage.customer.core.config.ObjectConfig
import com.facishare.webpage.customer.core.model.MenuCollectionType
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.facishare.webpage.customer.metadata.model.ObjectMenuData
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg
import com.facishare.webpage.customer.remote.ObjectService
import com.fxiaoke.enterpriserelation2.common.HeaderObj
import com.fxiaoke.enterpriserelation2.result.AppAssociationObjectResult
import com.fxiaoke.enterpriserelation2.service.AppDataRoleService
import spock.lang.Specification

class ObjectMetaDataServiceTest extends Specification {
    ObjectMetaDataService objectMetaDataService
    ObjectConfig objectConfig
    ObjectService objectService
    AppMenuConfig appMenuConfig
    MetaMenuFilterService metaMenuFilterService
    CheckService checkService
    AppDataRoleService appDataRoleService
    EIEAConverter eieaConverter

    def setupSpec() {
        // 使用元编程模拟静态方法
        GroovySystem.metaClassRegistry.removeMetaClass(WebPageGraySwitch)
        WebPageGraySwitch.metaClass.static.isAllowWebMainChannelMenuByBusiness = { String appId, int enterpriseId ->
            return true
        }
        WebPageGraySwitch.metaClass.static.isAllowCloseMenuByBusiness = { String appId, int enterpriseId ->
            return false
        }
        WebPageGraySwitch.metaClass.static.isAllowForMenuWidget = { String apiName, int tenantId, int employeeId ->
            return true
        }
    }

    def setup() {
        objectConfig = Mock(ObjectConfig)
        objectService = Mock(ObjectService)
        appMenuConfig = Mock(AppMenuConfig)
        metaMenuFilterService = Mock(MetaMenuFilterService)
        checkService = Mock(CheckService)
        appDataRoleService = Mock(AppDataRoleService)
        eieaConverter = Mock(EIEAConverter)

        objectMetaDataService = new ObjectMetaDataService()
        objectMetaDataService.objectConfig = objectConfig
        objectMetaDataService.objectService = objectService
        objectMetaDataService.appMenuConfig = appMenuConfig
        objectMetaDataService.metaMenuFilterService = metaMenuFilterService
        objectMetaDataService.checkService = checkService
        objectMetaDataService.appDataRoleService = appDataRoleService
        objectMetaDataService.eieaConverter = eieaConverter
    }

    def "queryMetaData_正常情况_返回元数据列表"() {
        given: "准备测试数据"
        def tenantId = 1
        def appId = "testApp"
        def locale = Locale.CHINESE
        def dataSourceEnv = DataSourceEnv.INNER
        def arg = QueryMetaDataArg.builder()
                .tenantId(tenantId)
                .appId(appId)
                .dataSourceEnv(dataSourceEnv)
                .locale(locale)
                .menuType(MenuCollectionType.ALL_TYPE)
                .build()

        def simpObjectDescription = new SimpObjectDescription(
                apiName: "testObject",
                objectType: MenuType.PRE_OBJ
        )
        def objectDescriptions = [simpObjectDescription]

        and: "配置Mock行为"
        objectService.getAllDescribe(tenantId, locale) >> objectDescriptions
        objectConfig.showApiNames() >> ["testObject"]
        objectConfig.getGrayUrlByApiName(_) >> null
        objectConfig.getIconIndexByApiName(_, _) >> 1
        objectConfig.isHiddenByApiName(_) >> false
        objectConfig.getShowDeviceTypes(_) >> []
        appMenuConfig.isTempleTenant(tenantId, appId) >> false
        appMenuConfig.getGrayMenuApiNamesByAppId(appId) >> []
        metaMenuFilterService.filterNoSupportCrmMenu(dataSourceEnv, tenantId, appId, objectDescriptions, Constant.LINK_APP_TYPE_PRE) >> objectDescriptions

        when: "调用queryMetaData方法"
        def result = objectMetaDataService.queryMetaData(arg)

        then: "验证结果"
        result != null
        result.metaMenuDataList.size() == 1
        with(result.metaMenuDataList[0]) { menuData ->
            assert menuData instanceof ObjectMenuData
            assert menuData.simpObjectDescription.apiName == "testObject"
            assert !menuData.hidden
        }
    }

    def "getCustomerLinkAppObjects_正常情况_返回关联对象列表"() {
        given: "准备测试数据"
        def appId = "testApp"
        def customerLinkAppId = "linkApp"
        def locale = Locale.CHINESE
        def dataSourceEnv = DataSourceEnv.INNER
        def type = "type1"

        def associationObject = new AppAssociationObjectResult()
        associationObject.objectApiName = "testObject"

        def simpObjectDescription = new SimpObjectDescription(
                apiName: "testObject",
                objectType: MenuType.PRE_OBJ
        )

        and: "配置Mock行为"
        eieaConverter.enterpriseIdToAccount(_) >> "EA1"

        // 创建Mock的Result对象
        def restResult = new com.fxiaoke.enterpriserelation2.common.RestResult<AppAssociationObjectResult>()
        restResult.data = [new AppAssociationObjectResult()]
        def listAppAssociationObjects = restResult

        // Mock HeaderObj的静态方法
        GroovySystem.metaClassRegistry.removeMetaClass(HeaderObj)
        HeaderObj.metaClass.static.newInstance = { int tenantId ->
            def header = Mock(HeaderObj)
            header.getProperty("x-fs-ei") >> tenantId.toString()
            return header
        }

        appDataRoleService.listAppAssociationObjects(_, _) >> listAppAssociationObjects

        objectService.getAllDescribe(_, _) >> [simpObjectDescription]
        objectConfig.showApiNames() >> ["testObject"]
        objectConfig.getGrayUrlByApiName(_) >> null
        objectConfig.getIconIndexByApiName(_, _) >> 1
        objectConfig.isHiddenByApiName(_) >> false
        objectConfig.getShowDeviceTypes(_) >> []
        appMenuConfig.isTempleTenant(_, _) >> false
        appMenuConfig.getGrayMenuApiNamesByAppId(customerLinkAppId) >> []
        metaMenuFilterService.filterNoSupportCrmMenu(_, _, _, _, _) >> [simpObjectDescription]

        when: "调用getCustomerLinkAppObjects方法"
        def result = objectMetaDataService.getCustomerLinkAppObjects(dataSourceEnv, 1, appId, type, locale, customerLinkAppId)

        then: "验证结果"
        result != null
        result.size() == 1
        with(result[0]) { menuData ->
            assert menuData instanceof ObjectMenuData
            assert menuData.simpObjectDescription.apiName == "testObject"
            assert !menuData.hidden
        }
    }

    def "filterMetaMenuByGray_正常情况_正确过滤灰度菜单"() {
        given: "准备测试数据"
        def tenantId = 1
        def appId = BizType.CRM.getValue()
        def metaMenuData1 = new ObjectMenuData()
        metaMenuData1.simpObjectDescription = new SimpObjectDescription(apiName: "menu1")
        def metaMenuData2 = new ObjectMenuData()
        metaMenuData2.simpObjectDescription = new SimpObjectDescription(apiName: "menu2")
        def metaMenuDataList = [metaMenuData1, metaMenuData2]

        and: "配置Mock行为"
        checkService.checkGoNewCRM(tenantId) >> false
        appMenuConfig.getGrayMenuApiNamesByAppId(appId) >> ["menu2"]
        checkService.checkGrayBusinessSwitch("menu2", tenantId) >> true

        when: "调用filterMetaMenuByGray方法"
        def result = objectMetaDataService.filterMetaMenuByGray(tenantId, appId, metaMenuDataList, false)

        then: "验证结果"
        result != null
        result.size() == 2
        result.collect { it.apiName }.containsAll(["menu1", "menu2"])
    }
}