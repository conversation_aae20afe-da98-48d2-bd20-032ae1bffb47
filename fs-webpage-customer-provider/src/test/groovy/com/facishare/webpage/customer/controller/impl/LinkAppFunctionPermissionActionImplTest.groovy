package com.facishare.webpage.customer.controller.impl

import com.facishare.webpage.customer.api.model.LinkAppObjectAssociationVO
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.facishare.webpage.customer.service.LinkAppObjectAssociationService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class LinkAppFunctionPermissionActionImplTest extends Specification {
    def linkAppFunctionPermissionAction = new LinkAppFunctionPermissionActionImpl()
    def linkAppObjectAssociationService = Mock(LinkAppObjectAssociationService)
    def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        Whitebox.setInternalState(linkAppFunctionPermissionAction, "linkAppObjectAssociationService", linkAppObjectAssociationService)
        // 设置fsGrayReleaseBiz到gray字段
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleObjectDescriptionFill方法在灰度开启时的行为
     */
    def "handleObjectDescriptionFillTestGrayEnabled"() {
        given:
        def enterpriseId = 1
        def associations = [new LinkAppObjectAssociationVO()]
        fsGrayReleaseBiz.isAllow(WebPageGraySwitch.CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT, enterpriseId.toString()) >> true

        when:
        linkAppFunctionPermissionAction.handleObjectDescriptionFill(enterpriseId, associations)

        then:
        1 * linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)
        0 * linkAppObjectAssociationService.fillObjectDescribe(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleObjectDescriptionFill方法在灰度关闭时的行为
     */
    def "handleObjectDescriptionFillTestGrayDisabled"() {
        given:
        def enterpriseId = 1
        def associations = [new LinkAppObjectAssociationVO()]
        fsGrayReleaseBiz.isAllow(WebPageGraySwitch.CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT, enterpriseId.toString()) >> false

        when:
        linkAppFunctionPermissionAction.handleObjectDescriptionFill(enterpriseId, associations)

        then:
        0 * linkAppObjectAssociationService.fillObjectMetadataForAssociations(_, _)
        1 * linkAppObjectAssociationService.fillObjectDescribe(enterpriseId, associations)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleObjectDescriptionFill方法处理空列表场景
     */
    def "handleObjectDescriptionFillTestEmptyList"() {
        given:
        def enterpriseId = 1
        def associations = []
        fsGrayReleaseBiz.isAllow(WebPageGraySwitch.CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT, enterpriseId.toString()) >> true

        when:
        linkAppFunctionPermissionAction.handleObjectDescriptionFill(enterpriseId, associations)

        then:
        1 * linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)
        0 * linkAppObjectAssociationService.fillObjectDescribe(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleObjectDescriptionFill方法处理空列表场景（灰度关闭）
     */
    def "handleObjectDescriptionFillTestEmptyListGrayDisabled"() {
        given:
        def enterpriseId = 1
        def associations = []
        fsGrayReleaseBiz.isAllow(WebPageGraySwitch.CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT, enterpriseId.toString()) >> false

        when:
        linkAppFunctionPermissionAction.handleObjectDescriptionFill(enterpriseId, associations)

        then:
        0 * linkAppObjectAssociationService.fillObjectMetadataForAssociations(_, _)
        1 * linkAppObjectAssociationService.fillObjectDescribe(enterpriseId, associations)
    }
} 