package com.facishare.webpage.customer.system.impl

import com.facishare.qixin.sysdb.filter.Filter
import com.facishare.qixin.sysdb.serivce.SystemDataService
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.Status
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity
import com.facishare.webpage.customer.util.TempleIdUtil
import org.mongodb.morphia.query.Query
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class PageSystemServiceImplTest extends Specification {

    def pageSystemService = new PageSystemServiceImpl()
    def systemDataService = Mock(SystemDataService)

    def setup() {
        Whitebox.setInternalState(pageSystemService, "homePageSystemDataService", systemDataService)
    }

    @Unroll
    def "test batchQueryPreCustomPageList with #scenario"() {
        given: "准备测试数据"
        def tenantId = 123
        def mockEntities = []
        if (entities) {
            mockEntities = entities.collect { appId ->
                def entity = new HomePageLayoutEntity()
                entity.setAppId(appId)
                entity.setAppTemplateId("template_${appId}")
                return entity
            }
        }

        and: "设置Mock行为"
        systemDataService.queryDataList(_, _, _) >> mockEntities

        when: "调用测试方法"
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList as Set)

        then: "验证结果"
        result.size() == expectedSize
        if (expectedSize > 0) {
            result.every { it.appId in appIdList }
        }

        and: "验证调用"
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), { Filter filter ->
            filter != null
        })

        where:
        scenario      | appIdList                    | entities                     | expectedSize
        "空列表"      | []                          | []                          | 0
        "单个AppId"   | ["app1"]                    | ["app1"]                    | 1
        "多个AppId"   | ["app1", "app2"]            | ["app1", "app2"]            | 2
        "部分匹配"    | ["app1", "app2"]            | ["app1"]                    | 1
        "无匹配"      | ["app1"]                    | ["app2"]                    | 0
        "null列表"    | null                        | []                          | 0
    }

    def "test batchQueryPreCustomPageList with template id processing"() {
        given: "准备测试数据"
        def tenantId = 123
        def appId = "testApp"
        def templateId = "originalTemplate"
        def entity = new HomePageLayoutEntity()
        entity.setAppId(appId)
        entity.setAppTemplateId(templateId)

        and: "设置Mock行为"
        systemDataService.queryDataList(_, _, _) >> { args ->
            def filter = args[2] as Filter
            filter.afterPreset(entity)
            [entity]
        }

        when: "调用测试方法"
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, [appId] as Set)

        then: "验证结果"
        result.size() == 1
        result[0].appTemplateId == TempleIdUtil.replaceId(tenantId, templateId, TempleIdUtil.SEPARATOR) ?: ""
    }

    def "test batchQueryPreCustomPageList with exception handling"() {
        given: "准备测试数据"
        def tenantId = 123
        def appIdList = ["app1"] as Set

        and: "设置Mock异常"
        systemDataService.queryDataList(_, _, _) >> { throw new RuntimeException("Test exception") }

        when: "调用测试方法"
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then: "验证异常处理"
        noExceptionThrown()
        result == []
    }

    def "test batchQueryPreCustomPageList filter conditions"() {
        given: "准备测试数据"
        def tenantId = 123
        def appIdList = ["app1"] as Set
        def queryMap = [:]

        and: "设置Mock行为"
        systemDataService.queryDataList(_, _, _) >> []

        when: "调用测试方法"
        pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then: "验证查询条件"
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), { Filter filter ->
            def query = Mock(Query) {
                filter(_) >> { args -> 
                    queryMap[args[0]] = args[1]
                    query
                }
                field(_) >> { args ->
                    [in: { value -> queryMap["${args[0]}.\$in"] = value; query },
                     notEqual: { value -> queryMap["${args[0]}.\$ne"] = value; query }] as Query
                }
            }
            
            filter.addFilter(query)
            
            assert queryMap["appType"] == BizType.PRE_CUSTOMER.getType()
            assert queryMap["apiName.\$in"] == appIdList.toList()
            assert queryMap["status.\$ne"] == Status.TEMPORARY
            true
        })
    }

    def "test batchQueryPreCustomPageList with empty appIdList"() {
        given:
        def tenantId = 1
        def appIdList = [] as Set

        when:
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then:
        result.isEmpty()
        0 * systemDataService.queryDataList(_, _, _)
    }

    def "test batchQueryPreCustomPageList with single appId"() {
        given:
        def tenantId = 1
        def appIdList = ["app1"] as Set
        def entity = new HomePageLayoutEntity(appId: "app1", layoutId: "layout1", status: Status.FORMAL)

        when:
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then:
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), _) >> [entity]
        result.size() == 1
        result[0].appId == "app1"
        result[0].layoutId == "layout1"
    }

    def "test batchQueryPreCustomPageList with multiple entities for same appId"() {
        given:
        def tenantId = 1
        def appIdList = ["app1"] as Set
        def entity1 = new HomePageLayoutEntity(appId: "app1", layoutId: "layout1", status: Status.FORMAL)
        def entity2 = new HomePageLayoutEntity(appId: "app1", layoutId: "layout2", status: Status.FORMAL)

        when:
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then:
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), _) >> [entity1, entity2]
        result.size() == 1
        result[0].appId == "app1"
        result[0].layoutId == "layout1" // 应该只返回第一个实体
    }

    def "test batchQueryPreCustomPageList with multiple appIds"() {
        given:
        def tenantId = 1
        def appIdList = ["app1", "app2", "app3"] as Set
        def entities = [
            new HomePageLayoutEntity(appId: "app1", layoutId: "layout1", status: Status.FORMAL),
            new HomePageLayoutEntity(appId: "app2", layoutId: "layout2", status: Status.FORMAL),
            new HomePageLayoutEntity(appId: "app2", layoutId: "layout3", status: Status.FORMAL),
            new HomePageLayoutEntity(appId: "app3", layoutId: "layout4", status: Status.FORMAL)
        ]

        when:
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then:
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), _) >> entities
        result.size() == 3
        result.collect { it.appId } == ["app1", "app2", "app3"] // 验证顺序与appIdList一致
        result.find { it.appId == "app2" }.layoutId == "layout2" // app2应该只返回第一个实体
    }

    def "test batchQueryPreCustomPageList with null list values"() {
        given:
        def tenantId = 1
        def appIdList = ["app1", "app2"] as Set
        def entities = [
            new HomePageLayoutEntity(appId: "app1", layoutId: "layout1", status: Status.FORMAL)
        ]

        when:
        def result = pageSystemService.batchQueryPreCustomPageList(tenantId, appIdList)

        then:
        1 * systemDataService.queryDataList(tenantId, BizType.PRE_CUSTOMER.getValue(), _) >> entities
        result.size() == 1
        result[0].appId == "app1"
        result[0].layoutId == "layout1"
    }
} 