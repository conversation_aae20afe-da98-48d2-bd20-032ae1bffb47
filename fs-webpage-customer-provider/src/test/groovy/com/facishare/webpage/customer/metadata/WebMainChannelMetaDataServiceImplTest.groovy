package com.facishare.webpage.customer.metadata

import com.facishare.common.parallel.ParallelUtils
import com.facishare.converter.EIEAConverter
import com.facishare.paas.license.pojo.ProductVersionPojo
import com.facishare.qixin.common.monitor.SlowLog
import com.facishare.webpage.customer.api.constant.TempleType
import com.facishare.webpage.customer.api.model.PageTemplate
import com.facishare.webpage.customer.api.model.PaaSAppVO
import com.facishare.webpage.customer.config.WebMainChannelConfig
import com.facishare.webpage.customer.constant.ComponentAppType
import com.facishare.webpage.customer.core.service.I18nService
import com.facishare.webpage.customer.core.service.UIPaasLicenseService
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.facishare.webpage.customer.metadata.model.WebMainChannelMetaData
import com.facishare.webpage.customer.service.*
import com.facishare.webpage.customer.util.MainChannelUtils
import com.facishare.webpage.customer.api.utils.UiPaasParallelUtils
import com.fxiaoke.i18n.client.I18nClient
import spock.lang.Specification

import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

class WebMainChannelMetaDataServiceImplTest extends Specification {

    WebMainChannelMetaDataServiceImpl service
    WebAppService webAppService1
    WebAppService webAppService2
    List<WebAppService> webAppServices
    WebMainChannelConfig webMainChannelConfig
    RemoteService remoteService
    TenantPageTempleBaseService tenantPageTempleBaseService
    UserPageTempleService userPageTempleService
    I18nService i18nService
    UIPaasLicenseService uiPaasLicenseService
    EIEAConverter eieaConverter
    PaaSAppService paaSAppService

    // 用于模拟并行任务的内部类
    class MockParallelTask implements ParallelUtils.ParallelTask {

        List<Runnable> tasks = []

        @Override
        ParallelUtils.ParallelTask submit(Runnable runnable) {
            tasks.add(runnable)
            runnable.run() // 直接执行任务，简化测试
            return this
        }

        @Override
        boolean await(long l, TimeUnit timeUnit) throws TimeoutException {
            return false
        }

        @Override
        void run() {
        }

    }

    def setupSpec() {
        // 使用元编程模拟静态方法
        GroovySystem.metaClassRegistry.removeMetaClass(WebPageGraySwitch)
        WebPageGraySwitch.metaClass.static.isAllowWebMainChannelMenuByBusiness = { String appId, int enterpriseId ->
            return true
        }
        WebPageGraySwitch.metaClass.static.isAllowCloseMenuByBusiness = { String appId, int enterpriseId ->
            return false
        }

        // 模拟MainChannelUtils静态方法
        GroovySystem.metaClassRegistry.removeMetaClass(MainChannelUtils)
        MainChannelUtils.metaClass.static.getWebMainChannelMetaDataMap = { List<WebMainChannelMetaData> list, boolean useUniqueAppId ->
            def map = [:]
            list.each { data ->
                String key = useUniqueAppId ? "${data.upEnterpriseAccount}:${data.appId}" : data.appId
                map[key] = data
            }
            return map
        }

        MainChannelUtils.metaClass.static.getUniqueAppId = { String upEA, String appId ->
            return "${upEA}:${appId}"
        }

        // 模拟WebPageParallelUtils静态方法
        GroovySystem.metaClassRegistry.removeMetaClass(UiPaasParallelUtils)
        UiPaasParallelUtils.metaClass.static.createParallelTask = { String name ->
            return new MockParallelTask()
        }

        // 模拟I18nClient - 不使用PowerMock
        def oldMetaClass = I18nClient.metaClass
        GroovySystem.metaClassRegistry.removeMetaClass(I18nClient)
        I18nClient.metaClass = oldMetaClass
        I18nClient.metaClass.static.getAllLanguage = { -> return [] }
    }

    def setup() {
        webAppService1 = Mock(WebAppService)
        webAppService2 = Mock(WebAppService)
        webAppServices = [webAppService1, webAppService2]
        webMainChannelConfig = Mock(WebMainChannelConfig)
        remoteService = Mock(RemoteService)
        tenantPageTempleBaseService = Mock(TenantPageTempleBaseService)
        userPageTempleService = Mock(UserPageTempleService)
        i18nService = Mock(I18nService)
        uiPaasLicenseService = Mock(UIPaasLicenseService)
        eieaConverter = Mock(EIEAConverter)
        paaSAppService = Mock(PaaSAppService)

        // 创建并配置被测对象
        service = new WebMainChannelMetaDataServiceImpl()
        service.setWebAppServices(webAppServices)
        service.webMainChannelConfig = webMainChannelConfig
        service.remoteService = remoteService
        service.tenantPageTempleBaseService = tenantPageTempleBaseService
        service.userPageTempleService = userPageTempleService
        service.i18nService = i18nService
        service.uiPaasLicenseService = uiPaasLicenseService
        service.eieaConverter = eieaConverter
        service.paaSAppService = paaSAppService
    }

    def "queryTenantMainChannelMetaDataList_正常情况_返回元数据列表"() {
        given: '准备测试数据'
        def tenantId = 1
        def enterpriseAccount = 'testEA'
        def employeeId = 2
        def locale = Locale.CHINESE
        def productionVersions = [new ProductVersionPojo()]
        def metaData1 = createWebMainChannelMetaData('app1', '应用1')
        def metaData2 = createWebMainChannelMetaData('app2', '应用2')

        and: '配置Mock行为'
        webAppService1.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> [metaData1]
        webAppService2.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> [metaData2]
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> []
        webMainChannelConfig.getWebMainChannelMenus(tenantId, enterpriseAccount, productionVersions) >> []
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false

        when: '调用测试方法'
        def result = service.queryTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, productionVersions)

        then: '验证结果'
        result.size() == 2
        result.collect { it.appId }.containsAll(['app1', 'app2'])
        result.collect { it.name }.containsAll(['应用1', '应用2'])
    }

    def "queryTenantMainChannelMetaDataList_空数据_返回空列表"() {
        given: '准备测试数据'
        def tenantId = 1
        def enterpriseAccount = 'testEA'
        def employeeId = 2
        def locale = Locale.CHINESE
        def productionVersions = [new ProductVersionPojo()]

        and: '配置Mock行为'
        webAppService1.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> []
        webAppService2.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> []
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> []

        when: '调用测试方法'
        def result = service.queryTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, productionVersions)

        then: '验证返回空列表'
        result.isEmpty()
    }

    def "queryUserWebMainChannelMetaDataList_正常情况_返回元数据列表"() {
        given: '准备测试数据'
        def tenantId = 1
        def enterpriseAccount = 'testEA'
        def employeeId = 2
        def locale = Locale.CHINESE
        def stopWatch = Mock(SlowLog)
        def productionVersions = [new ProductVersionPojo()]
        def metaData1 = createWebMainChannelMetaData('app1', '应用1')
        def metaData2 = createWebMainChannelMetaData('app2', '应用2')
        metaData2.appType = ComponentAppType.paaSAppType

        and: '配置Mock行为'
        webAppService1.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch) >> [metaData1]
        webAppService2.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch) >> [metaData2]
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> []
        webMainChannelConfig.getWebMainChannelMenus(tenantId, enterpriseAccount, productionVersions) >> []
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false
        userPageTempleService.getUserPageTemplateListForMainChannel(tenantId, employeeId, TempleType.WEB) >> [
                new PageTemplate(appId: 'app2', type: TempleType.WEB)
        ]

        when: '调用测试方法'
        def result = service.queryUserWebMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch, productionVersions)

        then: '验证结果'
        result.size() == 2
        result.collect { it.appId }.containsAll(['app1', 'app2'])
    }

    def "queryUserWebMainChannelMetaDataList_PaaS应用无视图_过滤掉相关应用"() {
        given: '准备测试数据'
        def tenantId = 1
        def enterpriseAccount = 'testEA'
        def employeeId = 2
        def locale = Locale.CHINESE
        def stopWatch = Mock(SlowLog)
        def productionVersions = [new ProductVersionPojo()]
        def metaData1 = createWebMainChannelMetaData('app1', '普通应用')
        def metaData2 = createWebMainChannelMetaData('app2', 'PaaS应用')
        metaData2.appType = ComponentAppType.paaSAppType

        and: '配置Mock行为'
        webAppService1.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch) >> [metaData1]
        webAppService2.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch) >> [metaData2]
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> []
        webMainChannelConfig.getWebMainChannelMenus(tenantId, enterpriseAccount, productionVersions) >> []
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false
        // 返回空列表，表示没有启用的web视图
        userPageTempleService.getUserPageTemplateListForMainChannel(tenantId, employeeId, TempleType.WEB) >> []

        when: '调用测试方法'
        def result = service.queryUserWebMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch, productionVersions)

        then: '验证PaaS应用被过滤'
        result.size() == 1
        result[0].appId == 'app1'
    }

    def "transLanguage_多语言未启用_保持原始名称"() {
        given: '准备测试数据'
        def tenantId = 1
        def locale = Locale.CHINESE
        def metaData = createWebMainChannelMetaData('app1', '应用1')
        def originalName = metaData.name
        def metaDataList = [metaData]

        and: '配置多语言功能未启用'
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> false

        when: '调用私有方法进行多语言处理'
        service.transLanguage(metaDataList, tenantId, locale)

        then: '验证名称未变化'
        metaDataList[0].name == originalName
        0 * i18nService.getTransValueIncludePreKey(*_) // 验证翻译方法未被调用
    }

    def "transLanguage_多语言启用_正确翻译应用名称和描述"() {
        given: '准备测试数据'
        def tenantId = 1
        def locale = Locale.CHINESE
        def normalApp = createWebMainChannelMetaData('app1', '应用1')
        normalApp.description = '描述1'
        normalApp.upEnterpriseAccount = ''  // 使用空字符串表示非链接应用
        def linkApp = createWebMainChannelMetaData('app2', '应用2')
        linkApp.description = '描述2'
        linkApp.upEnterpriseAccount = 'upEA'  // 链接应用
        def metaDataList = [normalApp, linkApp]

        and: '配置多语言功能启用'
        uiPaasLicenseService.existMultiLanguageModule(tenantId) >> true

        and: '配置翻译返回值'
        def transValue = [
            'appName.openApp1': '翻译后应用1',
            'appIntroduce.openApp1': '翻译后描述1'
        ]

        and: '配置PaaS应用信息'
        def paasAppVO = PaaSAppVO.builder()
            .appId('app2')
            .name('翻译后应用2')
            .description('翻译后描述2')
            .icon('newIcon')
            .build()

        when: '调用transLanguage方法'
        service.transLanguage(metaDataList, tenantId, locale)

        then: '验证调用和返回'
        1 * i18nService.getTransValueIncludePreKey(tenantId, _, locale.toLanguageTag()) >> { args ->
            def transArgs = args[1]
            // 验证传入的翻译参数
            assert transArgs.size() == 2
            assert transArgs.every { arg -> 
                arg.name in ['应用1', '描述1'] && 
                arg.customKey in ['appName.openApp1', 'appIntroduce.openApp1']
            }
            return transValue
        }

        and: '验证企业账号转换和PaaS应用信息获取'
        1 * eieaConverter.enterpriseAccountToId(['upEA']) >> ['upEA': 2]
        1 * paaSAppService.getLinkAppVOList({ it.tenantId == 2 && it.linkAppIds == ['app2'] }, locale) >> [paasAppVO]

        and: '验证翻译结果'
        with(metaDataList[0]) { app ->
            assert app.name == '翻译后应用1'
            assert app.description == '翻译后描述1'
        }
        with(metaDataList[1]) { app ->
            assert app.name == '翻译后应用2'
            assert app.description == '翻译后描述2'
            assert app.icon == 'newIcon'
            assert app.showOwnerIconFlag == true
        }
    }

    def "filterWebMainChannelMetaDataList_灰度菜单_正确过滤"() {
        given: '准备测试数据'
        def enterpriseId = 1
        def normalMetaData = createWebMainChannelMetaData('normalApp', '普通应用')
        def grayMetaData = createWebMainChannelMetaData('grayApp', '灰度应用')
        def metaDataList = [normalMetaData, grayMetaData]

        and: '配置灰度菜单和模拟灰度开关'
        webMainChannelConfig.getGrayMenus() >> ['grayApp']
        webMainChannelConfig.getCloseMenus() >> []

        when: '调用filterWebMainChannelMetaDataList方法'
        def result = service.filterWebMainChannelMetaDataList(enterpriseId, metaDataList)

        then: '验证灰度应用被正确处理'
        result.size() == 2
        result.collect { it.appId }.containsAll(['normalApp', 'grayApp'])
    }

    def "filterWebMainChannelMetaDataList_关闭菜单_正确过滤"() {
        given: '准备测试数据'
        def enterpriseId = 1
        def normalMetaData = createWebMainChannelMetaData('normalApp', '普通应用')
        def closedMetaData = createWebMainChannelMetaData('closedApp', '关闭应用')
        def metaDataList = [normalMetaData, closedMetaData]

        and: '配置关闭菜单和模拟开关'
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> ['closedApp']

        when: '调用filterWebMainChannelMetaDataList方法'
        def result = service.filterWebMainChannelMetaDataList(enterpriseId, metaDataList)

        then: '验证关闭的应用被过滤掉'
        result.size() == 1
        result[0].appId == 'normalApp'
    }

    def "sortAppMetaDataList_混合应用类型_企业内应用排在前面"() {
        given: '准备测试数据'
        def innerApp = createWebMainChannelMetaData('innerApp', '企业内应用')
        def crossApp = createWebMainChannelMetaData('crossApp', '互联应用')
        crossApp.crossName = '跨企业名称'
        def metaDataList = [crossApp, innerApp]

        when: '调用sortAppMetaDataList方法'
        def result = service.sortAppMetaDataList(metaDataList)

        then: '验证排序结果，企业内应用在前'
        result.size() == 2
        result[0].appId == 'innerApp'
        result[1].appId == 'crossApp'
    }

    def "异常情况_获取元数据失败_返回空列表"() {
        given: '准备测试数据'
        def tenantId = 1
        def enterpriseAccount = 'testEA'
        def employeeId = 2
        def locale = Locale.CHINESE
        def productionVersions = [new ProductVersionPojo()]

        and: '模拟异常'
        webAppService1.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> { throw new RuntimeException('测试异常') }
        webAppService2.getTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale) >> []
        webMainChannelConfig.getGrayMenus() >> []
        webMainChannelConfig.getCloseMenus() >> []

        when: '调用测试方法'
        def result = service.queryTenantMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, productionVersions)

        then: '验证返回空列表'
        result.isEmpty()
    }

    private WebMainChannelMetaData createWebMainChannelMetaData(String appId, String name) {
        return WebMainChannelMetaData.builder()
                .appId(appId)
                .name(name)
                .upEnterpriseAccount('testEA')
                .appType(1)
                .openAppId('openApp' + appId.replaceAll('\\D+', ''))
                .build()
    }

}
