package com.facishare.webpage.customer.controller.impl

import com.facishare.qixin.converter.QXEIEAConverter
import com.facishare.webpage.customer.api.model.UserPageTemplate
import com.facishare.webpage.customer.controller.UserPageTempleAction
import com.facishare.webpage.customer.controller.model.UserAppTemplateInfos
import com.fxiaoke.api.model.BatchGetPageInfo
import com.fxiaoke.api.model.PageInfo
import com.fxiaoke.api.service.PageTemplateService
import com.fxiaoke.enterpriserelation2.common.HeaderObj
import com.fxiaoke.enterpriserelation2.result.EnterpriseInfoResult
import com.fxiaoke.enterpriserelation2.service.EnterpriseMetaDataService
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class UserPageTempleActionImplTest extends Specification {
    UserPageTempleAction userPageTempleAction
    PageTemplateService pageTemplateService = Mock(PageTemplateService)
    EnterpriseMetaDataService enterpriseMetaDataService = Mock(EnterpriseMetaDataService)
    QXEIEAConverter qxEIEAConverter = Mock(QXEIEAConverter)

    def setup() {
        userPageTempleAction = new UserPageTempleActionImpl(pageTemplateService: pageTemplateService, enterpriseMetaDataService: enterpriseMetaDataService
                , qxEIEAConverter: qxEIEAConverter)
    }


    def "test buildUserAppPageTemplate"() {
        given:
        List<UserPageTemplate> userPageTemples = []
        UserPageTemplate userPageTemplate = new UserPageTemplate()
        userPageTemplate.setUpTenantId(74255)
        userPageTemples << userPageTemplate
        and:
        qxEIEAConverter.enterpriseIdToEa(_) >> "74255"
        and:
        BatchGetPageInfo.Result batchGetPageInfoResult = new BatchGetPageInfo.Result()
        List<PageInfo> pageInfos = []
        PageInfo pageInfo = new PageInfo()
        pageInfo.setSourceId("id")
        pageInfo.setHeader("header")
        pageInfos << pageInfo
        batchGetPageInfoResult.setPageInfos(pageInfos)
        pageTemplateService.batchPageInfo(_) >> batchGetPageInfoResult
        and:
        EnterpriseInfoResult enterpriseInfoResult = new EnterpriseInfoResult()
        enterpriseInfoResult.setEnterpriseName("74255测试")
        com.fxiaoke.enterpriserelation2.common.RestResult<EnterpriseInfoResult> restResult = new com.fxiaoke.enterpriserelation2.common.RestResult<EnterpriseInfoResult>()
        restResult.setData(enterpriseInfoResult)
        enterpriseMetaDataService.getEnterpriseInfo(*_) >> restResult
        when:
        UserAppTemplateInfos result = Whitebox.invokeMethod(userPageTempleAction, 'buildUserAppPageTemplate', 'CRM', userPageTemples, 300013128L)
        then:
        result.getEnterpriseInfoResultMap().containsKey(74255)
        result.getEnterpriseInfoResultMap().get(74255).getEnterpriseName() == "74255测试"
    }

    def "test buildUserAppPageTemplate with multiple tenants"() {
        given: "准备多个租户的测试数据"
        List<UserPageTemplate> userPageTemples = []
        def tenantIds = [74255, 74256, 74257]
        tenantIds.each { tenantId ->
            UserPageTemplate template = new UserPageTemplate()
            template.setUpTenantId(tenantId)
            template.setTempleId("temple-${tenantId}")
            template.setTemplateName("template-${tenantId}")
            userPageTemples << template
        }

        and: "模拟EA转换"
        qxEIEAConverter.enterpriseIdToEa(_) >> { args ->
            return args[0].toString()
        }

        and: "模拟页面信息查询"
        BatchGetPageInfo.Result batchGetPageInfoResult = new BatchGetPageInfo.Result()
        List<PageInfo> pageInfos = tenantIds.collect { tenantId ->
            PageInfo pageInfo = new PageInfo()
            pageInfo.setSourceId("temple-${tenantId}")
            pageInfo.setHeader("header-${tenantId}")
            return pageInfo
        }
        batchGetPageInfoResult.setPageInfos(pageInfos)
        pageTemplateService.batchPageInfo(_) >> batchGetPageInfoResult

        and: "模拟企业信息查询"
        enterpriseMetaDataService.getEnterpriseInfo(*_) >> { args ->
            HeaderObj headerObj = args[0]
            def tenantId = headerObj.get('x-fs-ei')
            EnterpriseInfoResult enterpriseInfoResult = new EnterpriseInfoResult()
            enterpriseInfoResult.setEnterpriseName("${tenantId}测试")
            return com.fxiaoke.enterpriserelation2.common.RestResult.newSuccess(enterpriseInfoResult)
        }

        when: "调用测试方法"
        UserAppTemplateInfos result = Whitebox.invokeMethod(userPageTempleAction, 'buildUserAppPageTemplate', 'CRM', userPageTemples, 300013128L)

        then: "验证结果"
        result.enterpriseInfoResultMap.size() == 3
        tenantIds.each { tenantId ->
            assert result.enterpriseInfoResultMap.containsKey(tenantId)
            assert result.enterpriseInfoResultMap.get(tenantId).enterpriseName == "${tenantId}测试"
        }
        result.userAppPageTemplates.size() == 3
    }

    def "test buildUserAppPageTemplate with empty input"() {
        given: "准备空输入"
        List<UserPageTemplate> userPageTemples = []

        when: "调用测试方法"
        UserAppTemplateInfos result = Whitebox.invokeMethod(userPageTempleAction, 'buildUserAppPageTemplate', 'CRM', userPageTemples, 300013128L)

        then: "验证结果"
        result.userAppPageTemplates.isEmpty()
        result.enterpriseInfoResultMap == null
        0 * pageTemplateService.batchPageInfo(_)
        0 * enterpriseMetaDataService.getEnterpriseInfo(*_)
    }

    @Unroll
    def "test buildUserAppPageTemplate with enterprise name display modes"() {
        given: "准备测试数据"
        List<UserPageTemplate> userPageTemples = []
        UserPageTemplate template = new UserPageTemplate()
        template.setUpTenantId(74255)
        template.setUpTenantName("原始企业名称")
        userPageTemples << template
        userPageTemples << template

        and: "模拟EA转换"
        qxEIEAConverter.enterpriseIdToEa(_) >> "74255"

        and: "模拟页面信息查询"
        BatchGetPageInfo.Result batchGetPageInfoResult = new BatchGetPageInfo.Result()
        List<PageInfo> pageInfos = [new PageInfo(sourceId: "id", header: "header")]
        batchGetPageInfoResult.setPageInfos(pageInfos)
        pageTemplateService.batchPageInfo(_) >> batchGetPageInfoResult

        and: "模拟企业信息查询"
        enterpriseMetaDataService.getEnterpriseInfo(*_) >> { args ->
            EnterpriseInfoResult enterpriseInfoResult = new EnterpriseInfoResult()
            enterpriseInfoResult.setEnterpriseName("74255测试")
            enterpriseInfoResult.setEnterpriseNameDisplayMode(displayMode)
            enterpriseInfoResult.setMultiOrgName("多组织名称")
            return com.fxiaoke.enterpriserelation2.common.RestResult.newSuccess(enterpriseInfoResult)
        }

        when: "调用测试方法"
        UserAppTemplateInfos result = Whitebox.invokeMethod(userPageTempleAction, 'buildUserAppPageTemplate', 'CRM', userPageTemples, 300013128L)

        then: "验证结果"
        result.userAppPageTemplates.size() == 2
        def template1 = result.userAppPageTemplates[0]
        def template2 = result.userAppPageTemplates[1]
        
        template1.upTenantName == expectedName
        template1.isHiddenUpTenantName() == shouldHide
        
        // 第二个模板应该和第一个有相同的值
        template2.upTenantName == template1.upTenantName
        template2.isHiddenUpTenantName() == template1.isHiddenUpTenantName()

        where: "不同的显示模式测试数据"
        displayMode | expectedName    | shouldHide  | description
        0          | "原始企业名称"    | false      | "DISPLAY_ENTERPRISE_NAME - 显示企业名称"
        1          | "原始企业名称"    | false      | "DISPLAY_MULTI_ORG_NAME - 显示原始名称"
        2          | "多组织名称"      | false      | "NOT_DISPLAY - 显示多组织名称"
    }
}
