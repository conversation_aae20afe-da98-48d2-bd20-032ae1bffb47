package com.facishare.webpage.customer.service.impl

import com.facishare.converter.EIEAConverter
import com.facishare.enterprise.common.model.paas.SimpleRecordTypeVO
import com.facishare.webpage.customer.api.model.LinkAppObjectAssociationVO
import com.facishare.webpage.customer.api.model.ObjectSimpleVo
import com.facishare.webpage.customer.dao.LinkAppObjectAssociationDao
import com.facishare.webpage.customer.service.CrmPassCacheManager
import com.fxiaoke.crmrestapi.common.contants.PaasObjectPeopleAllocateRule
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class LinkAppObjectAssociationServiceImplTest extends Specification {
    def linkAppObjectAssociationService = new LinkAppObjectAssociationServiceImpl()
    def linkAppObjectAssociationDao = Mock(LinkAppObjectAssociationDao)
    def eieaConverter = Mock(EIEAConverter)
    def crmPassCacheManager = Mock(CrmPassCacheManager)

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        Whitebox.setInternalState(linkAppObjectAssociationService, "linkAppObjectAssociationDao", linkAppObjectAssociationDao)
        Whitebox.setInternalState(linkAppObjectAssociationService, "eieaConverter", eieaConverter)
        Whitebox.setInternalState(linkAppObjectAssociationService, "crmPassCacheManager", crmPassCacheManager)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectMetadataForAssociations方法处理空列表场景
     */
    def "fillObjectMetadataForAssociationsTestEmptyList"() {
        given:
        def enterpriseId = 1
        def associations = []

        when:
        linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)

        then:
        0 * eieaConverter.enterpriseIdToAccount(_)
        0 * crmPassCacheManager.getObjectMetadataMapByApiNames(_, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectMetadataForAssociations方法处理正常数据场景
     */
    def "fillObjectMetadataForAssociationsTestNormalCase"() {
        given:
        def enterpriseId = 1
        def ea = "EA_1"
        def objectApiName = "testObject"
        def associations = [new LinkAppObjectAssociationVO(objectApiName: objectApiName)]
        def objectSimpleVo = new ObjectSimpleVo(
                apiName: objectApiName,
                label: "Test Object",
                ownerAllocateRule: PaasObjectPeopleAllocateRule.AUTO.getRule(),
                originalDescribeApiName: ""
        )
        def recordTypes = [new SimpleRecordTypeVO(api_name: "recordType1")]

        when:
        linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)

        then:
        1 * eieaConverter.enterpriseIdToAccount(enterpriseId) >> ea
        1 * crmPassCacheManager.getObjectMetadataMapByApiNames(enterpriseId, [objectApiName]) >> [(objectApiName): objectSimpleVo]
        1 * crmPassCacheManager.listRecordTypes(ea, objectApiName) >> recordTypes

        and:
        with(associations[0]) {
            objectLabel == "Test Object"
            ownerAllocateRule == PaasObjectPeopleAllocateRule.AUTO.getRule()
            needAllocateDetailLayout
            needAllocate == true
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectMetadataForAssociations方法处理对象元数据不存在场景
     */
    def "fillObjectMetadataForAssociationsTestMetadataNotFound"() {
        given:
        def enterpriseId = 1
        def ea = "EA_1"
        def objectApiName = "testObject"
        def association = new LinkAppObjectAssociationVO()
        association.setObjectApiName(objectApiName)
        association.setOwnerAllocateRule(null)  // 显式设置为null
        def associations = [association]

        when:
        linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)

        then:
        1 * eieaConverter.enterpriseIdToAccount(enterpriseId) >> ea
        1 * crmPassCacheManager.getObjectMetadataMapByApiNames(enterpriseId, [objectApiName]) >> [:]
        0 * crmPassCacheManager.listRecordTypes(_, _)

        and:
        with(associations[0]) {
            objectLabel == null
            ownerAllocateRule == null
            needAllocate == false
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectMetadataForAssociations方法处理记录类型为空场景
     */
    def "fillObjectMetadataForAssociationsTestEmptyRecordTypes"() {
        given:
        def enterpriseId = 1
        def ea = "EA_1"
        def objectApiName = "testObject"
        def associations = [new LinkAppObjectAssociationVO(objectApiName: objectApiName)]
        def objectSimpleVo = new ObjectSimpleVo(
                apiName: objectApiName,
                label: "Test Object",
                ownerAllocateRule: PaasObjectPeopleAllocateRule.AUTO.getRule(),
                originalDescribeApiName: ""
        )

        when:
        linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)

        then:
        1 * eieaConverter.enterpriseIdToAccount(enterpriseId) >> ea
        1 * crmPassCacheManager.getObjectMetadataMapByApiNames(enterpriseId, [objectApiName]) >> [(objectApiName): objectSimpleVo]
        1 * crmPassCacheManager.listRecordTypes(ea, objectApiName) >> []

        and:
        with(associations[0]) {
            objectLabel == "Test Object"
            ownerAllocateRule == PaasObjectPeopleAllocateRule.AUTO.getRule()
            needAllocateDetailLayout
            needAllocate == false
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectMetadataForAssociations方法处理异常场景
     */
    def "fillObjectMetadataForAssociationsTestException"() {
        given:
        def enterpriseId = 1
        def ea = "EA_1"
        def objectApiName = "testObject"
        def associations = [new LinkAppObjectAssociationVO(objectApiName: objectApiName)]
        def objectSimpleVo = new ObjectSimpleVo(
                apiName: objectApiName,
                label: "Test Object",
                ownerAllocateRule: PaasObjectPeopleAllocateRule.AUTO.getRule(),
                originalDescribeApiName: ""
        )

        when:
        linkAppObjectAssociationService.fillObjectMetadataForAssociations(enterpriseId, associations)

        then:
        1 * eieaConverter.enterpriseIdToAccount(enterpriseId) >> ea
        1 * crmPassCacheManager.getObjectMetadataMapByApiNames(enterpriseId, [objectApiName]) >> [(objectApiName): objectSimpleVo]
        1 * crmPassCacheManager.listRecordTypes(ea, objectApiName) >> { throw new RuntimeException("Test exception") }

        and:
        with(associations[0]) {
            objectLabel == "Test Object"
            ownerAllocateRule == PaasObjectPeopleAllocateRule.AUTO.getRule()
            needAllocateDetailLayout
            needAllocate == false
        }
    }
} 