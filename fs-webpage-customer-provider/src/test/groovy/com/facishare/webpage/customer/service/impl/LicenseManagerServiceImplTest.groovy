package com.facishare.webpage.customer.service.impl

import com.facishare.converter.EIEAConverter
import com.facishare.paas.license.Result.CheckValidityResult
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.CheckValidityPojo
import com.facishare.paas.license.pojo.ProductValidityInfoPojo
import com.facishare.webpage.customer.config.DefaultTenantConfig
import com.facishare.webpage.customer.service.LicenseManagerService
import org.powermock.reflect.Whitebox
import spock.lang.Specification

class LicenseManagerServiceImplTest extends Specification {
    LicenseManagerService licenseManagerService
    EIEAConverter eieaConverter = Mock(EIEAConverter)
    LicenseClient licenseClient = Mock(LicenseClient)

    def setup() {
        licenseManagerService = new LicenseManagerServiceImpl(eieaConverter: eieaConverter, licenseClient: licenseClient)
    }


    def "test getLinkAppAvailableTime"() {
        given:
        Whitebox.setInternalState(DefaultTenantConfig, 'APP_ID_AND_LICENSE_MAP', ['FSAID_114910f9': 'sales_stores_base_service_app'] as Map)
        eieaConverter.enterpriseIdToAccount(74255) >> '74255'
        CheckValidityResult checkValidityResult = new CheckValidityResult()
        ProductValidityInfoPojo productValidityInfoPojo = ProductValidityInfoPojo.builder()
                .tenantId('74255')
                .startTime(*************)
                .expiredTime(System.currentTimeMillis() + expiredTime)
                .build()
        CheckValidityPojo checkValidityPojo = CheckValidityPojo.builder().validityInfoPojos([productValidityInfoPojo] as Set).build()
        checkValidityResult.setResult(checkValidityPojo)
        licenseClient.checkValidity(_) >> checkValidityResult
        when:
        result == licenseManagerService.getLinkAppAvailableTime(74255, 'FSAID_114910f9')
        then:
        noExceptionThrown()
        where:
        expiredTime   || result
        ******** * 20 || 20
        0             || 0
    }
}
