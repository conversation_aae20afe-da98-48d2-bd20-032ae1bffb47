package com.facishare.webpage.customer.dao


import com.facishare.webpage.customer.constant.AppTypeEnum
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity
import com.facishare.webpage.customer.core.util.WebPageGraySwitch
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.query.FieldEnd
import org.mongodb.morphia.query.Query
import org.mongodb.morphia.query.UpdateOperations
import org.mongodb.morphia.query.CriteriaContainer
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Subject
import org.bson.types.ObjectId

class PaaSAppDaoImplTest extends Specification {

    @Subject
    PaaSAppDaoImpl paaSAppDao

    Datastore datastore
    Query query
    FieldEnd fieldEnd
    UpdateOperations updateOperations
    FsGrayReleaseBiz fsGrayReleaseBiz
    CriteriaContainer criteriaContainer

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        datastore = Mock(Datastore)
        query = Mock(Query)
        fieldEnd = Mock(FieldEnd)
        updateOperations = Mock(UpdateOperations)
        fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        criteriaContainer = Mock(CriteriaContainer)

        paaSAppDao = new PaaSAppDaoImpl()
        Whitebox.setInternalState(paaSAppDao, "datastore", datastore)
        Whitebox.setInternalState(WebPageGraySwitch, "gray", fsGrayReleaseBiz)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景1：没有记录时创建新记录"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >> query
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [] // 关键：返回空列表表示不存在记录

        and: "设置创建新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.setOnInsert(_, _) >> updateOperations
        updateOperations.set(_, _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证创建了新记录"
        1 * datastore.findAndModify(_, _, false, true)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景2：存在tenantId为0的记录时更新租户ID和状态"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1
        def existingEntity = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 0
        )

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >>> [query, query] // 第一次是检查记录，第二次是更新
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [existingEntity]  // 返回一个tenantId为0的记录

        and: "设置更新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.set(_, _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证更新了租户ID和状态"
        1 * datastore.findAndModify(_, _, false, false)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景3：存在tenantId匹配的记录时只更新status"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1
        def existingEntity = new PaaSAppEntity(
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: tenantId  // tenantId匹配
        )

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >>> [query, query] // 第一次是检查记录，第二次是更新
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [existingEntity]  // 返回一个tenantId匹配的记录

        and: "设置更新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.set("status", _) >> updateOperations
        updateOperations.set("updateTime", _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证只更新了status"
        1 * datastore.findAndModify(_, _, false, false)
        0 * updateOperations.set("tenantId", _)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景4：存在多条记录，包括tenantId匹配的记录"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1
        def matchingEntity = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: tenantId  // tenantId匹配
        )
        def zeroTenantIdEntity = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 0  // tenantId为0
        )
        def otherEntity = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 456  // 其他tenantId
        )

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >>> [query, query] // 第一次是检查记录，第二次是更新
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [matchingEntity, zeroTenantIdEntity, otherEntity]  // 返回多条记录

        and: "设置更新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.set("status", _) >> updateOperations
        updateOperations.set("updateTime", _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证只更新了tenantId匹配的记录的status"
        1 * datastore.findAndModify(_, _, false, false)
        0 * updateOperations.set("tenantId", _)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景5：存在多条记录，但没有tenantId匹配的记录，有tenantId为0的记录"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1
        def zeroTenantIdEntity = new PaaSAppEntity(
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 0  // tenantId为0
        )
        def otherEntity = new PaaSAppEntity(
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 456  // 其他tenantId
        )

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >>> [query, query] // 第一次是检查记录，第二次是更新
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [zeroTenantIdEntity, otherEntity]  // 返回多条记录，但没有tenantId匹配的

        and: "设置更新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.set(_, _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证更新了tenantId为0的记录的tenantId和status"
        1 * datastore.findAndModify(_, _, false, false)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关打开，场景6：存在记录但没有tenantId匹配或为0的记录，创建新记录"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1
        def otherEntity1 = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 456  // 其他tenantId
        )
        def otherEntity2 = new PaaSAppEntity(
                id: new ObjectId(),
                appId: linkAppId,
                appType: AppTypeEnum.CUSTOMER_LINK_APP.getAppType(),
                tenantId: 789  // 其他tenantId
        )

        and: "设置灰度开关和Mock行为"
        fsGrayReleaseBiz.isAllow(_, _) >> true
        datastore.createQuery(PaaSAppEntity) >>> [query, query] // 第一次是检查记录，第二次是创建
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query
        query.asList() >> [otherEntity1, otherEntity2]  // 返回多条记录，但没有tenantId匹配或为0的

        and: "设置创建新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.setOnInsert(_, _) >> updateOperations
        updateOperations.set(_, _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证创建了新记录"
        1 * datastore.findAndModify(_, _, false, true)
    }

    def "测试addTenantIdForCustomerLinkApp方法 - 灰度开关关闭，使用原有逻辑"() {
        given: "准备测试数据"
        def linkAppId = "testApp"
        def tenantId = 123
        def status = 1

        and: "设置灰度开关关闭"
        fsGrayReleaseBiz.isAllow(_, _) >> false
        datastore.createQuery(PaaSAppEntity) >> query
        query.field(_) >> fieldEnd
        fieldEnd.equal(_) >> query

        and: "设置更新记录的Mock行为"
        datastore.createUpdateOperations(PaaSAppEntity) >> updateOperations
        updateOperations.set(_, _) >> updateOperations

        when: "调用测试方法"
        paaSAppDao.addTenantIdForCustomerLinkApp(linkAppId, tenantId, status)

        then: "验证使用了原有逻辑"
        1 * datastore.findAndModify(_, _, true)
    }
} 