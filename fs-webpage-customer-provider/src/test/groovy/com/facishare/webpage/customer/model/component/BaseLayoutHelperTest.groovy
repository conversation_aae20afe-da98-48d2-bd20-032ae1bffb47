package com.facishare.webpage.customer.model.component

import com.alibaba.fastjson.JSONObject
import spock.lang.Specification

class BaseLayoutHelperTest extends Specification {


    def "test getComponents"() {
        given:
        def layoutStr = "{\"layout\":[{\"components\":[[]],\"columns\":[{\"width\":\"100%\"}]},{\"components\":[[],[]],\"columns\":[{\"width\":\"34%\"},{\"width\":\"66%\"}]}],\"components\":{},\"globalSettings\":{},\"pageLayoutType\":2.0,\"labelPageName\":\"自定义页面\",\"filters\":[{\"filterData\":\"{\\\"type\\\":3,\\\"canEdit\\\":true}\",\"filterType\":\"selector\"},{\"filterData\":\"{\\\"startTime\\\":0,\\\"endTime\\\":0,\\\"dateId\\\":4,\\\"dateType\\\":\\\"本月\\\",\\\"canEdit\\\":true}\",\"filterType\":\"date\"},{\"filterData\":\"{\\\"enableEmpFilterOfGlobalFilter\\\":1,\\\"enableDateFilterOfGlobalFilter\\\":1,\\\"enablePresetEmpFilterOfGlobalFilter\\\":1,\\\"enablePresetDateFilterOfGlobalFilter\\\":1}\",\"filterType\":\"pageDefault\"}],\"labelIndex\":\"0\"}"
        when:
        def layout = new CustomerLayoutHelper(JSONObject.parseObject(layoutStr))
        def components = layout.getComponents()
        then:
        components != null
        noExceptionThrown()
    }
}
