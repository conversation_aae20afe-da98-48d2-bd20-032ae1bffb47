package com.facishare.webpage.customer.test;

import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.google.common.collect.Lists;
import lombok.Data;
import org.junit.Test;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/11/16
 */
public class TestMapStream {

    @Data
    public static class Test1 implements Serializable {
        private int id;
        private String name;
    }

    @Test
    public void testStreamMap() {
        Test1 test = new Test1();
        test.setId(2);
        test.setName("2");

        Test1 test1 = new Test1();
        test1.setId(1);
        test1.setName("1");

        Test1 test2 = new Test1();
        test2.setId(3);
        test2.setName("3");

        List<Test1> test1List = Lists.newArrayList(test, test1, test2);

        System.out.println(test1List);


        Map<Integer, Test1> test1Map = test1List.stream().collect(Collectors.toMap(Test1::getId, x -> x, (key1, key2) -> key2));
        System.out.println(test1Map);

    }
}
