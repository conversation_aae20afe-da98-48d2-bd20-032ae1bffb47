package com.facishare.webpage.customer.service

import com.facishare.webpage.customer.controller.model.arg.portal.SiteDraftInfoDTO
import com.facishare.webpage.customer.api.model.User
import com.facishare.webpage.customer.dao.SiteDraftEntityDao
import com.facishare.webpage.customer.dao.entity.SiteDraftEntity
import com.facishare.webpage.customer.service.impl.SiteDraftServiceImpl
import spock.lang.Specification

class SiteDraftServiceTest extends Specification {

    SiteDraftEntityDao siteDraftEntityDao = Mock(SiteDraftEntityDao)
    SiteService siteService = Mock(SiteService)
    SiteDraftServiceImpl siteDraftService

    def setup() {
        siteDraftService = new SiteDraftServiceImpl(siteDraftEntityDao: siteDraftEntityDao, siteService: siteService)
    }

    def "creatOrUpdateSiteDraftInfo with new draft should call save method on siteDraftEntityDao"() {
        given:

        def siteDraftInfoDTO = new SiteDraftInfoDTO(
                siteApiName: "test_api",
                appId: "test_app"
        )
        def siteDraftEntity = siteDraftInfoDTO.toEntity()
        def user = new User(1, 1)
        and:
        siteDraftEntityDao.findDraftBySiteApiName(_,_) >> null

        when:
        siteDraftService.creatOrUpdateSiteDraftInfo(user, siteDraftInfoDTO)

        then:
        1 * siteDraftEntityDao.save(_, siteDraftEntity)
        0 * siteDraftEntityDao.update(_, _)
    }

    def "creatOrUpdateSiteDraftInfo with existing draft should call update method on siteDraftEntityDao"() {
        given:
        def siteDraftInfoDTO = new SiteDraftInfoDTO(
                siteApiName: "test_api",
                appId: "test_app"
        )
        def siteDraftEntity = siteDraftInfoDTO.toEntity()
        def existingDraft = new SiteDraftEntity(
                siteApiName: "test_api",
                appId: "test_app"
        )
        def user = new User(1, 1)
        and:
        siteDraftEntityDao.findDraftBySiteApiName(_, siteDraftEntity.getSiteApiName()) >> existingDraft

        when:
        siteDraftService.creatOrUpdateSiteDraftInfo(user, siteDraftInfoDTO)

        then:
        1 * siteDraftEntityDao.update(_, siteDraftEntity)
        0 * siteDraftEntityDao.save(_, _)
    }
}
