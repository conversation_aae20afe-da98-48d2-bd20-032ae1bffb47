package com.facishare.webpage.customer.groovy

import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments
import com.facishare.organization.adapter.api.permission.model.CheckHasManageAbility
import com.facishare.paas.license.Result.LicenseVersionResult
import com.facishare.paas.license.Result.ModuleInfoResult
import com.facishare.paas.license.http.LicenseClient
import com.facishare.qixin.objgroup.common.service.PaasApiBusV3Service
import com.facishare.qixin.permission.filter.PermissionFilterManager
import com.facishare.qixin.permission.unified.service.*
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult
import com.facishare.uc.api.service.EnterpriseEditionService
import com.facishare.webpage.customer.api.model.result.CheckUserPermissionApiResult
import com.facishare.webpage.customer.api.model.result.GetPageTemplatesResult
import com.facishare.webpage.customer.api.service.TenantPageTempleService
import com.facishare.webpage.customer.metadata.factory.PermissionFactory
import com.fxiaoke.appcenter.restapi.common.BaseResult
import com.fxiaoke.appcenter.restapi.model.vo.UserCanViewListVO
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Created by zhangyu on 2020/11/28
 */
class MockPermissionUnified extends Specification {

    public BusinessPermissionImpl businessPermission

    public CustomerPagePermissionServiceImpl customerPagePermissionService

    public EnterpriseSourcePermissionServiceImpl enterpriseSourcePermissionService

    public LicenseModulePermissionImpl licenseModulePermission

    public LicenseVersionServiceImpl licenseVersionService

    public ManagerPermissionServiceImpl managerPermissionService

    public ObjPermissionServiceImpl objPermissionService

    public RolePermissionServiceImpl rolePermissionService

    public AppOpenPermissionServiceImpl appOpenPermissionService

    public PermissionUnifiedService permissionUnifiedService

    public PermissionFactory permissionFactory

    public DevicePermissionServiceImpl devicePermissionService

    def setup() {
        businessPermission = new BusinessPermissionImpl()
        businessPermission.paasApiBusV3Service = paasApiBusV3Service

        objPermissionService = new ObjPermissionServiceImpl()
        objPermissionService.permissionFilterManager = permissionFilterManager

        rolePermissionService = new RolePermissionServiceImpl()
        rolePermissionService.orgPermissionService = permissionService

        customerPagePermissionService = new CustomerPagePermissionServiceImpl()
        customerPagePermissionService.tenantPageTempleService = tenantPageTempleService

        enterpriseSourcePermissionService = new EnterpriseSourcePermissionServiceImpl()
        enterpriseSourcePermissionService.enterpriseEditionService = enterpriseEditionService

        licenseModulePermission = new LicenseModulePermissionImpl()
        licenseModulePermission.licenseClient = licenseClient

        licenseVersionService = new LicenseVersionServiceImpl()
        licenseVersionService.licenseClient = licenseClient

        managerPermissionService = new ManagerPermissionServiceImpl()
        managerPermissionService.permissionService = permissionService

        appOpenPermissionService = new AppOpenPermissionServiceImpl()
        appOpenPermissionService.checkHasPermissionService = checkHasPermissionService

        devicePermissionService = new DevicePermissionServiceImpl()

        permissionUnifiedService = new PermissionUnifiedServiceImpl()
        permissionUnifiedService.combinePermissionServiceList = Lists.newArrayList(businessPermission,
                objPermissionService,
                rolePermissionService,
                customerPagePermissionService,
                enterpriseSourcePermissionService,
                licenseModulePermission,
                licenseVersionService,
                managerPermissionService,
                appOpenPermissionService,
                devicePermissionService)
        permissionUnifiedService.init()

        permissionFactory = new PermissionFactory()
        permissionFactory.permissionUnifiedService = permissionUnifiedService
    }

    def permissionFilterManager = Mock(PermissionFilterManager.class) {
        filterFunctionPermission(_) >> buildPermissionFuncAccess()
    }

    def permissionService = Mock(com.facishare.organization.adapter.api.permission.service.PermissionService.class) {
        batchGetRoleCodesByEmployeeIds(_) >> new BatchGetRoleCodesByEmployeeIds.Result()
        checkHasAbilityForNewManagement(_) >> new CheckHasManageAbility.Result()
        def result = new CheckFunctionCodeAndGetManageDepartments.Result()
        result.setIsManageWholeCompany(true)
        result.setHasAbility(true)
        checkFunctionCodeAndGetManageDepartments(_) >> result
    }

    def paasApiBusV3Service = Mock(PaasApiBusV3Service.class) {
        queryRecordTypes(_, _, _, _, _, _) >> buildRecordTypesMap()
    }

    def checkHasPermissionService = Mock(com.facishare.qixin.permission.unified.common.CheckHasPermissionService.class) {
        hasCrmPermission(_, _,_,_) >> true
    }

    def tenantPageTempleService = Mock(TenantPageTempleService.class) {
        getPageTemplates(_) >> new GetPageTemplatesResult()
        checkUserPermission(_) >> CheckUserPermissionApiResult.success(true)
    }

    def enterpriseEditionService = Mock(EnterpriseEditionService.class) {
        getEnterpriseData(_) >> new GetEnterpriseDataResult()
    }

    def licenseClient = Mock(LicenseClient.class) {
        queryModule(_) >> new ModuleInfoResult()
        queryProductVersion(_) >> new LicenseVersionResult()
    }

    Map<String, List<String>> buildPermissionFuncAccess() {
        List<String> list = Lists.newArrayList("List", "Add")

        Map<String, List<String>> permissionFuncAccess = Maps.newHashMap()
        permissionFuncAccess.put("GoalValueObj", list)
        permissionFuncAccess.put("MarketingEventObj", list)
        permissionFuncAccess.put("GoalBoard", list)
        permissionFuncAccess.put("LeadsObj", list)
        permissionFuncAccess.put("AccountObj", list)
        permissionFuncAccess.put("test__c", list)
        permissionFuncAccess.put("test1__c", list)

        return permissionFuncAccess
    }

    Map<String, List<String>> buildRecordTypesMap() {
        Map<String, List<String>> recordTypeMap = Maps.newHashMap()
        recordTypeMap.put("AccountObj", Lists.newArrayList("default__c"))

        return recordTypeMap
    }

}
