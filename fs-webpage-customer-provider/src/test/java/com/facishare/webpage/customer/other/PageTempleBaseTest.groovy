package com.facishare.webpage.customer.other

import com.facishare.cep.plugin.model.OuterUserInfo
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.organization.api.model.employee.SimpleEmployeeDto
import com.facishare.organization.api.service.EmployeeProviderService
import com.facishare.qixin.converter.QXEIEAConverter
import com.facishare.qixin.objgroup.common.service.PaaSAppAuthService
import com.facishare.qixin.objgroup.common.service.PaasApiBusV3Service
import com.facishare.qixin.permission.filter.PermissionFilterManager
import com.facishare.uc.api.service.EnterpriseEditionService
import com.facishare.webpage.customer.api.service.HomePageService

import com.facishare.webpage.customer.api.service.TenantPageTempleService
import com.facishare.webpage.customer.dao.TenantPageTempleDao
import com.facishare.webpage.customer.dao.TenantPageTempleDaoImpl
import com.facishare.webpage.customer.dao.entity.PageTempleEntity
import com.facishare.webpage.customer.service.RemoteService
import com.facishare.webpage.customer.service.TenantPageTempleBaseService
import com.facishare.webpage.customer.service.impl.RemoteServiceImpl
import com.facishare.webpage.customer.service.impl.TenantPageTempleBaseServiceImpl
import com.fxiaoke.api.service.PageTemplateService
import com.fxiaoke.enterpriserelation2.service.AppDataRoleService
import com.github.fakemongo.junit.FongoRule
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import spock.lang.Specification

/**
 * Created by shecheng on 19/10/21.
 */
class PageTempleBaseTest extends Specification {

    public final FongoRule fongoRule = new FongoRule(false)
    public Datastore datastore;
    public UserInfo userInfo
    public OuterUserInfo outerUserInfo
    public SimpleEmployeeDto simpleEmployeeDto
    public TenantPageTempleBaseService tenantPageTempleBaseService
    public RemoteService remoteService
    public TenantPageTempleDao tenantPageTempleDao
    public PageTemplateService pageTemplateService
    public String templeId1 = "templeId001"
    public String templeId2 = "templeId002"
    public String templeName1 = "web应用首页"
    public String templeName2 = "app应用首页"
    public String description1 = "web应用首页描述"
    public String description2 = "app应用首页描述"
    public String appId = "FSAID_11490d9e"
    public String sourceId = "sourceId001"
    public String appPageId = "appPageId002"
    public String webMenuId = "webMenuId001"
    public String webPageId = "webPageId001"
    public String page_type_web = "web"
    public String page_type_app = "app"
    public long time = 1572345474000
    public String employeeName = "zhang001"


    def appDataRoleService = Mock(AppDataRoleService.class)
    def qxEIEAConverter = Mock(QXEIEAConverter.class)
    def paasApiBusV3Service = Mock(PaasApiBusV3Service.class)
    def paaSAppAuthService = Mock(PaaSAppAuthService.class)
    def employeeProviderService = Mock(EmployeeProviderService.class)
    def enterpriseEditionService = Mock(EnterpriseEditionService.class)
    def tenantPageTempleService = Mock(TenantPageTempleService.class)
    def permissionFilterManager = Mock(PermissionFilterManager.class)
    def homePageService = Mock(HomePageService.class)
    def pageTemplateService = Mock(PageTemplateService.class)

    private ExpectedException exception = ExpectedException.none()

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule)

    def setup() {
        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test-pageTemplate")

        tenantPageTempleDao = new TenantPageTempleDaoImpl()
        tenantPageTempleDao.datastore = datastore
        tenantPageTempleBaseService = new TenantPageTempleBaseServiceImpl()
        tenantPageTempleBaseService.tenantPageTempleDao = tenantPageTempleDao

        remoteService = new RemoteServiceImpl()
        remoteService.paaSAppAuthService = paaSAppAuthService
        remoteService.appDataRoleService = appDataRoleService
        remoteService.employeeProviderService = employeeProviderService
        remoteService.enterpriseEditionService = enterpriseEditionService
        remoteService.paasApiBusV3Service = paasApiBusV3Service
        remoteService.permissionFilterManager = permissionFilterManager


        createMessage()
    }

    def createMessage() {
        userInfo = new UserInfo()
        userInfo.setEnterpriseId(71568)
        userInfo.setEmployeeId(1000)
        outerUserInfo = new OuterUserInfo()
        outerUserInfo.setOutTenantId(**********)
        outerUserInfo.setOutUserId(100001000)

        simpleEmployeeDto = new SimpleEmployeeDto()
        simpleEmployeeDto.setEnterpriseId(71568)
        simpleEmployeeDto.setEmployeeId(1000)
        simpleEmployeeDto.setName(employeeName)


        def entity1 = new PageTempleEntity()
        entity1.setTempleId(templeId1)
        entity1.setTenantId(userInfo.getEnterpriseId())
        entity1.setAppId(appId)
        entity1.setStatus(0)
        entity1.setName(templeName1)
        entity1.setType(page_type_web)
        entity1.setSourceType("system")
        entity1.setScopes(Collections.emptyList())
        entity1.setSourceId(sourceId)
        entity1.setDescription(description1)
        entity1.setCreateTime(time)
        entity1.setCreatorId(userInfo.getEmployeeId())
        entity1.setUpdaterId(userInfo.getEmployeeId())
        entity1.setUpdateTime(time)
        entity1.setWebMenuId(webMenuId)
        entity1.setWebPageId(webPageId)
        def templeEntity1 = tenantPageTempleDao.findAndModify(entity1)

        def entity2 = new PageTempleEntity()
        entity2.setTempleId(templeId2)
        entity2.setTenantId(userInfo.getEnterpriseId())
        entity2.setAppId(appId)
        entity2.setStatus(0)
        entity2.setName(templeName1)
        entity2.setType(page_type_app)
        entity2.setSourceType("system")
        entity2.setScopes(Collections.emptyList())
        entity2.setSourceId(sourceId)
        entity2.setDescription(description2)
        entity2.setCreateTime(time)
        entity2.setCreatorId(userInfo.getEmployeeId())
        entity2.setUpdaterId(userInfo.getEmployeeId())
        entity2.setUpdateTime(time)
        entity2.setAppPageId(appPageId)
        def templeEntity2 = tenantPageTempleDao.findAndModify(entity2)
    }

}
