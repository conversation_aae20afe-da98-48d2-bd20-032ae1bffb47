package com.facishare.webpage.customer.groovy

import com.facishare.cep.plugin.enums.ClientTypeEnum
import com.facishare.cep.plugin.model.ClientInfo
import com.facishare.cep.plugin.model.OuterUserInfo
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.qixin.converter.QXEIEAConverter
import com.facishare.webpage.customer.api.constant.ScopeType
import com.facishare.webpage.customer.api.model.RoleInfo
import com.facishare.webpage.customer.api.model.Scope
import com.facishare.webpage.customer.api.model.core.*
import com.facishare.webpage.customer.common.LanguageService
import com.facishare.webpage.customer.common.OrganizationCommonService
import com.facishare.webpage.customer.common.OrganizationCommonServiceImpl
import com.facishare.webpage.customer.config.AppMenuConfig
import com.facishare.webpage.customer.config.ComponentConfig
import com.facishare.webpage.customer.config.WebMainChannelConfig
import com.facishare.webpage.customer.constant.MenuType
import com.facishare.webpage.customer.core.config.GroupMetaConfig
import com.facishare.webpage.customer.core.config.MenusConfig
import com.facishare.webpage.customer.core.model.GroupMetaData
import com.facishare.webpage.customer.event.WebPageEventService
import com.facishare.webpage.customer.metadata.MetaMenuService
import com.facishare.webpage.customer.metadata.NavigateMenuConfig
import com.facishare.webpage.customer.metadata.model.ConfigMenuData
import com.facishare.webpage.customer.metadata.model.MetaMenuData
import com.facishare.webpage.customer.metadata.model.ObjectMenuData
import com.facishare.webpage.customer.metadata.model.PageMenuData
import com.facishare.webpage.customer.model.NavigationMenu
import com.facishare.webpage.customer.model.PageData
import com.facishare.webpage.customer.remote.RoleNameService
import com.facishare.webpage.customer.service.*
import com.facishare.webpage.customer.util.TempleIdUtil
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import org.springframework.test.context.ContextConfiguration

import javax.annotation.Resource

/**
 * Created by zhangyu on 2020/6/30
 */
@ContextConfiguration(locations = "classpath:applicationContext-test-1.xml")
class MockResource extends MockPermissionUnified {

    public final FongoRule fongoRule = new FongoRule(false)

    public Datastore datastore

    public OrganizationCommonService scopeService

    public ExpectedException exception = ExpectedException.none()

    public int tenantId = 71574

    @Resource
    public WebPageEventService webPageEventService
    @Resource
    public ComponentConfig componentConfig
    @Resource
    public AppMenuConfig appMenuConfig
    @Resource
    public MenusConfig menusConfig
    @Resource
    public WebMainChannelConfig webMainChannelConfig


    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule)

    def navigateMenuConfig = Mock(NavigateMenuConfig.class){
        getNavigateMenuByConfig(_,_,_) >> Lists.newArrayList()
    }

    def qxEIEAConverter = Mock(QXEIEAConverter.class) {
        enterpriseAccountToId(_) >> 71574
        enterpriseIdToEa(_) >> "fsceshi003"
    }

    def searchWordsService = Mock(SearchWordsService.class){
        getSearchWords(_) >> Maps.newHashMap()
    }

    def remoteService = Mock(RemoteService.class) {
        Map<Integer, String> employeeNameMap = Maps.newHashMap()
        employeeNameMap.put(1000, "张三")
        getEmployeeName(_, _) >> employeeNameMap

        getCusComponentList(_, _) >> Lists.newArrayList()

        getRoleIdsByEmployee(_, _) >> Lists.newArrayList("00000000000000000000000000000019", "00000000000000000000000000000009")
        permissionFuncAccess(_, _, _, _, _, _) >> buildPermissionFuncAccess()
        getFunctionCode(_, _, _) >> buildPermissionFuncAccess()
        getEmployeeName(_, _) >> buildEmployeeName()
        getDepartmentsName(_, _) >> buildDepartmentName()
        getGroupsName(_, _) >> buildGroupName()
        Map<Integer, String> map = Maps.newHashMap()
        map.put(71574, "测试003企业")
        map.put(78810, "下游企业")
        getTenantAccountNames(_) >> map

    }

    def remoteCrossService = Mock(RemoteCrossService.class) {

        def linkAppResult = new SimpleLinkAppResult()
        linkAppResult.setAppId("test1")
        linkAppResult.setAppType(1)
        linkAppResult.setAppName("应用")
        linkAppResult.setIsExprired(false)
        getUpSimpleLinkApp(_) >> Lists.newArrayList(linkAppResult)

        getCrossAppMap(_, _, _, _) >> Maps.newHashMap()

        getUpTenantIds(_) >> Lists.newArrayList(71574)

        getUserOuterRoleIds(_, _, _, _) >> Lists.newArrayList("*********", "0*********")

        getOuterRoleInfosByAppId(_, _) >> buildOuterRoleInfo()

    }

    def sysTemplateConfig = Mock(com.facishare.qixin.sysdb.config.SysTemplateConfig.class) {
        getDefaultDataId(_) >> Lists.newArrayList()
        getDefaultDataId(_, _) >> Lists.newArrayList()
        getSysDataId(_) >> Lists.newArrayList()
        buildDataId(_, _) >> TempleIdUtil.guid
        getUUId(_) >> TempleIdUtil.guid
    }

    def roleNameService = Mock(RoleNameService.class) {
        getRoleName(_, _) >> buildRoleName()
    }

    def metaMenuService = Mock(MetaMenuService.class){
        getMetaMenuList(_,_,_,_) >> getMetaMenuDataList()
    }

    def languageService = Mock(LanguageService.class) {
        queryGroupLanguage(_, _, _, _, _) >> Maps.newHashMap()
        queryComponentLanguage(_, _, _) >> Maps.newHashMap()
        queryLanguageByNameI18nKeys(_, _, _) >> Maps.newHashMap();
        queryMenusLanguage(_, _, _) >> Maps.newHashMap()
    }

    def groupMetaConfig = Mock(GroupMetaConfig.class){

        def groupMetaData = new GroupMetaData()
        groupMetaData.setAppId("CRM")
        groupMetaData.setAfterType("group")
        groupMetaData.setAfterApiName("分组2")
        groupMetaData.setGroupName("分组3")
        groupMetaData.setGroupApiName("group-111")
        groupMetaData.setMenus(Lists.newArrayList("MarketingEventObj"))

        getGroupMetaDataListByAppId(_) >> Lists.newArrayList(groupMetaData)
    }

    def homePageCommonService = Mock(HomePageCommonService.class) {


        def employeeScope = new Scope()
        employeeScope.setDataId("1000")
        employeeScope.setDataType(ScopeType.Employee.type)

        def departmentScope = new Scope()
        departmentScope.setDataType(ScopeType.Department.type)
        departmentScope.setDataId("999999")

        def roleScope = new Scope()
        roleScope.setDataType(ScopeType.Role.type)
        roleScope.setDataId("00000000000000000000000000000019")

    }

    def setupSpec() {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-webpage-customerLayout")
    }

    def setup() {
        //mock数据库
        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test-menu")

        scopeService = new OrganizationCommonServiceImpl()
        scopeService.remoteService = remoteService
        scopeService.roleNameService = roleNameService
    }

    List<RoleInfo> buildOuterRoleInfo() {
        def roleInfo1 = new RoleInfo()
        roleInfo1.setId("*********")
        roleInfo1.setName("one")

        def roleInfo2 = new RoleInfo()
        roleInfo2.setId("0*********")
        roleInfo2.setName("two")

        return Lists.newArrayList(roleInfo1, roleInfo2)
    }

    Map<Integer, String> buildEmployeeName() {
        Map<Integer, String> map = Maps.newHashMap()
        map.put(1000, "张三")
        return map
    }

    Map<Integer, String> buildDepartmentName() {
        Map<Integer, String> map = Maps.newHashMap()
        map.put(999999, "全公司")
        return map
    }

    Map<String, String> buildGroupName() {
        Map<String, String> map = Maps.newHashMap()
        map.put("0001", "用户组1")
        return map
    }

    Map<String, String> buildRoleName() {
        Map<String, String> map = Maps.newHashMap()
        map.put("11111111111", "one")
        return map
    }

    //获取所有的元数据
    List<MetaMenuData> getMetaMenuDataList() {

        List<MetaMenuData> metaMenuDatas = buildMetaMenuDataFromMenus()
        metaMenuDatas.addAll(buildObjectMenuDataFromCrmMenus())
        metaMenuDatas.addAll(buildPageMenuDataFromPages())
        return metaMenuDatas
    }

    //mock非对象的元数据
    private List<MetaMenuData> buildMetaMenuDataFromMenus() {

        def icon = new Icon()
        icon.icon_1 = "111"
        icon.icon_2 = "222"

        def url = new Url()


        def menu1 = new Menu()
        menu1.name = "目标"
        menu1.id = "GoalValueObj"
        menu1.deviceTypes = Lists.newArrayList("iOS", "Android", "WEB", "Desktop_Mac", "Desktop_Windows")
        PersonPrivilege personPrivilege1 = new PersonPrivilege()
        personPrivilege1.functionCode = Lists.newArrayList("List")
        menu1.personPrivilege = personPrivilege1
        menu1.icon = icon
        menu1.url = url

        def menu2 = new Menu()
        menu2.name = "目标完成情况"
        menu2.id = "GoalBoard"
        menu2.deviceTypes = Lists.newArrayList("iOS", "Android", "WEB", "Desktop_Mac", "Desktop_Windows")
        PersonPrivilege personPrivilege2 = new PersonPrivilege()
        personPrivilege2.functionCode = Lists.newArrayList("List")
        menu2.personPrivilege = personPrivilege2
        menu2.icon = icon
        menu2.url = url

        def menu3 = new Menu()
        menu3.name = "CRM待办"
        menu3.id = "CrmToDo"
        menu3.deviceTypes = Lists.newArrayList("iOS", "Android")
        menu3.icon = icon
        menu3.url = url

        def menu4 = new Menu()
        menu4.name = "审批流程监控"
        menu4.id = "BPMMonitor"
        menu4.deviceTypes = Lists.newArrayList("WEB")
        menu4.icon = icon
        menu4.url = url

        def menu5 = new Menu()
        menu5.name = "CRM提醒"
        menu5.id = "CrmRemind"
        menu5.deviceTypes = Lists.newArrayList("iOS", "Android")
        menu5.icon = icon
        menu5.url = url

        List<Menu> menus = Lists.newArrayList(menu1, menu2, menu3, menu4, menu5)

        List<MetaMenuData> metaMenuDataList = Lists.newArrayList();

        for (Menu menu : menus) {
            def configMenuData = new ConfigMenuData()
            configMenuData.setMenu(menu);
            metaMenuDataList.add(configMenuData)
        }
        return metaMenuDataList

    }

    //mock对象的元数据（包含追加的自定义对象）
    private List<MetaMenuData> buildObjectMenuDataFromCrmMenus() {

        def icon = new Icon()
        icon.icon_1 = "111"
        icon.icon_2 = "222"

        SimpObjectDescription crmMenu1 = new SimpObjectDescription()
        crmMenu1.apiName = "MarketingEventObj"
        crmMenu1.name = "市场活动"
        crmMenu1.setActive(true)
        crmMenu1.objectType = MenuType.PRE_OBJ
        crmMenu1.icon = icon
        crmMenu1.iconIndex = 0

        SimpObjectDescription crmMenu2 = new SimpObjectDescription()
        crmMenu2.apiName = "LeadsObj"
        crmMenu2.name = "销售线索"
        crmMenu2.setActive(true)
        crmMenu2.objectType = MenuType.PRE_OBJ
        crmMenu2.icon = icon
        crmMenu2.iconIndex = 0

        SimpObjectDescription crmMenu3 = new SimpObjectDescription()
        crmMenu3.apiName = "AccountObj"
        crmMenu3.name = "客户"
        crmMenu3.setActive(true)
        crmMenu3.objectType = MenuType.PRE_OBJ
        crmMenu3.icon = icon
        crmMenu3.iconIndex = 0

        SimpObjectDescription crmMenu4 = new SimpObjectDescription()
        crmMenu4.apiName = "test__c"
        crmMenu4.name = "自定义对象"
        crmMenu4.setActive(true)
        crmMenu4.objectType = MenuType.UD_OBJ
        crmMenu4.icon = icon
        crmMenu4.iconIndex = 0

        SimpObjectDescription crmMenu5 = new SimpObjectDescription()
        crmMenu5.apiName = "test1__c"
        crmMenu5.name = "自定义对象"
        crmMenu5.setActive(true)
        crmMenu5.objectType = MenuType.UD_OBJ
        crmMenu5.icon = icon
        crmMenu5.iconIndex = 0

        List<SimpObjectDescription> crmMenuList = Lists.newArrayList(crmMenu1, crmMenu2, crmMenu3, crmMenu4, crmMenu5)

        List<MetaMenuData> metaMenuDataList = Lists.newArrayList()

        for (SimpObjectDescription crmMenu : crmMenuList) {
            def objectMenuData = new ObjectMenuData()
            objectMenuData.setSimpObjectDescription(crmMenu)
            metaMenuDataList.add(objectMenuData)
        }

        return metaMenuDataList
    }


    //mock自定义页面的元数据
    List<MetaMenuData> buildPageMenuDataFromPages() {
        def pageData1 = new PageData()
        pageData1.active = true
        pageData1.layoutApiName = "layout_8WFKHSfQT2__c"
        pageData1.displayName = "测试自定义页面"

        List<PageData> pageDatas = Lists.newArrayList(pageData1)

        List<MetaMenuData> metaMenuDataList = Lists.newArrayList()

        for (PageData pageData : pageDatas) {
            def pageMenuData = new PageMenuData()
            pageMenuData.setPageData(pageData)
            metaMenuDataList.add(pageMenuData)
        }
        return metaMenuDataList
    }

    UserInfo buildUserInfo() {
        def userInfo = new UserInfo()
        userInfo.setEnterpriseId(tenantId)
        userInfo.setEmployeeId(1000)
        userInfo.setEnterpriseAccount("fsceshi003")
        return userInfo;
    }

    ClientInfo buildClientInfo(ClientTypeEnum clientType) {
        def clientInfo = new ClientInfo()
        clientInfo.type = clientType
        clientInfo.locale = Locale.CHINA
        clientInfo.setVersion("111")
        return clientInfo
    }

    OuterUserInfo buildOuterUserInfo() {
        def outerUserInfo = new OuterUserInfo()
        outerUserInfo.setOutTenantId(1*********123)
        outerUserInfo.setOutUserId(***********)
        return outerUserInfo
    }

    List<NavigationMenu> getNavigationMenus() {
        def navigationMenu1 = new NavigationMenu()
        navigationMenu1.setName("伙伴学堂")
        navigationMenu1.setId(TempleIdUtil.guid)
        navigationMenu1.setIcon("123456")
        navigationMenu1.setMenuType(MenuType.ENTERPRISE_RELATION)

        def navigationMenu2 = new NavigationMenu()
        navigationMenu2.setName("代理通")
        navigationMenu2.setId(TempleIdUtil.guid)
        navigationMenu2.setIcon("123456")
        navigationMenu2.setMenuType(MenuType.ENTERPRISE_RELATION)

        return Lists.newArrayList(navigationMenu1, navigationMenu2)
    }
}
