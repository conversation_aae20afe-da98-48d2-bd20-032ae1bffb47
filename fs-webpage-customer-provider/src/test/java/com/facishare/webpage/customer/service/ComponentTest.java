package com.facishare.webpage.customer.service;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.TenantHomePageAction;
import com.facishare.webpage.customer.controller.model.arg.homepage.GetDropListItemsArg;
import com.facishare.webpage.customer.controller.model.result.homepage.GetDropListItemsResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/4/8
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-remote.xml")
public class ComponentTest {

    @Resource
    private TenantHomePageAction tenantHomePageAction;


    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Test
    public void testGetDropList(){
        UserInfo userInfo = new UserInfo();
        userInfo.setEmployeeId(1000);
        userInfo.setEnterpriseId(71574);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(Locale.CHINA);

        GetDropListItemsArg arg = new GetDropListItemsArg();
        arg.setAppType(4);
        arg.setAppId("ObjectDetailPage_MarketingEventObj");

        GetDropListItemsResult result = tenantHomePageAction.getDropListItems(userInfo, clientInfo, arg);
        System.out.println(result);
    }

}
