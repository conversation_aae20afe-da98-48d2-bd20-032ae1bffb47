package com.facishare.webpage.customer.test;

import com.facishare.qixin.relation.rest.EnterpriseRelationRoleService;
import com.facishare.qixin.relation.rest.RelationApiNamesConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * Created by zhangyu on 2019/12/23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TestGetAppList {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private EnterpriseRelationRoleService enterpriseRelationRoleService;
    @Autowired
    private RelationApiNamesConfig relationApiNamesConfig;


    @Test
    public void testGetObjectList(){
        List<String> apNames = enterpriseRelationRoleService.getAllOutRelationObject(71574, "fsceshi003", "FSAID_11490ea7", 1);
        List<String> apiNames = relationApiNamesConfig.getSortedApiNames(apNames);
        System.out.println(apiNames.size());

    }


}
