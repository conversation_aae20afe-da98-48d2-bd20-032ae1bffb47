package com.facishare.webpage.customer.groovy.paas

import com.facishare.cep.plugin.enums.ClientTypeEnum
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.api.model.MainChannelMenuVO
import com.facishare.webpage.customer.api.model.arg.QueryUserWebMainChannelArg
import com.facishare.webpage.customer.util.MainChannelUtils
import com.google.common.collect.Maps

/**
 * Created by zhangyu on 2020/11/30
 */
class WebMainChannelActionTest extends WebMainChannelBasic {

    def "getManWebMainChannelMenu"() {
        when:
        def result = tenantWebMainChannelAction.getManWebMainChannelList(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac),)
        then:
        result != null
        result.manageWebMainChannelVOS.size() == 1
        def webMainChannelVO = result.manageWebMainChannelVOS.get(0)
        webMainChannelVO.sourceType == SourceType.SYSTEM
        webMainChannelVO.apiName == "default__c"
        webMainChannelVO.mainChannelMenuVOList.size() == 7

        def mainChannelMenuVOMap = getMainChannelMenuVOMap(webMainChannelVO.mainChannelMenuVOList)
        def OUT_WORK_MENU = mainChannelMenuVOMap.get(OUT_WORK)
        OUT_WORK_MENU != null
    }

    def "queryUserWebMainChannelVOTest1"() {
        given:
        def arg = new QueryUserWebMainChannelArg()
        arg.setEnterpriseAccount("78810")
        arg.setEmployeeId(1017)
        arg.setLocale(Locale.CHINA)
        when:
        def result = userWebMainChannelService.queryUserWebMainChannelVO("78810", arg)
        then:
        result != null
        result.userWebMainChannelVO.mainChannelMenuVOList.size() != null
        def mainChannelMenuVOMap = getMainChannelMenuVOMap(result.userWebMainChannelVO.mainChannelMenuVOList)
        def OUT_WORK_MENU = mainChannelMenuVOMap.get(OUT_WORK)
        OUT_WORK_MENU != null

        def portal = mainChannelMenuVOMap.get(MainChannelUtils.getUniqueAppId("fsceshi003", Portal))
        portal != null
    }

    def "queryUserWebMainChannelVOTest2"() {
        given:
        def arg = new QueryUserWebMainChannelArg()
        arg.setEnterpriseAccount("78810")
        arg.setEmployeeId(1020)
        arg.setLocale(Locale.CHINA)
        when:
        def result = userWebMainChannelService.queryUserWebMainChannelVO("78810", arg)
        then:
        result != null
        result.userWebMainChannelVO.mainChannelMenuVOList.size() != null
        def mainChannelMenuVOMap = getMainChannelMenuVOMap(result.userWebMainChannelVO.mainChannelMenuVOList)
        def OUT_WORK_MENU = mainChannelMenuVOMap.get(OUT_WORK)
        OUT_WORK_MENU != null

        def portal = mainChannelMenuVOMap.get(MainChannelUtils.getUniqueAppId("fsceshi003", Portal))
        portal != null
    }

    def "queryUserWebMainChannelVOTest3"() {
        given:
        def arg = new QueryUserWebMainChannelArg()
        arg.setEnterpriseAccount("78810")
        arg.setEmployeeId(1000)
        arg.setLocale(Locale.CHINA)
        when:
        def result = userWebMainChannelService.queryUserWebMainChannelVO("78810", arg)
        then:
        result != null
        result.userWebMainChannelVO.mainChannelMenuVOList.size() != null
        def mainChannelMenuVOMap = getMainChannelMenuVOMap(result.userWebMainChannelVO.mainChannelMenuVOList)
        def OUT_WORK_MENU = mainChannelMenuVOMap.get(OUT_WORK)
        OUT_WORK_MENU == null

        def portal = mainChannelMenuVOMap.get(MainChannelUtils.getUniqueAppId("fsceshi003", Portal))
        portal != null
    }

    private Map<String, MainChannelMenuVO> getMainChannelMenuVOMap(List<MainChannelMenuVO> mainChannelMenuVOList) {
        Map<String, MainChannelMenuVO> mainChannelMenuVOMap = Maps.newHashMap()

        for (MainChannelMenuVO mainChannelMenuVO : mainChannelMenuVOList) {
            mainChannelMenuVOMap.put(MainChannelUtils.getUniqueAppId(mainChannelMenuVO.upEnterpriseAccount, mainChannelMenuVO.appId), mainChannelMenuVO)
        }

        return mainChannelMenuVOMap
    }

}
