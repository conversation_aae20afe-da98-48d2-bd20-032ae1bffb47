package com.facishare.webpage.customer.controller.impl;

import com.facishare.webpage.customer.api.model.arg.GetCrossAppPageTemplateArg;
import com.facishare.webpage.customer.api.model.arg.GetCrossAppPageTemplesArg;
import com.facishare.webpage.customer.api.model.arg.GetInnerAppPageTemplateArg;
import com.facishare.webpage.customer.api.model.arg.GetVendorAppPageTemplateArg;
import com.facishare.webpage.customer.api.model.result.GetCrossAppPageTemplateResult;
import com.facishare.webpage.customer.api.model.result.GetCrossAppPageTemplesResult;
import com.facishare.webpage.customer.api.model.result.GetVendorAppPageTemplateResult;
import com.facishare.webpage.customer.api.model.result.QueryAppStatAccessInfoResult;
import com.facishare.webpage.customer.api.service.AppPageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class AppPageServiceTest {
    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private AppPageService appPageService;

    @Test
    public void getCrossApp(){
        Map<String, String> header = new HashMap<>();
        header.put("X-fs-Enterprise-Id", "78810");
        header.put("x-fs-enterprise-account", "78810");
        header.put("X-fs-Employee-Id", String.valueOf(*********));
        header.put("X-app-id", "FSAID_11490d9e");

        GetCrossAppPageTemplateArg arg = new GetCrossAppPageTemplateArg();
        arg.setUpEi(71574);
        arg.setOuterEi(*********);
        arg.setOuterId(*********);
        arg.setAppId("FSAID_11490d9e");
        GetCrossAppPageTemplateResult ret = appPageService.getCrossAppPageTemplate(header, arg);
        System.out.println(ret);
    }

    @Test
    public void getVendorApp(){
        Map<String, String> header = new HashMap<>();
        header.put("X-fs-Enterprise-Id", "78810");
        header.put("x-fs-enterprise-account", "78810");
        header.put("X-fs-Employee-Id", String.valueOf(*********));
        header.put("X-app-id", "FSAID_11490d9e");

        GetVendorAppPageTemplateArg arg = new GetVendorAppPageTemplateArg();
        arg.setEi(78810);
        arg.setEmployeeId(1020);
        arg.setAppId("FSAID_11491009");
        GetVendorAppPageTemplateResult ret = appPageService.getVendorAppPageTemplate(header, arg);
        System.out.println(ret);
    }

    @Test
    public void queryVendorWebStatAccessInfo(){
        Map<String, String> header = new HashMap<>();
        header.put("X-fs-Enterprise-Id", "85036");
        header.put("x-fs-enterprise-account", "85036");
        header.put("X-fs-Employee-Id", String.valueOf(1000));
        header.put("X-app-id", "FSAID_11490d9e");

        GetVendorAppPageTemplateArg arg = new GetVendorAppPageTemplateArg();
        arg.setEi(85036);
        arg.setEmployeeId(1000);
        arg.setAppId("FSAID_11490d9e");
        QueryAppStatAccessInfoResult ret = appPageService.queryVendorWebStatAccessInfo(header, arg);
        System.out.println(ret);
    }

    @Test
    public void getVendorAspp(){
        Map<String, String> header = new HashMap<>();

        GetCrossAppPageTemplesArg arg = new GetCrossAppPageTemplesArg();
        arg.setTenantId(78810);
        arg.setEmployeeId(1020);
        arg.setAppId("FSAID_11491009");
        GetCrossAppPageTemplesResult ret = appPageService.getCrossAppPageTemples(header, arg);
        System.out.println(ret);
    }
}
