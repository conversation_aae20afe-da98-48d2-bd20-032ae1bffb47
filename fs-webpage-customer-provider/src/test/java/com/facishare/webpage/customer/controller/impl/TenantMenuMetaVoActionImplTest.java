package com.facishare.webpage.customer.controller.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/17.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TenantMenuMetaVoActionImplTest {

    static {
        System.getProperties().setProperty("process.profile", "ceshi113");
    }

    @Test
    public void getAllTenantMenuLists() throws Exception {
    }

    @Test
    public void getTenantMenuDetailById() throws Exception {
    }

    @Test
    public void getTenantDefaultMenuDetailByAppId() throws Exception {
    }

    @Test
    public void updateTenantMenu() throws Exception {
    }

}