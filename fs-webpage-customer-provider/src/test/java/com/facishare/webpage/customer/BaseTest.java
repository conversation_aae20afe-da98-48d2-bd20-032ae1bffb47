package com.facishare.webpage.customer;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import java.util.Locale;

/**
 * <AUTHOR> create by liy on 2022/5/30
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class BaseTest {

    protected UserInfo userInfo;
    protected ClientInfo clientInfo;

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @PostConstruct
    private void init() {

        userInfo = new UserInfo();
        userInfo.setEnterpriseId(71574);
        userInfo.setEnterpriseAccount("fsceshi003");
        userInfo.setEmployeeId(1017);

        clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setVersion("chrome");
        clientInfo.setLocale(Locale.CHINA);
    }
}
