package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.PaaSAppAuthService;
import com.facishare.qixin.objgroup.common.service.utils.PaasMetaUtils;
import com.facishare.qixin.relation.rest.FsDownstreamService;
import com.facishare.qixin.relation.rest.FsUpstreamService;
import com.facishare.webpage.customer.common.resource.UdObjRestResource;
import com.facishare.webpage.customer.common.resource.model.arg.FindAllPluginByApiNameArg;
import com.facishare.webpage.customer.common.resource.model.arg.FindStoreArg;
import com.facishare.webpage.customer.common.resource.model.arg.GetBusComponentsArg;
import com.facishare.webpage.customer.common.resource.model.result.FindStoreResult;
import com.facishare.webpage.customer.common.resource.model.result.GetBusComponentsResult;
import com.fxiaoke.appcenter.restapi.arg.QueryAppListByFsEnterpriseAccountArg;
import com.fxiaoke.appcenter.restapi.common.BaseResult;
import com.fxiaoke.appcenter.restapi.model.OpenAppDO;
import com.fxiaoke.appcenter.restapi.service.OpenFsUserBindAppService;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.*;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.result.data.UserRoleInfoData;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.enterpriserelation2.service.DownstreamService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyi on 2019/11/29.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class RemoteServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UdObjRestResource udObjRestResource;
    @Resource
    private UpstreamService upstreamService;
    @Resource
    private FsDownstreamService fsDownstreamService;
    @Resource
    private DownstreamService downstreamService;
    @Resource
    private FxiaokeAccountService fxiaokeAccountService;
    @Resource
    private PaaSAppAuthService paaSAppAuthService;
    @Resource
    private FsUpstreamService fsUpstreamService;
    @Resource
    private AppOuterRoleService appOuterRoleService;
    @Resource
    private OpenFsUserBindAppService openFsUserBindAppService;


    @Test
    public void testBusinessComponent(){
        GetBusComponentsArg arg = new GetBusComponentsArg();
        arg.setObjectApiName("object_15Qyb__c");
        GetBusComponentsResult result = udObjRestResource.getBusComponents(arg, PaasMetaUtils.getHeaders(String.valueOf(78057), Locale.CHINA));
        System.out.println(result);

    }

    @Test
    public void testFind() {
        FindStoreArg arg = FindStoreArg.builder().key("fx.store.server").build();
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", String.valueOf(71574));
        headers.put("x-fs-userInfo", String.valueOf(1017));
        FindStoreResult result = udObjRestResource.findStore(headers, arg);
        System.out.println(result);
    }

    @Test
    public void getObjectPlugins() {
        FindStoreArg arg = FindStoreArg.builder().key("fx.store.server").build();
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", String.valueOf(74255));
        headers.put("X-fs-Enterprise-Id", String.valueOf(74255));
        //headers.put("x-fs-userInfo", String.valueOf(1000));
        //headers.put("X-fs-Employee-Id", String.valueOf(1000));
        FindAllPluginByApiNameArg findAllPluginByApiNameArg = new FindAllPluginByApiNameArg();
        findAllPluginByApiNameArg.setObjectApiName("SalesOrderObj");
        JSONObject result = udObjRestResource.getObjectPlugins(headers, findAllPluginByApiNameArg);
        System.out.println(result);
    }
    @Test
    public void testGetApp() {
        ListOpenedAppsOutArg arg = new ListOpenedAppsOutArg();
        arg.setUpstreamTenantId(71574);
        arg.setAppType(1);

        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId("x_web_user_defined");

        RestResult<List<SimpleLinkAppResult>> result = upstreamService.listOpenedApps(headerObj, arg);
        List<SimpleLinkAppResult> data = result.getData();
        System.out.println(data);
    }

    @Test
    public void testListBenchAndDownStreamAppsForTenant() {

        ListBenchAndDownStreamAppsForTenantArg arg = new ListBenchAndDownStreamAppsForTenantArg();
        arg.setTenantId(71574);

        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId("x_web_user_defined");
        headerObj.put("x-fs-locale", Locale.CHINA);
        RestResult<BenchAndDownstreamAppsResult> result = downstreamService.listBenchAndDownStreamAppsForTenant(headerObj, arg);
        System.out.println("--------------------------------------------------");
        System.out.println(result.getData().getLinkAppVos());
        System.out.println("--------------------------------------------------");
    }

    @Test
    public void testListBenchAndDownStreamAppsForUser() {
        ListBenchAndDownStreamAppsForUserArg arg = new ListBenchAndDownStreamAppsForUserArg();
        arg.setTenantId(80980);
        arg.setFsUserId(1000);

        HeaderObj headerObj = HeaderObj.newInstance(80980);
        headerObj.setAppId("x_web_user_defined");

        RestResult<BenchAndDownstreamAppsResult> result = downstreamService.listBenchAndDownStreamAppsForUser(headerObj, arg);
        System.out.println("--------------------------------------------------");
        System.out.println(result.getData());
        System.out.println("--------------------------------------------------");
    }

    @Test
    public void testGetCrossApp() {
        List<LinkAppVo> linkAppVos = fsDownstreamService.listEmployeeAppRolesByUpstreamEa("78810", 1000, "fsceshi003");
        List<String> names = linkAppVos.stream().map(linkAppVo -> linkAppVo.getName()).collect(Collectors.toList());
        System.out.println(linkAppVos);
    }

    @Test
    public void testGetAppByOutUser() {
        ListEmployeeAppRolesByUpstreamEaArg arg = new ListEmployeeAppRolesByUpstreamEaArg();
        arg.setDownstreamOuterTenantId(*********l);
        arg.setDownstreamOuterUid(*********L);

        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId("x_web_user_defined");

        RestResult<ListEmployeeAppRolesByUpstreamEaResult> result = downstreamService.listEmployeeAppRolesByUpstreamEa(headerObj, arg);

        System.out.println(result.getData().getLinkAppVoList().get(0));
    }

    @Test
    public void getOuterAccountByFs() {

        HeaderObj headerObj = HeaderObj.newInstance(0);
        GetOuterAccountByFsArg arg = new GetOuterAccountByFsArg();
        arg.setFsUserId(1000);
        arg.setEa("79410");
        RestResult<GetOuterAccountByFsResult> result = fxiaokeAccountService.getOuterAccountByFs(headerObj, arg);
        System.out.println(result);

        //data=GetOuterAccountByFsResult(outerTenantId=*********, outerUid=*********))


    }

    @Test
    public void getEmployeeIdByOutUid() {
        HeaderObj headerObj = HeaderObj.newInstance(0);

        BatchGetFsAccountByOuterUidsArg arg = new BatchGetFsAccountByOuterUidsArg();
        arg.setOuterUids(Lists.newArrayList(*********L));

        RestResult<BatchGetFsAccountByOuterUidsResult> result = fxiaokeAccountService.batchGetFsAccountByOuterUids(headerObj, arg);
        System.out.println(result.getData());

    }

    @Test
    public void name() {
        List<SimpleLinkAppResult> results = fsUpstreamService.listOpenedApps(71574, 1);
        System.out.println(results);
    }

    @Test
    public void testListAppOuterRolesByAppId() {
        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId("x_web_user_defined");

        ListAppOuterRolesByAppIdArg arg = new ListAppOuterRolesByAppIdArg();
        arg.setLinkAppId("FSAID_11491009");
        arg.setTenantId(71574);
        RestResult<List<RoleInfoData>> result = appOuterRoleService.listAppOuterRolesByAppId(headerObj, arg);
        System.out.println(result.getData());
    }

    @Test
    public void testListUserOuterRolesByAppId() {
        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId("x_web_user_defined");

        ListUserOuterRolesByAppIdArg arg = new ListUserOuterRolesByAppIdArg();
        arg.setLinkAppId("FSAID_11490d9e");
        arg.setOuterTenantId(200074183L);
        arg.setOuterUid(100018591L);
        arg.setTenantId(71568);

        RestResult<List<UserRoleInfoData>> result = appOuterRoleService.listUserOuterRolesByAppId(headerObj, arg);
        System.out.println(result.getData());

    }

    @Test
    public void testOpenFsUserBindAppService(){
        com.fxiaoke.appcenter.restapi.common.HeaderObj headerObj = com.fxiaoke.appcenter.restapi.common.HeaderObj.newInstance("71574");
        headerObj.put("X-fs-locale", Locale.ENGLISH.toLanguageTag());
        QueryAppListByFsEnterpriseAccountArg arg = new QueryAppListByFsEnterpriseAccountArg();
        arg.setArg2("fsceshi003");
        arg.setFsUserVO("fsceshi003", 1001,"");
        BaseResult<List<OpenAppDO>> listBaseResult = openFsUserBindAppService.queryAppListByFsEnterpriseAccount(headerObj, arg);
        System.out.println(listBaseResult.getResult());
    }

}