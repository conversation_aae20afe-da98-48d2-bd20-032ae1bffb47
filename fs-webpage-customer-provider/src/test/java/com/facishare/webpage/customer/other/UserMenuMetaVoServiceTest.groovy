package com.facishare.webpage.customer.other

import com.alibaba.fastjson.JSON
import com.facishare.webpage.customer.controller.UserMenuAction
import com.facishare.webpage.customer.controller.impl.UserMenuActionImpl
import com.facishare.webpage.customer.controller.model.UserMenuTempleVo
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenuByAppIdArg
import org.apache.commons.lang3.ObjectUtils

/**
 * Created by zhangyu on 2019/9/21
 */
class UserMenuMetaVoServiceTest extends MenuMetaVoBaseTest {
    private UserMenuAction userMenuAction

    private String expectMenu = "{\n" +
            "                \"isCurrent\":true,\n" +
            "                \"isSystem\":false,\n" +
            "                \"items\":[{\n" +
            "                        \"id\":\"AccountObj\",\n" +
            "                        \"displayName\":\"客户\",\n" +
            "                        \"referenceApiname\":\"AccountObj\",\n" +
            "                        \"number\":10,\n" +
            "                        \"type\":\"menu\",\n" +
            "                        \"isHidden\":false,\n" +
            "                        \"privilegeAction\":[\n" +
            "                            \"List\",\n" +
            "                            \"Add\"\n" +
            "                        ]\n" +
            "                    },{\n" +
            "                        \"id\":\"ContactObj\",\n" +
            "                        \"displayName\":\"联系人\",\n" +
            "                        \"referenceApiname\":\"ContactObj\",\n" +
            "                        \"number\":20,\n" +
            "                        \"type\":\"menu\",\n" +
            "                        \"isHidden\":false,\n" +
            "                        \"privilegeAction\":[\n" +
            "                            \"List\",\n" +
            "                            \"Add\"\n" +
            "                        ]\n" +
            "                    }]" +
            "}"

    def "getUserMenuById"() {
        given:
        userMenuAction = new UserMenuActionImpl()
        userMenuAction.tenantMenuService = tenantMenuService
        userMenuAction.tenantPageTempleService = tenantPageTempleService
        UserMenuTempleVo expectMenus = JSON.parseObject(expectMenu, UserMenuTempleVo.class)

        when:
        def arg = new GetUserMenuByAppIdArg()
        arg.setId(menuId)
        def result = userMenuAction.getUserMenuByAppId(userInfo, outerUserInfo, arg)
        then:
        ObjectUtils.equals(expectMenus, result.userMenuTempleVos.get(0))
    }
}
