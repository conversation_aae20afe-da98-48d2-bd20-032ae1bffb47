package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.webpage.customer.BaseTest;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.service.HomePageRestService;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.controller.UserPageTempleAction;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.*;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.*;
import com.facishare.webpage.customer.dao.LinkAppObjectAssociationDao;
import com.facishare.webpage.customer.dao.entity.LinkAppObjectAssociationEntity;
import com.google.gson.JsonObject;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static org.junit.Assert.*;

/**
 * <AUTHOR> create by liy on 2022/5/30
 */
public class UserPageTempleActionTest extends BaseTest {
    @Resource
    private UserPageTempleAction userPageTempleAction;
    @Autowired
    private PaaSAppRestService paaSAppRestService;
    @Autowired
    private LinkAppObjectAssociationDao linkAppObjectAssociationDao;

    @Autowired
    private HomePageRestService homePageRestService;

    @Test
    public void testGetAppVendorTemplateList() {
        userInfo.setEnterpriseId(74164);
        userInfo.setEnterpriseAccount("74164");
        userInfo.setEmployeeId(1000);
        //
        clientInfo.setType(ClientTypeEnum.iOS);
        clientInfo.setVersion("*********");
        //
        GetWebVendorTemplateListArg arg = new GetWebVendorTemplateListArg();
        arg.setAppId("FSAID_11491009");
        try {
            GetWebVendorTemplateListResult appVendorTemplateList = userPageTempleAction.getWebVendorTemplateList(userInfo, clientInfo, arg);
        } catch (Exception e) {
            System.out.println();
        }
        System.out.println();
    }

    @Test
    public void testUpdateUserPageTempleStatus() {
        userInfo.setEnterpriseId(85188);
        userInfo.setEnterpriseAccount("85188");
        userInfo.setEmployeeId(1000);
        UpdateUserPageTempleStatusArg arg = UpdateUserPageTempleStatusArg.builder()
                .status(1)
                .templeId("85188_1a48cf02430149809f01e6de4c4704be")
                .build();
            UpdateUserPageTempleStatusResult result = userPageTempleAction.updateUserPageTempleStatus(userInfo, arg);
            assertNotNull(result);
    }


    @Test
    public void testCheckCanAddUserPageTemplet() {
        userInfo.setEnterpriseId(85188);
        userInfo.setEnterpriseAccount("85188");
        userInfo.setEmployeeId(1000);
        CheckCanAddUserPageTempletArg arg = CheckCanAddUserPageTempletArg.builder()
                .appId("CRM")
                .build();
        CheckCanAddUserPagetempletResult result = userPageTempleAction.checkCanAddUserPageTemplet(userInfo,arg);
        assertNotNull(result);
    }

    @Test
    public void testGetUserPageTempleById() {
        userInfo.setEnterpriseId(85188);
        userInfo.setEnterpriseAccount("85188");
        userInfo.setEmployeeId(1000);
        GetUserPageTemplateByIdArg arg = new GetUserPageTemplateByIdArg();
        arg.setTempleId("85188_a25b64601e2c408d9147f5794a4a5e5b");
        GetUserPageTemplateByIdResult result = userPageTempleAction.getUserPageTempleById(userInfo,arg);
        assertNotNull(result);
    }

    @Test
    public void getAppListRest() {

        QueryPaasAppArg arg = new QueryPaasAppArg();
        arg.setEnterpriseId(71574);
        arg.setEnterpriseAccount("71574");
        arg.setEmployeeId(1000);
        arg.setLocale(Locale.CHINESE);
        QueryPaasAppResult result = paaSAppRestService.getAppList("71574",arg);
        assertNotNull(result);
    }
    @Test
    public void getAppLise() {
        List<AccessTypeEnum> list = new ArrayList<>();
        list.add(AccessTypeEnum.APP);
        list.add(AccessTypeEnum.WEB);
        GetUserPerAppArg arg = GetUserPerAppArg.builder().accessTypeList(list).tenantId(78810).employeeId(1000).build();

        GetUserPerAppResult result = paaSAppRestService.getUserPerAppList("78810",arg);
        assertNotNull(result);
    }
    @Test
    public void clearCache() {

        ClearTenantCacheArg arg = ClearTenantCacheArg.builder().tenantId(78810).build();

        ClearTenantCacheResult result = paaSAppRestService.clearTenantCache("78810",arg);
        assertNotNull(result);
    }
    @Test
    public void getAppLise2() {
        List<Scope> list = new ArrayList<>();
        Scope scope = new Scope();
        scope.setDataType(1);
        scope.setDataId("1000");
        list.add(scope);
        SavePaasAppArg arg = new SavePaasAppArg();
        arg.setAppId("FSAID_9896c1");
        arg.setScopes(list);
        arg.setTenantId(78810);
        arg.setUserId(1000);
        arg.setAccessType(AccessTypeEnum.APP.getType());

        SavePaasAppResult result = paaSAppRestService.saveTenantPaaSAppInfo("78810",arg);
        assertNotNull(result);
    }

    @Test
    public void testGetHomePageLayoutById() {
        GetHomePageLayoutByIdArg arg = new GetHomePageLayoutByIdArg();
        arg.setEnterpriseAccount("74164");
        arg.setLayoutId("74164_f91772e7316e41a8a1b222f14865a9d2");
        arg.setEnterpriseId(74164);
        arg.setEmployeeId(1000);
        arg.setAppType(BizType.APP.getType());
        arg.setLocale(Locale.ENGLISH);
        arg.setType(ClientTypeEnum.Android);
        GetHomePageLayoutByIdResult result =  homePageRestService.getHomePageLayoutById("74164",arg);
        System.out.println(JSONObject.toJSONString(result));
    }

}
