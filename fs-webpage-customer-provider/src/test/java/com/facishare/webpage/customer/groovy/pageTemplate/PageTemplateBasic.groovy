package com.facishare.webpage.customer.groovy.pageTemplate

import com.facishare.cep.plugin.model.UserInfo
import com.facishare.qixin.sysdb.serivce.DataSourceService
import com.facishare.qixin.sysdb.serivce.SystemDataService
import com.facishare.qixin.sysdb.serivce.SystemDataServiceImpl
import com.facishare.webpage.customer.api.constant.TempleType
import com.facishare.webpage.customer.api.model.result.MakeHomePageFormalResult
import com.facishare.webpage.customer.api.service.HomePageService
import com.facishare.webpage.customer.core.config.PageTemplateConfig
import com.facishare.webpage.customer.core.model.PageTemplateConfigVO
import com.facishare.webpage.customer.controller.TenantPageTempleAction
import com.facishare.webpage.customer.controller.UserPageTempleAction
import com.facishare.webpage.customer.controller.impl.TenantPageTempleActionImpl
import com.facishare.webpage.customer.controller.impl.UserPageTempleActionImpl
import com.facishare.webpage.customer.controller.model.AppData
import com.facishare.webpage.customer.dao.TenantPageTempleDao
import com.facishare.webpage.customer.dao.TenantPageTempleDaoImpl
import com.facishare.webpage.customer.dao.entity.PageTempleEntity
import com.facishare.webpage.customer.groovy.MockResource
import com.facishare.webpage.customer.model.MenuTemple
import com.facishare.webpage.customer.service.*
import com.facishare.webpage.customer.service.impl.TenantPageTempleBaseServiceImpl
import com.facishare.webpage.customer.service.impl.UserPageTempleServiceImpl
import com.facishare.webpage.customer.system.datasource.PageTemplateDataServiceImpl
import com.facishare.webpage.customer.util.TempleIdUtil
import com.fxiaoke.api.model.Temp2NormalPageTemplate
import com.fxiaoke.api.service.PageTemplateService
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.EmployeeCardResult
import com.fxiaoke.enterpriserelation2.result.GetOuterAccountByFsResult
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService
import com.google.common.collect.Lists
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import spock.lang.Shared

/**
 * Created by zhangyu on 2020/6/23
 */
class PageTemplateBasic extends MockResource {

    public TenantPageTempleBaseService tenantPageTempleBaseService;

    public TenantPageTempleAction tenantPageTempleAction

    public UserPageTempleAction userPageTempleAction

    public UserPageTempleService userPageTempleService

    public TenantPageTempleDao tenantPageTempleDao

    public SystemDataService appSystemDataService

    public DataSourceService pageTemplateDataService

    public String appId = "FSAID_11490d9e"

    public UserInfo upUserInfo = buildUpUserInfo()

    public String webPageId = TempleIdUtil.guid

    public String menuPageId = TempleIdUtil.guid

    public String appPageId = TempleIdUtil.guid

    def pageTemplateConfig = Mock(PageTemplateConfig.class) {

        def pageTemplateConfigVO = new PageTemplateConfigVO()
        pageTemplateConfigVO.setApplicationType(0)
        pageTemplateConfigVO.setCrossApp(true)
        pageTemplateConfigVO.setHasMenu(true)
        getPageTemplateConfig(_) >> pageTemplateConfigVO
    }

    def applicationService = Mock(ApplicationService.class) {
        AppData appData = new AppData()
        appData.setName("代理通")
        getCrossAppData(_, _) >> appData
        getInnerAppData(_, _, _, _) >> appData
    }

    def publicEmployeeService = Mock(PublicEmployeeService.class) {
        RestResult<List<EmployeeCardResult>> listRestResult = new RestResult()
        def employeeCardResult = new EmployeeCardResult()
        employeeCardResult.setOuterTenantId(*********)
        employeeCardResult.setOuterUid(********)
        employeeCardResult.setNameSpell("张三")
        employeeCardResult.setGender("男")
        employeeCardResult.setProfileImage("img/path")

        listRestResult.setData(Lists.newArrayList(employeeCardResult))
        listSourceEmployeesByFs(_, _) >> listRestResult
    }

    def fxiaokeAccountService = Mock(FxiaokeAccountService.class) {
        RestResult<GetOuterAccountByFsResult> result = new RestResult<GetOuterAccountByFsResult>()
        def getOuterAccountByFsResult = new GetOuterAccountByFsResult()
        getOuterAccountByFsResult.setOuterUid(********)
        getOuterAccountByFsResult.setOuterTenantId(*********)
        result.setData(getOuterAccountByFsResult)
        getOuterAccountByFs(_, _) >> result
    }

    def pageTemplateService = Mock(PageTemplateService.class) {
        temp2NormalPageTemplate(_) >> new Temp2NormalPageTemplate.Result()
    }

    def homePageService = Mock(HomePageService.class) {

        def result = new MakeHomePageFormalResult()
        result.setUpdateHomePage(true)
        makeHomePageFormal(_) >> result
    }

    def tenantMenuService = Mock(TenantMenuService.class) {
        temp2Normal(_, _, _, _) >> new MenuTemple()
    }

    def homePageCommonService = Mock(HomePageCommonService.class) {
        getScopeList(_, _) >> Lists.newArrayList("11111", "22222")
    }

    private ExpectedException exception = ExpectedException.none()

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule)
    @Shared
    PageTempleEntity webPageTempleEntity  //web端模板
    @Shared
    PageTempleEntity appPageTempleEntity  //app端模板

    def setupSpec() {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-webpage-customerLayout")
    }

    def setup() {

        tenantPageTempleDao = new TenantPageTempleDaoImpl()
        tenantPageTempleDao.datastore = datastore

        pageTemplateDataService = new PageTemplateDataServiceImpl()
        pageTemplateDataService.tenantPageTempleDao = tenantPageTempleDao
        pageTemplateDataService.homePageCommonService = homePageCommonService

        appSystemDataService = new SystemDataServiceImpl()
        appSystemDataService.dataSourceService = pageTemplateDataService
        appSystemDataService.sysTemplateConfig = sysTemplateConfig

        tenantPageTempleBaseService = new TenantPageTempleBaseServiceImpl()
        tenantPageTempleBaseService.appSystemDataService = appSystemDataService

        userPageTempleService = new UserPageTempleServiceImpl()
        userPageTempleService.remoteService = remoteService
        userPageTempleService.tenantPageTempleBaseService = tenantPageTempleBaseService
        userPageTempleService.qxEIEAConverter = qxEIEAConverter
        userPageTempleService.appSystemDataService = appSystemDataService
        userPageTempleService.fxiaokeAccountService = fxiaokeAccountService

        tenantPageTempleAction = new TenantPageTempleActionImpl()
        tenantPageTempleAction.tenantPageTempleBaseService = tenantPageTempleBaseService
        tenantPageTempleAction.remoteService = remoteService
        tenantPageTempleAction.homePageService = homePageService
        tenantPageTempleAction.pageTemplateService = pageTemplateService
        tenantPageTempleAction.tenantMenuService = tenantMenuService
        tenantPageTempleAction.pageTemplateConfig = pageTemplateConfig
        tenantPageTempleAction.applicationService = applicationService
        tenantPageTempleAction.organizationCommonService = scopeService
        tenantPageTempleAction.qxEIEAConverter = qxEIEAConverter
        tenantPageTempleAction.homePageService = homePageService
        tenantPageTempleAction.tenantMenuService = tenantMenuService

        userPageTempleAction = new UserPageTempleActionImpl()
        userPageTempleAction.userPageTempleService = userPageTempleService
        userPageTempleAction.remoteCrossService = remoteCrossService
        userPageTempleAction.publicEmployeeService = publicEmployeeService
        userPageTempleAction.webPageEventService = webPageEventService

        createMessage()
    }

    def createMessage() {

        def buildWebPageTempleEntity = buildWebPageTempleEntity()
        tenantPageTempleDao.findAndModify(71574, buildWebPageTempleEntity)
        webPageTempleEntity = buildWebPageTempleEntity


        def buildAppPageTempleEntity = buildAppPageTempleEntity()
        tenantPageTempleDao.findAndModify(71574, buildAppPageTempleEntity)
        appPageTempleEntity = buildAppPageTempleEntity
    }

    PageTempleEntity buildWebPageTempleEntity() {
        def pageTempleEntity = new PageTempleEntity()
        pageTempleEntity.setTenantId(71574)
        pageTempleEntity.setName("web端模板")
        pageTempleEntity.setStatus(0)
        pageTempleEntity.setCreateTime(1592896327000)
        pageTempleEntity.setUpdateTime(1592896327000)
        pageTempleEntity.setType(TempleType.WEB)
        pageTempleEntity.setAppId(appId)
        pageTempleEntity.setSystem(false)
        pageTempleEntity.setCreatorId(1000)
        pageTempleEntity.setTempleId(TempleIdUtil.buildId(71574))
        pageTempleEntity.setDescription("测试web端模板")
        pageTempleEntity.setPriorityLevel(9)
        pageTempleEntity.setScopes(Lists.newArrayList("OR-*********", "OR-**********"))
        pageTempleEntity.setUpdaterId(1000)
        pageTempleEntity.setWebMenuId(menuPageId)
        pageTempleEntity.setWebPageId(webPageId)
        return pageTempleEntity
    }

    PageTempleEntity buildAppPageTempleEntity() {
        def pageTempleEntity = new PageTempleEntity()
        pageTempleEntity.setTenantId(71574)
        pageTempleEntity.setName("app端模板")
        pageTempleEntity.setStatus(0)
        pageTempleEntity.setCreateTime(1592896327000)
        pageTempleEntity.setUpdateTime(1592896327000)
        pageTempleEntity.setType(TempleType.APP)
        pageTempleEntity.setAppId(appId)
        pageTempleEntity.setSystem(false)
        pageTempleEntity.setCreatorId(1000)
        pageTempleEntity.setTempleId(TempleIdUtil.buildId(71574))
        pageTempleEntity.setDescription("测试app端模板")
        pageTempleEntity.setPriorityLevel(9)
        pageTempleEntity.setScopes(Lists.newArrayList("OR-*********", "OR-**********"))
        pageTempleEntity.setUpdaterId(1000)
        pageTempleEntity.setAppPageId(appPageId)
        return pageTempleEntity
    }

    UserInfo buildUpUserInfo() {
        UserInfo userInfo = new UserInfo()
        userInfo.setEmployeeId(1000)
        userInfo.setEnterpriseId(71574)
        userInfo.setEnterpriseAccount("fsceshi003")
        return userInfo;
    }

}
