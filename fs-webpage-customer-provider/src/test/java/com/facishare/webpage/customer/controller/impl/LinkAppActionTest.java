package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.common.result.BaseResult;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.CreateLinkAppArg;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppByAppIdArg;
import com.facishare.webpage.customer.api.model.arg.UpdateLinkAppArg;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.utils.UploadIcon;
import com.facishare.webpage.customer.controller.LinkAppAction;
import com.facishare.webpage.customer.util.GeneralUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/2/24
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class LinkAppActionTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private LinkAppAction linkAppAction;

    @Autowired
    QuotaService quotaService;

    @Test
    public void getHomePageById() {
//        Filter<Query> filter = query -> query.field("appId").equal("AccountObj");

//        List<HomePageLayoutEntity> list = (List<HomePageLayoutEntity>) systemDataService.queryDataList(71574, filter);
//        System.out.println(list.size());
    }

    @Test
    public void getLinkAppList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetLinkAppListResult result = linkAppAction.getLinkAppList(userInfo, clientInfo);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void getUpLinkAppList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetUpLinkAppListResult result = linkAppAction.getUpLinkAppList(userInfo, clientInfo);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void createLinkApp() {
        //UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        String argStr = "{\n" +
                "    \"scopeList\":[\n" +
                "        {\n" +
                "            \"dataId\":\"5b6817bbe4b066655a6397e4\",\n" +
                "            \"dataType\":5\n" +
                "        }\n" +
                "    ],\n" +
                "    \"name\":\"分子公司管控\",\n" +
                "    \"description\":\"分子公司管控\",\n" +
                "    \"useUserPageTempleFlag\":false,\n" +
                "    \"iconIndex\":\"paasApp-19\",\n" +
                "    \"icon\":\"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon19.svg\",\n" +
                "    \"status\":1,\n" +
                "    \"appId\":\"FSAID_PaaS_83c23733513\",\n" +
                "    \"sourceType\":\"customer\"\n" +
                "}";
        CreateLinkAppArg arg = JSON.toJavaObject(JSON.parseObject(argStr), CreateLinkAppArg.class);
        List<Scope> scopeList = new ArrayList<>();
        Scope scope = new Scope();
        scope.setDataType(5);
        scope.setDataId("5b6817bbe4b066655a6397e4");
        scopeList.add(scope);
        arg.setScopeList(scopeList);
        arg.setName(arg.getName() + System.currentTimeMillis());
        UploadIcon uploadIcon = new UploadIcon();
        uploadIcon.setType("image/svg+xml");
        uploadIcon.setPath("TN_c564110236a64a63ab20c294f1515917");
        arg.setUploadIcon(uploadIcon);
        CreateLinkAppResult result = linkAppAction.createLinkApp(userInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void updateLinkApp() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        String argStr = "{\n" +
                "    \"scopeList\":[\n" +
                "        {\n" +
                "            \"dataId\":\"1017\",\n" +
                "            \"dataType\":1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"name\":\"分子公司管控7\",\n" +
                "    \"description\":\"分子公司管控\",\n" +
                "    \"useUserPageTempleFlag\":false,\n" +
                "    \"iconIndex\":\"paasApp-19\",\n" +
                "    \"icon\":\"https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon19.svg\",\n" +
                "    \"status\":1,\n" +
                "    \"appId\":\"FSAID_11491028\",\n" +
                "    \"sourceType\":\"customer\"\n" +
                "}";
        UpdateLinkAppArg arg = JSON.toJavaObject(JSON.parseObject(argStr), UpdateLinkAppArg.class);
        List<Scope> scopeList = new ArrayList<>();
        Scope scope = new Scope();
        scope.setDataType(5);
        scope.setDataId("5b6817bbe4b066655a6397e4");
        scopeList.add(scope);
        arg.setScopeList(scopeList);
        UploadIcon uploadIcon = new UploadIcon();
        uploadIcon.setType("image/svg+xml");
        uploadIcon.setPath("TN_c564110236a64a63ab20c294f1515917");
        arg.setUploadIcon(uploadIcon);
        UpdateLinkAppResult result = linkAppAction.updateLinkApp(userInfo, clientInfo,arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void getLinkAppByAppId() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetLinkAppByAppIdArg getLinkAppByAppIdArg = new GetLinkAppByAppIdArg();
        getLinkAppByAppIdArg.setAppId("FSAID_11491040");
        GetLinkAppByAppIdResult result = linkAppAction.getLinkAppByAppId(userInfo, clientInfo, getLinkAppByAppIdArg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void testUpdateQuota() throws ParseException {
        String data = "2088-02-24 24:21:00";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BaseResult<Void> result = quotaService.allotQuotaForDownEa("85188", "85188", 1000, 1000);
        System.out.println(JSONObject.toJSONString(result));
    }

}
