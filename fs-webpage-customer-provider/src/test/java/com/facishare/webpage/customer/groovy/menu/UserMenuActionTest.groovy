package com.facishare.webpage.customer.groovy.menu

import com.facishare.cep.plugin.enums.ClientTypeEnum
import com.facishare.webpage.customer.api.model.UserMenuItem
import com.facishare.webpage.customer.controller.model.UserMenuTempleVo
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenusArg
import com.google.common.collect.Maps

/**
 * Created by zhangyu on 2020/4/1
 */
class UserMenuActionTest extends MenuBasicTest {

    def "all_menu - web端请求"() {
        given:
        def userMenusArg = new GetUserMenusArg()
        when:
        def result = userMenuAction.getUserMenus(buildUserInfo(), buildClientInfo(ClientTypeEnum.Web), userMenusArg)
        then:
        List<UserMenuTempleVo> userMenuTempleVos = result.userMenuTempleVos
        //判断菜单条数是否相等
        userMenuTempleVos.size() == 2
        Map<String, List<UserMenuItem>> map = Maps.newHashMap()
        for (UserMenuTempleVo userMenuTempleVo : userMenuTempleVos) {
            map.put(userMenuTempleVo.getId(), userMenuTempleVo.getItems())
        }

        Map<String, UserMenuItem> systemUserMenuItemsMap = Maps.newHashMap()
        for (UserMenuItem userMenuItem : map.get(systemMenuId)) {
            systemUserMenuItemsMap.put(userMenuItem.getReferenceApiname(), userMenuItem)
        }

        //系统级级菜单隐藏自定义对象，个人级显示
        def systemUdObjMenuHidden = systemUserMenuItemsMap.get(hiddenSystemUdObj)
        systemUdObjMenuHidden == null

        def systemAddBPMMonitor = systemUserMenuItemsMap.get(addBPMMonitor)
        systemAddBPMMonitor != null

        def systemAddUDTest__c = systemUserMenuItemsMap.get(addUDTest__c)
        systemAddUDTest__c != null

        def hiddenGroup = systemUserMenuItemsMap.get(hiddenGroup)
        hiddenGroup == null


        Map<String, UserMenuItem> tenantUserMenuItemsMap = Maps.newHashMap()
        for (UserMenuItem userMenuItem : map.get(tenantMenuId)) {
            tenantUserMenuItemsMap.put(userMenuItem.getReferenceApiname(), userMenuItem)
        }
        //租户级菜单：个人级隐藏
        def tenantUserMenuHidden = tenantUserMenuItemsMap.get(hiddenTenantPreObj)
        //判断个人级隐藏是否生效
        tenantUserMenuHidden.getIsHidden() == true

        def tenantUdObjMenuHidden = tenantUserMenuItemsMap.get(hiddenTenantUdObj)
        tenantUdObjMenuHidden == null

        def tenantAddBPMMonitor1 = tenantUserMenuItemsMap.get(addBPMMonitor)
        tenantAddBPMMonitor1 != null

        def tenantAddUDTest__c1 = tenantUserMenuItemsMap.get(addUDTest__c)
        tenantAddUDTest__c1 != null
    }

    def "all_menu - 终端端请求"() {
        given:
        def userMenusArg = new GetUserMenusArg()
        when:
        def result = userMenuAction.getUserMenus(buildUserInfo(), buildClientInfo(ClientTypeEnum.iOS), userMenusArg)
        then:
        List<UserMenuTempleVo> userMenuTempleVos = result.userMenuTempleVos
        //判断菜单条数是否相等
        userMenuTempleVos.size() == 2
        Map<String, List<UserMenuItem>> map = Maps.newHashMap()
        for (UserMenuTempleVo userMenuTempleVo : userMenuTempleVos) {
            map.put(userMenuTempleVo.getId(), userMenuTempleVo.getItems())
        }
        Map<String, UserMenuItem> systemUserMenuItemsMap = Maps.newHashMap()
        for (UserMenuItem userMenuItem : map.get(systemMenuId)) {
            systemUserMenuItemsMap.put(userMenuItem.getReferenceApiname(), userMenuItem)
        }

        //系统级级菜单隐藏自定义对象，个人级显示
        def systemUdObjMenuHidden = systemUserMenuItemsMap.get(hiddenSystemUdObj)
        systemUdObjMenuHidden == null

        def systemAddCRMRemind = systemUserMenuItemsMap.get(addCRMRemind)
        systemAddCRMRemind != null

        def systemAddCRMTodo = systemUserMenuItemsMap.get(addCRMTodo)
        systemAddCRMTodo != null

        def hiddenGroup = systemUserMenuItemsMap.get(hiddenGroup)
        hiddenGroup == null

        def addGroup = systemUserMenuItemsMap.get("group-111")
        addGroup != null

        addGroup.getNumber() == systemUserMenuItemsMap.get("group-a6494bf9-525f-4b40-9390-20d7d187cfce").getNumber() + 1
    }
}
