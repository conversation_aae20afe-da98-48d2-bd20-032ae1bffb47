package com.facishare.webpage.customer.controller.impl;

import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.model.Key;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Map;

/**
 * Created by zhangyu on 2020/3/5
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test-1.xml")
public class I18NServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private QixinI18nService qixinI18nService;

    @Test
    public void testI18N(){
        Key key = new Key("group_group-20ed2442-da6d-429d-8fa9-bda9c475b07c", "我");
        Map<String, String> i118Result = qixinI18nService.getMultiI18nValueDefault(0L, Lists.newArrayList(key), Locale.ENGLISH);
        System.out.println(i118Result.get("group_group-20ed2442-da6d-429d-8fa9-bda9c475b07c"));
    }

}
