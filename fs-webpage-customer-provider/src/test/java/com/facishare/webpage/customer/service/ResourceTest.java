package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.core.resource.BiReportResource;
import com.facishare.webpage.customer.api.model.GetDashBoardList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/8 11:33 AM
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:fs-webpage-customer-rest-remote.xml")
public class ResourceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private BiReportResource biReportResource;

    @Test
    public void getDashBoardList() {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-enterprise-id", "71574");
        headers.put("x-fs-employee-id", "1017");
        headers.put("x-fs-enterprise-account", "71574");
        headers.put("accept-language", Locale.ENGLISH.toLanguageTag());

        GetDashBoardList.Arg arg = GetDashBoardList.Arg.builder().
                dashboardIds(Lists.newArrayList("BI_DSB_5dd7a910fe6ddb0001499999")).
                build();

      /*  GetDashBoardList.Result result = biReportResource.getDashBoardList(headers, arg);
        System.out.println(result.getData().getDashboardList().get(0).getName());*/
    }
}
