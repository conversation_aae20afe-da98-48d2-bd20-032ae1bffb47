package com.facishare.webpage.customer.groovy.paas

import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.api.service.UserWebMainChannelService
import com.facishare.webpage.customer.controller.TenantWebMainChannelAction
import com.facishare.webpage.customer.controller.impl.TenantWebMainChannelActionImpl
import com.facishare.webpage.customer.dao.*
import com.facishare.webpage.customer.dao.entity.*
import com.facishare.webpage.customer.metadata.ConfigWebAppService
import com.facishare.webpage.customer.metadata.CrossWebAppService
import com.facishare.webpage.customer.metadata.InnerWebAppService
import com.facishare.webpage.customer.metadata.PaaSWebAppService
import com.facishare.webpage.customer.rest.UserWebMainChannelRestAction
import com.facishare.webpage.customer.service.WebMainChannelService
import com.facishare.webpage.customer.service.impl.WebMainChannelServiceImpl
import com.google.common.collect.Lists

import java.util.stream.Collectors

/**
 * Created by zhangyu on 2020/11/30
 */
class WebMainChannelBasic extends ApplicationBasic {

    public UserWebMainChannelService userWebMainChannelService

    public TenantWebMainChannelAction tenantWebMainChannelAction

    public WebMainChannelService webMainChannelService

    public UserMainChannelDao userMainChannelDao

    public MainChannelEntityDao mainChannelEntityDao

    public TenantMainChannelDao tenantMainChannelDao

    public ConfigWebAppService configWebAppService

    public CrossWebAppService crossWebAppService

    public InnerWebAppService innerWebAppService

    public PaaSWebAppService paaSWebAppService


    def setup() {
        userMainChannelDao = new UserMainChannelDaoImpl()
        userMainChannelDao.datastore = datastore

        mainChannelEntityDao = new MainChannelEntityDaoImpl()
        mainChannelEntityDao.datastore = datastore

        tenantMainChannelDao = new TenantMainChannelDaoImpl()
        tenantMainChannelDao.datastore = datastore

        configWebAppService = new ConfigWebAppService()
        configWebAppService.appMenuConfig = appMenuConfig
        configWebAppService.menusConfig = menusConfig
        configWebAppService.languageService = languageService
        configWebAppService.openFsUserAppViewService = openFsUserAppViewService
        configWebAppService.permissionUnifiedService = permissionUnifiedService

        crossWebAppService = new CrossWebAppService()
        crossWebAppService.applicationService = applicationService

        innerWebAppService = new InnerWebAppService()
        innerWebAppService.applicationService = applicationService

        paaSWebAppService = new PaaSWebAppService()
        paaSWebAppService.paaSAppService = paaSAppService
        paaSWebAppService.permissionUnifiedService = permissionUnifiedService
        paaSWebAppService.appMenuConfig = appMenuConfig


        webMainChannelService = new WebMainChannelServiceImpl()
        webMainChannelService.userMainChannelDao = userMainChannelDao
        webMainChannelService.tenantMainChannelDao = tenantMainChannelDao
        webMainChannelService.mainChannelEntityDao = mainChannelEntityDao
        webMainChannelService.remoteService = remoteService
        webMainChannelService.searchWordsService = searchWordsService
        webMainChannelService.menusConfig = menusConfig
        webMainChannelService.webMainChannelConfig = webMainChannelConfig
        webMainChannelService.webAppServices = Lists.newArrayList(configWebAppService, crossWebAppService, innerWebAppService, paaSWebAppService)

        tenantWebMainChannelAction = new TenantWebMainChannelActionImpl()
        tenantWebMainChannelAction.webMainChannelService = webMainChannelService

        userWebMainChannelService = new UserWebMainChannelRestAction()
        userWebMainChannelService.webMainChannelService = webMainChannelService
        userWebMainChannelService.remoteService = remoteService

        createTenantMainChannelEntity()
        createUserMainChannelEntity()
        createOldMainChannelEntity()
    }

    def createOldMainChannelEntity() {
        def mainChannelEntity = new MainChannelEntity()
        mainChannelEntity.tenantId = 78810
        mainChannelEntity.name = "厂商门户"
        mainChannelEntity.mainChannelId = "78810_123"
        mainChannelEntity.sourceType = "FSAID_11491009"
        mainChannelEntity.creatorId = -10000
        mainChannelEntity.createTime = System.currentTimeMillis()
        mainChannelEntity.updateId = -10000
        mainChannelEntity.updateTime = System.currentTimeMillis()
        mainChannelEntity.effectiveEmployeeIds = Lists.newArrayList(1000, 1001, 1002)
        mainChannelEntity.mainChannelMenus = buildMainChannelMenuEntityList()

        mainChannelEntityDao.findAndModify(mainChannelEntity)

    }

    private List<MainChannelMenuEntity> buildMainChannelMenuEntityList() {
        def workCircleEntity = new MainChannelMenuEntity()
        workCircleEntity.menuId = "WORK_CIRCLE"

        def CRMEntity = new MainChannelMenuEntity()
        CRMEntity.menuId = "CRM"

        def APP_CENTREEntity = new MainChannelMenuEntity()
        APP_CENTREEntity.menuId = "APP_CENTRE"

        def PortalEntity = new MainChannelMenuEntity()
        PortalEntity.menuId = "Portal"

        return Lists.newArrayList(workCircleEntity, CRMEntity, APP_CENTREEntity, PortalEntity)
    }


    def createTenantMainChannelEntity() {
        def tenantMainChannelEntity = new TenantMainChannelEntity()
        tenantMainChannelEntity.tenantId = 78810
        tenantMainChannelEntity.apiName = "default__c"
        tenantMainChannelEntity.sourceType = SourceType.SYSTEM
        tenantMainChannelEntity.webMainChannelMenuEntityList = buildWebMainChannelMenuEntityList()
        tenantMainChannelEntity.creatorId = 1000
        tenantMainChannelEntity.createTime = System.currentTimeMillis()
        tenantMainChannelEntity.updaterId = 1000
        tenantMainChannelEntity.updateTime = System.currentTimeMillis()
        tenantMainChannelDao.saveTenantMainChannelEntity(tenantMainChannelEntity)
    }

    def createUserMainChannelEntity() {
        def userMainChannelEntity = new UserMainChannelEntity()
        userMainChannelEntity.tenantId = 78810
        userMainChannelEntity.apiName = "default__c"
        userMainChannelEntity.employeeId = 1020
        userMainChannelEntity.createTime = System.currentTimeMillis()
        userMainChannelEntity.updateTime = System.currentTimeMillis()
        userMainChannelEntity.webMainChannelMenuEntityList = buildWebMainChannelMenuEntityList()
        return userMainChannelEntity
    }


    private List<WebMainChannelMenuEntity> buildWebMainChannelMenuEntityList() {
        def channelMenuVOList = webMainChannelService.getTenantMainChannelMenuVOList(78810, "78810", 1000, Locale.CHINA, null)
        def webMainChannelVO = channelMenuVOList.get(0)
        return webMainChannelVO.getMainChannelMenuVOList().stream().map({ x ->
            def entity = new WebMainChannelMenuEntity()
            entity.appId = x.appId
            entity.upEnterpriseAccount = x.upEnterpriseAccount
            if (x.appId.equals("")){
                entity.hidden = true
            }else {
                entity.hidden = false
            }
            return entity
        }).collect(Collectors.toList())
    }


}
