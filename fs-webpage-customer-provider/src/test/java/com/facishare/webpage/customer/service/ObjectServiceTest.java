package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.core.model.MenuCollectionType;
import com.facishare.webpage.customer.metadata.ObjectMetaDataService;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.ObjectRecordData;
import com.facishare.webpage.customer.remote.ObjectService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/11/5
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class ObjectServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private ObjectService objectService;

    @Autowired
    private ObjectMetaDataService objectMetaDataService;

    @Test
    public void getObjectRecordDataList() {
        List<ObjectRecordData> objectRecordDataList = objectService.getObjectRecordDataList(71574, Lists.newArrayList("AccountObj"), Locale.CHINA);
        System.out.println("-----------------------------");
        System.out.println(objectRecordDataList);
        System.out.println("-----------------------------");
    }

    @Test
    public void getCustomerLinkAppObjects() {
        List<MetaMenuData> list = objectMetaDataService.getCustomerLinkAppObjects(DataSourceEnv.CROSS, 78810, "CROSS_PaaS", MenuCollectionType.ALL_TYPE, Locale.CHINA, "FSAID_1149103d");
        System.out.println(JSONObject.toJSONString(list));
        System.out.println("-----------------------------");
    }
}
