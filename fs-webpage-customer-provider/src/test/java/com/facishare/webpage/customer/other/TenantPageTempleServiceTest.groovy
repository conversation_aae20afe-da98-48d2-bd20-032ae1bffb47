package com.facishare.webpage.customer.other

import com.facishare.organization.api.model.employee.BatchGetSimpleEmployeeDto
import com.facishare.organization.api.model.employee.SimpleEmployeeDto
import com.facishare.webpage.customer.api.model.PageTemplate
import com.facishare.webpage.customer.controller.TenantPageTempleAction
import com.facishare.webpage.customer.controller.impl.TenantPageTempleActionImpl
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetPageTemplateByIdArg
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetWebPageTemplesArg
import com.google.common.collect.Lists
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.ObjectUtils

/**
 * Created by z<PERSON><PERSON> on 2019/9/21
 */
class TenantPageTempleServiceTest extends PageTempleBaseTest {
    private TenantPageTempleAction tenantPageTempleAction
    private String expectTemple = "{\n" +
            "            \"templeId\":\"827c8577-5a86-4a6a-b348-a1b49bff7a0e\",\n" +
            "            \"tenantId\":71568,\n" +
            "            \"name\":\"demo1\",\n" +
            "            \"appId\":\"FSAID_11490d9e\",\n" +
            "            \"type\":\"web\",\n" +
            "            \"webMenuId\":\"2cbe70cc-d559-4d87-916e-75ce7ef8d9a0\",\n" +
            "            \"webPageId\":\"FSAID_11490d9e_09d453f8-1642-48c6-b8da-c72912327e33\",\n" +
            "            \"description\":\"\",\n" +
            "            \"scopes\":[\n" +
            "                \"5b6817bbe4b066655a6397e4\"\n" +
            "            ],\n" +
            "            \"creatorId\":1000,\n" +
            "            \"createTime\":1572340385995,\n" +
            "            \"updaterId\":1000,\n" +
            "            \"updateTime\":1572349836000,\n" +
            "            \"status\":0,\n" +
            "            \"system\":false\n" +
            "        }"


    def "getPageTemplateById"() {
        given:
        tenantPageTempleAction = new TenantPageTempleActionImpl()
        tenantPageTempleAction.tenantPageTempleBaseService = tenantPageTempleBaseService
        tenantPageTempleAction.remoteService = remoteService
        tenantPageTempleAction.homePageService = homePageService
        tenantPageTempleAction.tenantMenuApiService = tenantMenuApiService
        tenantPageTempleAction.pageTemplateService = pageTemplateService
        PageTemplate expectPageTemplate = buiExpectPageTemple()
        when:
        def arg = new GetPageTemplateByIdArg()
        arg.setTempleId(templeId1)
        def result = tenantPageTempleAction.getPageTemplateById(userInfo, arg)
        then:
        ObjectUtils.equals(expectPageTemplate, result.pageTemplate)

    }

    def "getWebPageTemples" (){
        given:
        tenantPageTempleAction = new TenantPageTempleActionImpl()
        tenantPageTempleAction.tenantPageTempleBaseService = tenantPageTempleBaseService
        tenantPageTempleAction.remoteService = remoteService
        tenantPageTempleAction.homePageService = homePageService
        tenantPageTempleAction.tenantMenuApiService = tenantMenuApiService
        tenantPageTempleAction.pageTemplateService = pageTemplateService
        List<PageTemplate> expectEebPageTemplateList = Lists.newArrayList(buiExpectPageTemple())

        BatchGetSimpleEmployeeDto.Result batchGetSimpleEmployeeDto = new BatchGetSimpleEmployeeDto.Result()
        List<SimpleEmployeeDto> employeeDtos = Lists.newArrayList()
        employeeDtos.add(simpleEmployeeDto)
        batchGetSimpleEmployeeDto.setEmployeeDtos(employeeDtos)
        employeeProviderService.batchGetSimpleEmployeeDto(_)>>>[batchGetSimpleEmployeeDto]

        when:
        def arg = new GetWebPageTemplesArg()
        arg.setAppId(appId)
        def result = tenantPageTempleAction.getWebPageTemples(userInfo, arg)
        then:
        CollectionUtils.isEqualCollection(expectEebPageTemplateList, result.webPageTemplateList)
    }

    def "saveVendorWebPageTemplate"(){

    }

    private PageTemplate buiExpectPageTemple() {
        PageTemplate expectPageTemplate = new PageTemplate()
        expectPageTemplate.setTempleId(templeId1)
        expectPageTemplate.setTenantId(userInfo.getEnterpriseId())
        expectPageTemplate.setName(templeName1)
        expectPageTemplate.setDescription(description1)
        expectPageTemplate.setAppId(appId)
        expectPageTemplate.setType(page_type_web)
        expectPageTemplate.setWebMenuId(webMenuId)
        expectPageTemplate.setWebPageId(webPageId)
        expectPageTemplate.setCreatorId(userInfo.getEmployeeId())
        expectPageTemplate.setUpdaterId(userInfo.getEmployeeId())
        expectPageTemplate.setCreateTime(time)
        expectPageTemplate.setUpdateTime(time)
        expectPageTemplate.setCreateName(employeeName)
        expectPageTemplate.setUpdaterName(employeeName)
        expectPageTemplate.setSystem(true)
        expectPageTemplate.setScopeNames(Lists.newArrayList())
        return expectPageTemplate
    }
}
