package com.facishare.webpage.customer.groovy.layout

import com.alibaba.fastjson.JSONObject
import com.facishare.qixin.common.utils.FastJsonSerializer
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService
import com.facishare.qixin.sysdb.serivce.DataSourceService
import com.facishare.qixin.sysdb.serivce.SystemDataService
import com.facishare.qixin.sysdb.serivce.SystemDataServiceImpl
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.api.constant.Status
import com.facishare.webpage.customer.api.model.LayoutType
import com.facishare.webpage.customer.common.EmployeeConfigCommonService
import com.facishare.webpage.customer.common.EmployeeConfigCommonServiceImpl
import com.facishare.webpage.customer.component.ComponentService
import com.facishare.webpage.customer.component.ComponentsCovertService
import com.facishare.webpage.customer.component.CovertCustomerManage
import com.facishare.webpage.customer.component.impl.ComponentServiceImpl
import com.facishare.webpage.customer.component.impl.ComponentsCovertServiceImpl
import com.facishare.webpage.customer.component.impl.CovertCustomerManageImpl
import com.facishare.webpage.customer.config.ComponentConfig
import com.facishare.webpage.customer.config.DefaultTenantConfig
import com.facishare.webpage.customer.config.HomePageMaxConfig
import com.facishare.webpage.customer.api.constant.EmployeeConstant
import com.facishare.webpage.customer.constant.WebPageConstants
import com.facishare.webpage.customer.controller.ComponentAction
import com.facishare.webpage.customer.controller.TenantHomePageAction
import com.facishare.webpage.customer.controller.UserHomePageAction
import com.facishare.webpage.customer.controller.impl.ComponentActionImpl
import com.facishare.webpage.customer.controller.impl.TenantHomePageActionImpl
import com.facishare.webpage.customer.controller.impl.UserHomePageActionImpl
import com.facishare.webpage.customer.core.business.ComponentListManager
import com.facishare.webpage.customer.core.component.ComponentCovertService
import com.facishare.webpage.customer.core.config.ComponentNameConfig
import com.facishare.webpage.customer.core.util.BIUrlUtil
import com.facishare.webpage.customer.dao.*
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity
import com.facishare.webpage.customer.groovy.MockResource
import com.facishare.webpage.customer.metadata.ApplicationService
import com.facishare.webpage.customer.api.model.EmployeeConfig
import com.facishare.webpage.customer.remote.ObjectService
import com.facishare.webpage.customer.service.EmployeeConfigBaseService
import com.facishare.webpage.customer.service.HomePageBaseService
import com.facishare.webpage.customer.service.HomePageCommonService
import com.facishare.webpage.customer.service.UserHomePageBaseService
import com.facishare.webpage.customer.service.impl.EmployeeConfigBaseServiceImpl
import com.facishare.webpage.customer.service.impl.HomePageBaseServiceImpl
import com.facishare.webpage.customer.service.impl.HomePageCommonServiceImpl
import com.facishare.webpage.customer.service.impl.UserHomePageBaseServiceImpl
import com.facishare.webpage.customer.system.datasource.HomePageDataServiceImpl
import com.facishare.webpage.customer.util.TempleIdUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps

import javax.annotation.Resource

/**
 * Created by zhangyu on 2020/3/30
 */
class HomePageBasicTest extends MockResource {

    public HomePageLayoutDao homePageLayoutDao

    public HomePageBaseService homePageBaseService

    public UserHomePageAction userHomePageAction

    public TenantHomePageAction tenantHomePageAction

    public HomePageCommonService homePageCommonService

    public SystemDataService homePageSystemDataService

    public EmployeeConfigDao employeeConfigDao

    public ComponentCovertService componentCovertService

    public EmployeeConfigBaseService employeeConfigBaseService

    public EmployeeConfigCommonService employeeConfigCommonService

    public ComponentsCovertService componentsCovertService

    public UserHomePageBaseService userHomePageBaseService

    public EmployeeCurrentHomePageLayoutDao employeeCurrentHomePageLayoutDao

    public ComponentService componentService

    public CovertCustomerManage covertCustomerManage

    public DataSourceService homePageDataService

    public ComponentAction componentAction


    def objectService = Mock(ObjectService.class) {
        getDisplayName(_, _) >>> Maps.newHashMap()
    }

    def homePageMaxConfig = Mock(HomePageMaxConfig.class) {
        getNum(_, _) >> 0
    }

    def defaultTenantConfig = Mock(DefaultTenantConfig.class) {
        getDefaultTenantConfig() >> Lists.newArrayList()
    }

    def paasOrgGroupService = Mock(PaasOrgGroupService.class) {
        getTemplateId(_, _, _) >> "111"
    }

    def componentNameConfig = Mock(ComponentNameConfig.class) {
        getComponentName(_) >> "biCard"
    }

    def applicationService = Mock(ApplicationService.class) {
        getWebManAppDataList(_, _, _, _) >> Lists.newArrayList()
        getUserWebAppDataList(_, _, _, _, _, _, _) >> Lists.newArrayList()
    }

    def componentConfig = Mock(ComponentConfig.class) {
        getAppShowComponents() >> Lists.newArrayList()
        getWidgetTypeMap() >> Maps.newHashMap()
    }

    def biUrlUtil = Mock(BIUrlUtil.class) {
        biUrlUtil.buildUrl(_, _) >> ""
    }


    @Resource
    private ComponentListManager componentListManager

    public String layoutId = TempleIdUtil.buildId(tenantId)
    public String vendorLayoutId = TempleIdUtil.buildId(tenantId)

    public String appId = WebPageConstants.APP_CRM
    public String vendorAppId = "FSAID_11491009"

    public int appType = BizType.CUSTOMER.type
    public String apiName = "customerLayout__c"
    public String customerAppId = "PortalPage"
    public int layoutType = LayoutType.ENTERPRISE
    public List<String> scopes = Lists.newArrayList("D-999999");
    public String homePageCard1 = "{\n" +
            "    \"CardID\":\"BI_5a68724c37aa1b3d48658337\",\n" +
            "    \"Type\":1,\n" +
            "    \"Title\":\"预测\",\n" +
            "    \"Row\":2,\n" +
            "    \"Column\":0,\n" +
            "    \"Width\":8,\n" +
            "    \"Height\":1,\n" +
            "    \"MobileHeight\":1,\n" +
            "    \"Order\":5\n" +
            "}"
    public String homePageCard2 = "{\n" +
            "    \"CardID\":\"BI_5a68730437aa1b3d48658351\",\n" +
            "    \"Type\":1,\n" +
            "    \"Title\":\"订单统计\",\n" +
            "    \"Row\":4,\n" +
            "    \"Column\":0,\n" +
            "    \"Width\":8,\n" +
            "    \"Height\":1,\n" +
            "    \"MobileHeight\":3,\n" +
            "    \"Order\":7\n" +
            "}"
    public String homePageCard3 = "{\n" +
            "    \"CardID\":\"BI_SaleReport\",\n" +
            "    \"Type\":1,\n" +
            "    \"Title\":\"销售简报\",\n" +
            "    \"Row\":0,\n" +
            "    \"Column\":0,\n" +
            "    \"Width\":8,\n" +
            "    \"Height\":1,\n" +
            "    \"MobileHeight\":5,\n" +
            "    \"Order\":1\n" +
            "}"
    public String homePageCard4 = "{\n" +
            "    \"CardID\":\"PS_CluesInto\",\n" +
            "    \"Type\":1,\n" +
            "    \"Title\":\"线索转化\",\n" +
            "    \"Row\":3,\n" +
            "    \"Column\":8,\n" +
            "    \"Width\":8,\n" +
            "    \"Height\":1,\n" +
            "    \"MobileHeight\":0,\n" +
            "    \"Order\":6\n" +
            "}"
    public String customerLayoutJson = "{\"layout\":[{\"components\":[[\"filters\"]],\"columns\":[{\"width\":\"100%\"}]},{\"components\":[[\"BI_SaleReport-CRM\",\"BI_5a68724c37aa1b3d48658337-CRM\",\"BI_5a68730437aa1b3d48658351-CRM\",\"BI_5a68738337aa1b3d4865836e-CRM\",\"BI_5a6873f237aa1b3d4865838b-CRM\",\"BI_595e213437aa1badec9778e6-CRM\",\"PS_ToExpireContract-CRM\",\"PS_CustomerInfoNeedtobeImproved-CRM\"],[\"BI_5a6871d037aa1b3d4865831d-CRM\",\"PS_CluesInto-CRM\",\"BI_SalesAssistant\",\"PS_BeRetractedCustomer-CRM\",\"PS_ForgetCustomerRemind-CRM\",\"PS_BirthdayCare-CRM\",\"PS_Tool-CRM\"]],\"columns\":[{\"width\":\"66%\"},{\"width\":\"34%\"}]}],\"components\":[{\"dataId\":\"BI_595e213437aa1badec9778e6\",\"api_name\":\"BI_595e213437aa1badec9778e6-CRM\",\"cardId\":\"BI_595e213437aa1badec9778e6\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"销售阶段转化分析\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_595e213437aa1badec9778e6\",\"limit\":1.0},{\"dataId\":\"PS_CustomerInfoNeedtobeImproved\",\"api_name\":\"PS_CustomerInfoNeedtobeImproved-CRM\",\"cardId\":\"PS_CustomerInfoNeedtobeImproved\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"客户资料需要完善\",\"type\":\"card\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"PS_CluesInto\",\"api_name\":\"PS_CluesInto-CRM\",\"cardId\":\"PS_CluesInto\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"线索转化\",\"type\":\"cluesInto\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"PS_BeRetractedCustomer\",\"api_name\":\"PS_BeRetractedCustomer-CRM\",\"cardId\":\"PS_BeRetractedCustomer\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"将被收回的客户\",\"type\":\"card\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"PS_ToExpireContract\",\"api_name\":\"PS_ToExpireContract-CRM\",\"cardId\":\"PS_ToExpireContract\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"即将到期的合同\",\"type\":\"card\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"BI_SaleReport\",\"api_name\":\"BI_SaleReport-CRM\",\"cardId\":\"BI_SaleReport\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"全盛浩的销售简报\",\"type\":\"saleReport\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/report/salesbrief\",\"limit\":1.0},{\"dataId\":\"PS_ForgetCustomerRemind\",\"api_name\":\"PS_ForgetCustomerRemind-CRM\",\"cardId\":\"PS_ForgetCustomerRemind\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"遗忘客户提醒\",\"type\":\"card\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"PS_BirthdayCare\",\"api_name\":\"PS_BirthdayCare-CRM\",\"cardId\":\"PS_BirthdayCare\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"生日关怀\",\"type\":\"card\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"BI_5a68738337aa1b3d4865836e\",\"api_name\":\"BI_5a68738337aa1b3d4865836e-CRM\",\"cardId\":\"BI_5a68738337aa1b3d4865836e\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"回款统计\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5a68738337aa1b3d4865836e\",\"limit\":1.0},{\"dataId\":\"PS_Tool\",\"api_name\":\"PS_Tool-CRM\",\"cardId\":\"PS_Tool\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"工具\",\"type\":\"tools\",\"tools\":[],\"url\":\"\",\"limit\":1.0},{\"dataId\":\"BI_5a6873f237aa1b3d4865838b\",\"api_name\":\"BI_5a6873f237aa1b3d4865838b-CRM\",\"cardId\":\"BI_5a6873f237aa1b3d4865838b\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"退款统计\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5a6873f237aa1b3d4865838b\",\"limit\":1.0},{\"dataId\":\"BI_5a68724c37aa1b3d48658337\",\"api_name\":\"BI_5a68724c37aa1b3d48658337-CRM\",\"cardId\":\"BI_5a68724c37aa1b3d48658337\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"商机预测\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5a68724c37aa1b3d48658337\",\"limit\":1.0},{\"dataId\":\"BI_5a68730437aa1b3d48658351\",\"api_name\":\"BI_5a68730437aa1b3d48658351-CRM\",\"cardId\":\"BI_5a68730437aa1b3d48658351\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"订单统计\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5a68730437aa1b3d48658351\",\"limit\":1.0},{\"dataId\":\"BI_5a6871d037aa1b3d4865831d\",\"api_name\":\"BI_5a6871d037aa1b3d4865831d-CRM\",\"cardId\":\"BI_5a6871d037aa1b3d4865831d\",\"appId\":\"CRM\",\"filters\":[],\"title\":\"回款率(回款/目标)\",\"type\":\"biCard\",\"tools\":[],\"url\":\"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5a6871d037aa1b3d4865831d\",\"limit\":1.0},{\"buttons\":[],\"dataId\":\"BI_SalesAssistant\",\"api_name\":\"BI_SalesAssistant\",\"related_list_name\":\"\",\"cardId\":\"BI_SalesAssistant\",\"limit\":1.0,\"header\":\"销售助手\",\"nameI18nKey\":\"webpage_homepage.sales_assistant\",\"title\":\"销售助手\",\"type\":\"salesAssistant\"}]}"
    public String vendorLayoutJson = "{\"layout\":[{\"components\":[[\"filters\"]],\"columns\":[{\"width\":\"100%\"}]},{\"components\":[[\"navigate\",\"PS_CluesInto-FSAID_11490c84\",\"sceneCard_1593585094214\",\"BI_SaleReport-FSAID_11490c84\",\"BI_SaleReport-FSAID_11490d9e\"],[\"sceneCard_1593585054716\"]],\"columns\":[{\"width\":\"66%\"},{\"width\":\"34%\"}]}],\"components\":[{\"api_name\":\"navigate\",\"header\":\"导航组件\",\"localKeys\":[\"getComponentsDate\"],\"menus\":[{\"name\":\"通知公告\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490c83\"},{\"name\":\"订货通\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490c84\"},{\"name\":\"互联网盘\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490c82\"},{\"name\":\"代理通\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490d9e\"},{\"name\":\"互联网盘\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490c82\"},{\"name\":\"通知公告\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490c83\"},{\"name\":\"派工单\",\"icon\":\"https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201910_10_7de9fa48519f4cce8466cc558bc99d42.png&size=150_150&ea=appCenter\",\"menuType\":\"ENTERPRISE_RELATION\",\"id\":\"FSAID_11490d93\"}],\"limit\":1.0,\"type\":\"navigate\"},{\"buttons\":[],\"dataId\":\"PS_CluesInto\",\"api_name\":\"PS_CluesInto-FSAID_11490c84\",\"related_list_name\":\"\",\"cardId\":\"PS_CluesInto\",\"appId\":\"FSAID_11490c84\",\"header\":\"线索转化\",\"nameI18nKey\":\"webpage_homepage.cluesinfo\",\"title\":\"线索转化\",\"limit\":1.0,\"type\":\"cluesInto\"},{\"buttons\":[],\"sceneUrl\":\"/EM1HWebPage/HomePage/getOutTemplate\",\"related_list_name\":\"\",\"nameI18nKey\":\"webpage_homepage.senses\",\"filters\":[{\"FilterMainID\":\"5d8082978f98543c0de5ea3a\",\"ObjectApiName\":\"CheckRecordObj\",\"FilterKey\":\"5d8082978f98543c0de5ea3a\",\"FilterName\":\"全部\"}],\"title\":\"设备巡检 / 全部\",\"layoutCardId\":\"sceneCard_1593585054716\",\"dataId\":\"PS_Filter\",\"api_name\":\"sceneCard_1593585054716\",\"cardId\":\"PS_Filter\",\"appId\":\"FSAID_11490ea7\",\"objUrl\":\"/EM1HWebPage/HomePage/getObjectList\",\"header\":\"场景\",\"localKeys\":[\"objUrl\",\"sceneUrl\",\"parseObjResponseData\",\"createHandle\"],\"limit\":0.0,\"type\":\"sceneCard\"},{\"buttons\":[],\"sceneUrl\":\"/EM1HWebPage/HomePage/getOutTemplate\",\"related_list_name\":\"\",\"nameI18nKey\":\"webpage_homepage.senses\",\"title\":\"测试第一个自定义对象 / 全部\",\"dataId\":\"PS_Filter\",\"api_name\":\"sceneCard_1593585094214\",\"cardId\":\"PS_Filter\",\"appId\":\"FSAID_11490ea7\",\"objUrl\":\"/EM1HWebPage/HomePage/getObjectList\",\"header\":\"场景\",\"localKeys\":[\"objUrl\",\"sceneUrl\",\"parseObjResponseData\",\"createHandle\"],\"layoutCardId\":\"sceneCard_1593585094214\",\"filters\":[{\"ObjectApiName\":\"object_TZj1t__c\",\"FilterKey\":\"5d8082978f98543c0de5ea3a\",\"FilterMainID\":\"5d8082978f98543c0de5ea3a\",\"FilterName\":\"全部\"}],\"limit\":0.0,\"type\":\"sceneCard\"},{\"buttons\":[],\"dataId\":\"BI_SaleReport\",\"api_name\":\"BI_SaleReport-FSAID_11490c84\",\"related_list_name\":\"\",\"cardId\":\"BI_SaleReport\",\"appId\":\"FSAID_11490c84\",\"header\":\"销售简报\",\"nameI18nKey\":\"webpage_homepage.sale_report\",\"title\":\"销售简报\",\"limit\":1.0,\"type\":\"saleReport\"},{\"buttons\":[],\"dataId\":\"BI_SaleReport\",\"api_name\":\"BI_SaleReport-FSAID_11490d9e\",\"related_list_name\":\"\",\"cardId\":\"BI_SaleReport\",\"appId\":\"FSAID_11490d9e\",\"header\":\"销售简报\",\"nameI18nKey\":\"webpage_homepage.sale_report\",\"title\":\"销售简报\",\"limit\":1.0,\"type\":\"saleReport\"},{\"buttons\":[],\"dataId\":\"filters\",\"api_name\":\"filters\",\"related_list_name\":\"\",\"cardId\":\"filters\",\"header\":\"筛选器\",\"nameI18nKey\":\"webpage_homepage.filter\",\"title\":\"筛选器\",\"limit\":1.0,\"type\":\"filters\"}],\"filters\":[{\"filterData\":\"{\\\"empsAndDeps\\\":[{\\\"id\\\":1017,\\\"type\\\":0}],\\\"isAll\\\":false,\\\"startTime\\\":0,\\\"endTime\\\":0,\\\"dateId\\\":4,\\\"value\\\":{\\\"value1\\\":\\\"[{\\\\\\\"id\\\\\\\":1017,\\\\\\\"type\\\\\\\":\\\\\\\"p\\\\\\\"}]\\\"}}\",\"filterType\":\"selector\"},{\"filterData\":\"{\\\"empsAndDeps\\\":[{\\\"id\\\":1017,\\\"type\\\":0}],\\\"isAll\\\":false,\\\"startTime\\\":0,\\\"endTime\\\":0,\\\"dateId\\\":4,\\\"value\\\":{\\\"dateRangeID\\\":\\\"4\\\"}}\",\"filterType\":\"date\"}]}"

    public JSONObject customerLayout = JSONObject.parseObject(customerLayoutJson)
    public JSONObject vendorLayout = JSONObject.parseObject(vendorLayoutJson)

    public List<String> homePageCardEntityList = Lists.newArrayList(homePageCard1, homePageCard2, homePageCard3, homePageCard4)
    public String sourceType = SourceType.CUSTOMER
    public int status = Status.ENABLE
    public String name = "自定义页面"
    public String description = "默认首页"
    public boolean isChange = true
    public int creatorId = 1000
    public Date createTime = new Date()
    public int updaterId = 1000
    public Date updateTime = new Date()


    def setup() {

        setDatasource()

        setComponentCovertService()

        setComponentService()

        setUserHomePageBaseService()

        setEmployeeConfigBaseService()

        setEmployeeConfigCommonService()

        setHomePageCommonService()

        setHomePageDataService()

        setHomePageSystemDataService()

        setComponentsCovertService()

        setCovertCustomerManage()

        setHomePageBaseService()

        setUserHomePageAction()

        setTenantHomePageAction()

        setComponentAction()

        createMessage()

    }

    private void setDatasource() {
        homePageLayoutDao = new HomePageLayoutDaoImpl()
        homePageLayoutDao.datastore = datastore

        employeeConfigDao = new EmployeeConfigDaoImpl()
        employeeConfigDao.datastore = datastore

        employeeCurrentHomePageLayoutDao = new EmployeeCurrentHomePageLayoutDaoImpl()
        employeeCurrentHomePageLayoutDao.datastore = datastore
    }

    private void setComponentCovertService() {
        componentCovertService = new ComponentCovertService()
        componentCovertService.componentNameConfig = componentNameConfig
    }

    private void setComponentService() {
        componentService = new ComponentServiceImpl()
        componentService.componentListManager = componentListManager
        componentService.defaultTenantConfig = defaultTenantConfig
        componentService.componentNameConfig = componentNameConfig
        componentService.remoteService = remoteService
        componentService.languageService = languageService
        componentService.componentConfig = componentConfig
    }

    private void setUserHomePageBaseService() {
        userHomePageBaseService = new UserHomePageBaseServiceImpl()
        userHomePageBaseService.employeeCurrentHomePageLayoutDao = employeeCurrentHomePageLayoutDao
    }

    private void setEmployeeConfigBaseService() {
        employeeConfigBaseService = new EmployeeConfigBaseServiceImpl()
        employeeConfigBaseService.employeeConfigDao = employeeConfigDao
    }

    private void setEmployeeConfigCommonService() {
        employeeConfigCommonService = new EmployeeConfigCommonServiceImpl()
        employeeConfigCommonService.remoteService = remoteService
    }

    private void setHomePageCommonService() {
        homePageCommonService = new HomePageCommonServiceImpl()
        homePageCommonService.remoteService = remoteService
        homePageCommonService.employeeConfigCommonService = employeeConfigCommonService
        homePageCommonService.employeeConfigBaseService = employeeConfigBaseService
    }

    private void setHomePageDataService() {
        homePageDataService = new HomePageDataServiceImpl()
        homePageDataService.homePageLayoutDao = homePageLayoutDao
        homePageDataService.homePageCommonService = homePageCommonService
    }

    private void setHomePageSystemDataService() {
        homePageSystemDataService = new SystemDataServiceImpl()
        homePageSystemDataService.dataSourceService = homePageDataService
        homePageSystemDataService.sysTemplateConfig = sysTemplateConfig
    }

    private void setComponentsCovertService() {
        componentsCovertService = new ComponentsCovertServiceImpl()
        componentsCovertService.remoteService = remoteService
        componentsCovertService.homePageCommonService = homePageCommonService
        componentsCovertService.applicationService = applicationService
        componentsCovertService.paasOrgGroupService = paasOrgGroupService
    }

    private void setCovertCustomerManage() {
        covertCustomerManage = new CovertCustomerManageImpl()
        covertCustomerManage.componentsCovertService = componentsCovertService
        covertCustomerManage.homePageCommonService = homePageCommonService
    }

    private void setHomePageBaseService() {
        homePageBaseService = new HomePageBaseServiceImpl()
        homePageBaseService.homePageLayoutDao = homePageLayoutDao
        homePageBaseService.homePageCommonService = homePageCommonService
        homePageBaseService.componentNameConfig = componentNameConfig
        homePageBaseService.remoteService = remoteService
        homePageBaseService.objectService = objectService
        homePageBaseService.userHomePageBaseService = userHomePageBaseService
        homePageBaseService.homePageSystemDataService = homePageSystemDataService
        homePageBaseService.employeeConfigDao = employeeConfigDao
        homePageBaseService.componentNameConfig = componentNameConfig
        homePageBaseService.biUrlUtil = biUrlUtil
        homePageBaseService.paasOrgGroupService = paasOrgGroupService
        homePageBaseService.componentCovertService = componentCovertService
        homePageBaseService.covertCustomerManage = covertCustomerManage
        homePageBaseService.languageService = languageService
    }

    private void setUserHomePageAction() {
        userHomePageAction = new UserHomePageActionImpl()
        userHomePageAction.homePageBaseService = homePageBaseService
        userHomePageAction.webPageEventService = webPageEventService
        userHomePageAction.tenantPageTempleService = tenantPageTempleService
    }

    private void setTenantHomePageAction() {
        tenantHomePageAction = new TenantHomePageActionImpl()
        tenantHomePageAction.homePageBaseService = homePageBaseService
        tenantHomePageAction.remoteService = remoteService
        tenantHomePageAction.homePageMaxConfig = homePageMaxConfig
        tenantHomePageAction.permissionService = permissionService
        tenantHomePageAction.defaultTenantConfig = defaultTenantConfig
        tenantHomePageAction.paasOrgGroupService = paasOrgGroupService
        tenantHomePageAction.componentListManager = componentListManager
        tenantHomePageAction.objectService = objectService
        tenantHomePageAction.organizationCommonService = scopeService
        tenantHomePageAction.webPageEventService = webPageEventService
        tenantHomePageAction.userHomePageBaseService = userHomePageBaseService
        tenantHomePageAction.biUrlUtil = biUrlUtil
        tenantHomePageAction.languageService = languageService
        tenantHomePageAction.componentService = componentService
        tenantHomePageAction.componentConfig = componentConfig
    }

    private void setComponentAction() {
        componentAction = new ComponentActionImpl()
        componentAction.componentService = componentService
    }

    def createMessage() {
        def homePageLayoutEntity = new HomePageLayoutEntity()
        //CRM首页
        homePageLayoutEntity.layoutId = layoutId
        homePageLayoutEntity.layoutType = layoutType
        homePageLayoutEntity.status = status
        homePageLayoutEntity.scopes = scopes
        homePageLayoutEntity.description = description
        homePageLayoutEntity.name = name
        homePageLayoutEntity.appId = appId
        homePageLayoutEntity.appType = BizType.CRM.type
        homePageLayoutEntity.change = isChange
        homePageLayoutEntity.createTime = createTime
        homePageLayoutEntity.creatorId = creatorId
        homePageLayoutEntity.homePageCardEntityList = homePageCardEntityList
        homePageLayoutEntity.sourceType = sourceType
        homePageLayoutEntity.tenantId = tenantId
        homePageLayoutEntity.updaterId = updaterId
        homePageLayoutEntity.updateTime = updateTime
        homePageLayoutDao.findAndModifyHomePage(tenantId, homePageLayoutEntity)
        //自定义页面首页
        def entity = new HomePageLayoutEntity()
        entity.layoutId = TempleIdUtil.buildId(tenantId)
        entity.layoutType = LayoutType.ENTERPRISE
        entity.status = status
        entity.description = description
        entity.name = name
        entity.appId = customerAppId
        entity.appType = BizType.CUSTOMER.type
        entity.change = true
        entity.createTime = createTime
        entity.creatorId = creatorId
        entity.customerLayout = JSONObject.toJSONString(customerLayout)
        entity.sourceType = SourceType.CUSTOMER
        entity.tenantId = tenantId
        entity.updaterId = updaterId
        entity.updateTime = updateTime
        entity.dataVersion = 200
        homePageLayoutDao.findAndModifyHomePage(tenantId, apiName, entity)

        //app应用首页新数据结构保存
        def appHomePageLayoutEntity = new HomePageLayoutEntity()
        appHomePageLayoutEntity.layoutId = vendorLayoutId
        appHomePageLayoutEntity.layoutType = LayoutType.ENTERPRISE
        appHomePageLayoutEntity.status = status
        appHomePageLayoutEntity.description = description
        appHomePageLayoutEntity.name = name
        appHomePageLayoutEntity.appId = vendorAppId
        appHomePageLayoutEntity.appType = BizType.APP.type
        appHomePageLayoutEntity.change = true
        appHomePageLayoutEntity.createTime = createTime
        appHomePageLayoutEntity.creatorId = creatorId
        appHomePageLayoutEntity.customerLayout = JSONObject.toJSONString(vendorLayout)
        appHomePageLayoutEntity.sourceType = SourceType.CUSTOMER
        appHomePageLayoutEntity.tenantId = tenantId
        appHomePageLayoutEntity.updaterId = updaterId
        appHomePageLayoutEntity.updateTime = updateTime
        appHomePageLayoutEntity.dataVersion = 200
        homePageLayoutDao.findAndModifyHomePage(tenantId, appHomePageLayoutEntity)
    }

    EmployeeConfig getDefaultEmployeeConfig() {
        EmployeeConfig employeeConfig = new EmployeeConfig();

        EmployeeConstant.HomePageDefaultValue homePageDefaultValue = new EmployeeConstant.HomePageDefaultValue();
        homePageDefaultValue.setStartTime("0");
        homePageDefaultValue.setEndTime("0");
        homePageDefaultValue.setDateId(String.valueOf(4));
        homePageDefaultValue.setDateType("本月");
        homePageDefaultValue.setIsAll(true)

        employeeConfig.setKey(EmployeeConstant.HomePageDefault);
        employeeConfig.setValue(FastJsonSerializer.entityToJson(homePageDefaultValue));
        return employeeConfig;
    }

    EmployeeConfig getDefaultTenantEmployeeConfig() {
        EmployeeConfig employeeConfig = new EmployeeConfig();
        employeeConfig.setKey(EmployeeConstant.UserDefindSelect);
        employeeConfig.setValue("{\"enableEmpFilterOfGlobalFilter\":1,\"enableDateFilterOfGlobalFilter\":1}");
        return employeeConfig;
    }

}
