package com.facishare.webpage.customer.test;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.model.MenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/4/30
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class TestSortByMenuItems {

    @Test
    public void testSortMenuItems(){


        MenuData menuData1 = new MenuData();
        menuData1.setIsHidden(true);
        menuData1.setGroupApiName(null);
        menuData1.setOrderNumber(10);
        menuData1.setApiName("AccountObj");
        menuData1.setName("客户");

        MenuItem menuItem1 = new MenuItem();
        menuItem1.setMenuData(menuData1);
        menuItem1.setMenuItemType(MenuType.PRE_OBJ);

        MenuData menuData2 = new MenuData();
        menuData2.setIsHidden(true);
        menuData2.setGroupApiName(null);
        menuData2.setOrderNumber(10);
        menuData2.setApiName("NearByCustomer");
        menuData2.setName("附近客户");
        MenuItem menuItem2 = new MenuItem();
        menuItem2.setMenuData(menuData2);
        menuItem2.setMenuItemType(MenuType.NO_OBJ);

        List<MenuItem> menuItemList = Lists.newArrayList(menuItem2, menuItem1);

        System.out.println(menuItemList);

    }

    @Test
    public void testContain(){
        List<String> list = Lists.newArrayList("111");


        System.out.println(CollectionUtils.containsAny(list, Lists.newArrayList()));
    }

    @Test
    public void test(){
        System.out.println(111);
    }

    @Test
    public void testEquals() {
        Scope scope1 = new Scope();
        scope1.setDataId("111");
        scope1.setDataType(1);

        Scope scope2 = new Scope();
        scope2.setDataType(1);
        scope2.setDataId("111");

        List<Scope> scopeList = Lists.newArrayList(scope1, scope2);

        List<String> list = scopeList.stream().distinct().map(scope -> scope.getDataId()).collect(Collectors.toList());

        System.out.println(list);
    }

    @Test
    public void testEmpty() {
        List<String> scopeNames = Lists.newArrayList();
        String join = StringUtils.join(scopeNames, ",");
        System.out.println(join);
    }

    @Test
    public void sortByTime() {

        long a = 1000;

        long b = 2000;

        List<Long> list = Lists.newArrayList(b, a);

        List<Long> longList = list.stream().sorted((o1, o2) -> {
            if (o1 > o2) {
                return 1;
            } else if (o1 < o2) {
                return -1;
            } else {
                return 0;
            }
        }).collect(Collectors.toList());
        System.out.println(longList);

    }

    @Test
    public void testSet() {
        String a = "fktest001,fktest002,fktest003,fktest004,fktest005,fktest006,fktest007,fktest008,fktest009,fktest010,fktest011,fktest012,fktest013,fktest014,fktest015,fktest016,fktest017,fktest018,fktest019,fktest020,fktest021,fktest022,fktest023,fktest024,fktest025,fktest026,fktest027,fktest028,fktest029,fktest030,fktest031,fktest032,fktest033,fktest034,fktest035,fktest036,fktest037,fktest038,fktest039,fktest040,fktest041,fktest042,fktest043,fktest044,fktest045,fktest046,fktest047,fktest048,fktest049,fktest050,fktest051,fktest052,fktest053,fktest054,fktest055,fktest056,fktest057,fktest058,fktest059,fktest060,fktest061,fktest062,fktest063,fktest064,fktest065,fktest066,fktest067,fktest068,fktest069,fktest070,fktest071,fktest072,fktest073,fktest074,fktest075,fktest076,fktest077,fktest078,fktest081,fktest082,fktest083,fktest089,fktest090,fktest091,fktest092,fktest093,fktest094,fktest095,fktest096,fktest097,fktest098,fktest099,fktest100,fktest101,fktest102,fktest103,fktest104,fktest105,fktest106,fktest107,fktest108,fktest109,fktest110,fktest111,fktest112,fktest113,fktest114,fktest115,fktest116,fktest117,fktest118,fktest119,fktest120,fktest121,fktest122,fktest123,fktest124,fktest125,fktest126,fktest127,fktest128,fktest129,fktest130,fktest131,fktest132,fktest133,fktest134,fktest135,fktest136,fktest137,fktest138,fktest139,fktest140,fktest141,fktest142,fktest143,fktest144,fktest145,fktest146,fktest147,fktest148,fktest149,fktest150,fktest151,fktest152,fktest153,fktest154,fktest155,fktest156,fktest157,fktest158,fktest159,fktest160,fktest161,fktest162,fktest163,fktest164,fktest165,fktest166,fktest167,fktest168,fktest169,fktest170,fktest171,fktest172,fktest173,fktest174,fktest175,fktest176,fktest177,fktest178,fktest179,fktest180,fktest181,fktest182,fktest184,fktest185,fktest186,fktest187,fktest188,fktest189,fktest190,fktest191,fktest192,fktest193,fktest194,fktest195,fktest196,fktest197,fktest198,fktest199,fktest200,fktest201,fktest202,fktest203,fktest204,fktest205,fktest206,fktest207,fktest208,fktest209,fktest210,fktest211,fktest212,fktest213,fktest214,fktest215,fktest216,fktest218,fktest219,fktest220,fktest221,fktest222,fktest223,fktest224,fktest225,fktest226,fktest227,fktest228,fktest229,fktest230,fktest231,fktest232,fktest233,fktest234,fktest235,fktest236,fktest237,fktest238,fktest239,fktest240,fktest241,fktest242,fktest243,fktest244,fktest245,fktest246,fktest247,fktest248,fktest249,fktest250,fktest251,fktest252,fktest253,fktest254,fktest255,fktest256,fktest257,fktest258,fktest259,fktest260,fktest261,fktest262,fktest263,fktest264,fktest265,fktest266,fktest267,fktest268,fktest269,fktest270,fktest271,fktest272,fktest273,fktest274,fktest275,fktest276,fktest277,fktest278,fktest279,fktest280,fktest281,fktest282,fktest283,fktest284,fktest285,fktest286,fktest287,fktest288,fktest290,fktest291,fktest292,fktest294,fktest295,fktest296,fktest297,fktest298,fktest299,fktest1000,fktest1001,fktest1002,fktest1003,fktest1004,fktest1005,fktest1006,fktest1007,fktest1008,fktest1009,fktest1010,fktest1011,fktest1012,fktest1013,fktest1014,fktest1015,fktest1016,fktest1017,fktest1018,fktest1019,fktest1020,fktest1021,fktest1022,fktest1023,fktest1024,fktest1025,fktest1027,fktest1028,fktest1029,fktest1030,fktest1031,fktest1032,fktest1033,fktest1034,fktest1035,fktest1036,fktest1037,fktest1038,fktest1039,fktest1040,fktest1041,fktest1042,fktest1043,fktest1044,fktest1045,fktest1046,fktest1047,fktest1048,fktest1049,fktest1050,fktest1051,fktest1052,fktest1053,fktest1054,fktest1055,fktest1056,fktest1057,fktest1058,fktest1059,fktest1060,fktest1061,fktest1062,fktest1063,fktest1064,fktest1065,fktest1066,fktest1067,fktest1068,fktest1069,fktest1070,fktest1071,fktest1072,fktest1073,fktest1074,fktest1075,fktest1076,fktest1077,fktest1078,fktest1079,fktest1080,fktest1081,fktest1082,fktest1083,fktest1084,fktest1085,fktest1086,fktest1087,fktest1088,fktest1089,fktest1090,fktest1091,fktest1092,fktest1093,fktest1094,fktest1095,fktest1096,fktest1097,fktest1098,fktest1099,fktest1100,fktest1101,fktest1102,fktest1103,fktest1104,fktest1105,fktest1106,fktest1107,fktest1108,fktest1109,fktest1110,fktest1111,fktest1112,fktest1113,fktest1114,fktest1115,fktest1116,fktest1117,fktest1118,fktest1119,fktest1120,fktest1121,fktest1122,fktest1123,fktest1124,fktest1125,fktest1126,fktest1127,fktest1128,fktest1129,fktest1130,fktest1131,fktest1132,fktest1133,fktest1134,fktest1135,fktest1136,fktest1137,fktest1138,fktest1139,fktest1140,fktest1141,fktest1142,fktest1143,fktest1144,fktest1145,fktest1146,fktest1147,fktest1148,fktest1149,fktest1150,fktest1151,fktest1152,fktest1153,fktest1154,fktest1155,fktest1156,fktest1157,fktest1158,fktest1159,fktest1160,fktest1161,fktest1162,fktest1163,fktest1164,fktest1165,fktest1166,fktest1167,fktest1168,fktest1169,fktest1170,fktest1171,fktest1172,fktest1173,fktest1174,fktest1175,fktest1176,fktest1177,fktest1178,fktest1179,fktest1180,fktest1181,fktest1182,fktest1183,fktest1184,fktest1185,fktest1186,fktest1187,fktest1188,fktest1189,fktest1191,fktest1192,fktest1193,fktest1194,fktest1195,fktest1196,fktest1198,fktest1199,fktest1307,fktest1308,fktest1309,fktest1310,fktest1311,fktest1312,fktest1313,fktest1314,fktest1315,fktest1316,fktest1317,fktest1318,fktest1319,fktest1320,fktest1321,fktest1322,fktest1323,fktest1324,fktest1325,fktest1326,fktest1327,fktest1328,fktest1329,fktest1330,fktest1331,fktest1332,fktest1333,fktest1335,fktest1336,fktest1337,fktest1338,fktest1339,fktest1340,fktest1341,fktest1342,fktest1343,fktest1344,fktest1345,fktest1346,fktest1347,fktest1348,fktest1349,fktest1350,fktest1351,fktest1352,fktest1353,fktest1354,fktest1355,fktest1356,fktest1357,fktest1358,fktest1359,fktest1360,fktest1361,fktest1362,fktest1363,fktest1364,fktest1365,fktest1367,fktest1368,fktest1369,fktest1370,fktest1371,fktest1372,fktest1373,fktest1374,fktest1375,fktest1376,fktest1377,fktest1378,fktest1379,fktest1380,fktest1381,fktest1382,fktest1383,fktest1384,fktest1385,fktest1386,fktest1387,fktest1388,fktest1389,fktest1390,fktest1391,fktest1392,fktest1393,fktest1394,fktest1395,fktest1396,fktest1397,fktest1398,fktest1399,fktest1400,fktest1401,fktest1402,fktest1403,fktest1404,fktest1405,fktest1406,fktest1407,fktest1408,fktest1409,fktest1410,fktest1411,fktest1412,fktest1413,fktest1414,fktest1415,fktest1416,fktest1417,fktest1418,fktest1419,fktest1420,fktest1421,fktest1422,fktest1423,fktest1424,fktest1425,fktest1426,fktest1427,fktest1428,fktest1429,fktest1430,fktest1431,fktest1432,fktest1433,fktest1434,fktest1435,fktest1436,fktest1437,fktest1438,fktest1439,fktest1440,fktest1441,fktest1442,fktest1443,fktest1444,fktest1445,fktest1446,fktest1447,fktest1448,fktest1449,fktest1451,fktest1452,fktest1453,fktest1454,fktest1455,fktest1456,fktest1457,fktest1458,fktest1459,fktest1460,fktest1461,fktest1462,fktest1463,fktest1464,fktest1465,fktest1466,fktest1467,fktest1468,fktest1469,fktest1470,fktest1471,fktest1472,fktest1473,fktest1474,fktest1475,fktest1476,fktest1477,fktest1478,fktest1479,fktest1480,fktest1481,fktest1482,fktest1483,fktest1484,fktest1485,fktest1486,fktest1487,fktest1488,fktest1489,fktest1490,fktest1491,fktest1492,fktest1493,fktest1494,fktest1495,fktest1496,fktest1497,fktest1498,fktest1499,fktest1500,fktest1501,fktest1502,fktest1503,fktest1504,fktest1505,fktest1506,fktest1507,fktest1508,fktest1509,fktest1510,fktest1511,fktest1512,fktest1513,fktest1514,fktest1515,fktest1516,fktest1517,fktest1518,fktest1519,fktest1520,fktest1521,fktest1522,fktest1523,fktest1524,fktest1525,fktest1526,fktest1527,fktest1528,fktest1529,fktest1530,fktest1531,fktest1532,fktest1533,fktest1534,fktest1535,fktest1536,fktest1537,fktest1538,fktest1539,fktest1540,fktest1541,fktest1542,fktest1543,fktest1544,fktest1545,fktest1546,fktest1547,fktest1548,fktest1549,fktest1550,fktest1551,fktest1552,fktest1553,fktest1200,fktest1201,fktest1202,fktest1203,fktest1204,fktest1205,fktest1206,fktest1207,fktest1208,fktest1209,fktest1210,fktest1211,fktest1212,fktest1213,fktest1214,fktest1215,fktest1216,fktest1217,fktest1218,fktest1219,fktest1300,fktest1220,fktest1221,fktest1222,fktest1223,fktest1224,fktest1225,fktest1226,fktest1227,fktest1228,fktest1229,fktest1230,fktest1231,fktest1232,fktest1233,fktest1235,fktest1236,fktest1237,fktest1238,fktest1239,fktest1240,fktest1241,fktest1242,fktest1243,fktest1244,fktest1245,fktest1246,fktest1247,fktest1248,fktest1250,fktest1251,fktest1252,fktest1253,fktest1254,fktest1255,fktest1256,fktest1257,fktest1258,fktest1259,fktest1563,fktest1562,fktest1561,fktest1560,fktest1559,fktest1558,fktest1557,fktest1556,fktest1555,fktest1554,fktest1260,fktest1261,fktest1262,fktest1263,fktest1264,fktest1265,fktest1266,fktest1267,fktest1268,fktest1269,fktest1270,fktest1271,fktest1272,fktest1273,fktest1274,fktest1275,fktest1276,fktest1277,fktest1278,fktest1279,fktest1280,fktest1282,fktest1283,fktest1284,fktest1286,fktest1287,fktest1288,fktest1289,fktest1290,fktest1291,fktest1292,fktest1294,fktest1295,fktest1296,fktest1297,fktest1298,fktest1299,fktest1573,fktest1572,fktest1571,fktest1570,fktest1569,fktest1568,fktest1567,fktest1566,fktest1565,fktest1564,633347,603884,fktest079,fktest080,fktest084,fktest085,fktest086,fktest087,fktest088,fktest183,fktest217,fktest289,fktest293,fktest1026,fktest1190,fktest1197,fktest1366,fktest1334,fktest1450,fs,xinhaoxuan888,540053,jiaeda,syyfty,583193,ihengyou888,quan5666,dbzg9168,kscloudfxcrm,tieon2020,496004";
        String b = "496004,quan5666,583193,syyfty,jiaeda,540053,xinhaoxuan888,633347,603884,fs,583193,ihengyou888,quan5666,dbzg9168,kscloudfxcrm,tieon2020,496004,fktest001,fktest002,fktest003,fktest004,fktest005,fktest006,fktest007,fktest008,fktest009,fktest010,fktest011,fktest012,fktest013,fktest014,fktest015,fktest016,fktest017,fktest018,fktest019,fktest020,fktest021,fktest022,fktest023,fktest024,fktest025,fktest026,fktest027,fktest028,fktest029,fktest030,fktest031,fktest032,fktest033,fktest034,fktest035,fktest036,fktest037,fktest038,fktest039,fktest040,fktest041,fktest042,fktest043,fktest044,fktest045,fktest046,fktest047,fktest048,fktest049,fktest050,fktest051,fktest052,fktest053,fktest054,fktest055,fktest056,fktest057,fktest058,fktest059,fktest060,fktest061,fktest062,fktest063,fktest064,fktest065,fktest066,fktest067,fktest068,fktest069,fktest070,fktest071,fktest072,fktest073,fktest074,fktest075,fktest076,fktest077,fktest078,fktest081,fktest082,fktest083,fktest089,fktest090,fktest091,fktest092,fktest093,fktest094,fktest095,fktest096,fktest097,fktest098,fktest099,fktest100,fktest101,fktest102,fktest103,fktest104,fktest105,fktest106,fktest107,fktest108,fktest109,fktest110,fktest111,fktest112,fktest113,fktest114,fktest115,fktest116,fktest117,fktest118,fktest119,fktest120,fktest121,fktest122,fktest123,fktest124,fktest125,fktest126,fktest127,fktest128,fktest129,fktest130,fktest131,fktest132,fktest133,fktest134,fktest135,fktest136,fktest137,fktest138,fktest139,fktest140,fktest141,fktest142,fktest143,fktest144,fktest145,fktest146,fktest147,fktest148,fktest149,fktest150,fktest151,fktest152,fktest153,fktest154,fktest155,fktest156,fktest157,fktest158,fktest159,fktest160,fktest161,fktest162,fktest163,fktest164,fktest165,fktest166,fktest167,fktest168,fktest169,fktest170,fktest171,fktest172,fktest173,fktest174,fktest175,fktest176,fktest177,fktest178,fktest179,fktest180,fktest181,fktest182,fktest184,fktest185,fktest186,fktest187,fktest188,fktest189,fktest190,fktest191,fktest192,fktest193,fktest194,fktest195,fktest196,fktest197,fktest198,fktest199,fktest200,fktest201,fktest202,fktest203,fktest204,fktest205,fktest206,fktest207,fktest208,fktest209,fktest210,fktest211,fktest212,fktest213,fktest214,fktest215,fktest216,fktest218,fktest219,fktest220,fktest221,fktest222,fktest223,fktest224,fktest225,fktest226,fktest227,fktest228,fktest229,fktest230,fktest231,fktest232,fktest233,fktest234,fktest235,fktest236,fktest237,fktest238,fktest239,fktest240,fktest241,fktest242,fktest243,fktest244,fktest245,fktest246,fktest247,fktest248,fktest249,fktest250,fktest251,fktest252,fktest253,fktest254,fktest255,fktest256,fktest257,fktest258,fktest259,fktest260,fktest261,fktest262,fktest263,fktest264,fktest265,fktest266,fktest267,fktest268,fktest269,fktest270,fktest271,fktest272,fktest273,fktest274,fktest275,fktest276,fktest277,fktest278,fktest279,fktest280,fktest281,fktest282,fktest283,fktest284,fktest285,fktest286,fktest287,fktest288,fktest290,fktest291,fktest292,fktest294,fktest295,fktest296,fktest297,fktest298,fktest299,fktest1000,fktest1001,fktest1002,fktest1003,fktest1004,fktest1005,fktest1006,fktest1007,fktest1008,fktest1009,fktest1010,fktest1011,fktest1012,fktest1013,fktest1014,fktest1015,fktest1016,fktest1017,fktest1018,fktest1019,fktest1020,fktest1021,fktest1022,fktest1023,fktest1024,fktest1025,fktest1027,fktest1028,fktest1029,fktest1030,fktest1031,fktest1032,fktest1033,fktest1034,fktest1035,fktest1036,fktest1037,fktest1038,fktest1039,fktest1040,fktest1041,fktest1042,fktest1043,fktest1044,fktest1045,fktest1046,fktest1047,fktest1048,fktest1049,fktest1050,fktest1051,fktest1052,fktest1053,fktest1054,fktest1055,fktest1056,fktest1057,fktest1058,fktest1059,fktest1060,fktest1061,fktest1062,fktest1063,fktest1064,fktest1065,fktest1066,fktest1067,fktest1068,fktest1069,fktest1070,fktest1071,fktest1072,fktest1073,fktest1074,fktest1075,fktest1076,fktest1077,fktest1078,fktest1079,fktest1080,fktest1081,fktest1082,fktest1083,fktest1084,fktest1085,fktest1086,fktest1087,fktest1088,fktest1089,fktest1090,fktest1091,fktest1092,fktest1093,fktest1094,fktest1095,fktest1096,fktest1097,fktest1098,fktest1099,fktest1100,fktest1101,fktest1102,fktest1103,fktest1104,fktest1105,fktest1106,fktest1107,fktest1108,fktest1109,fktest1110,fktest1111,fktest1112,fktest1113,fktest1114,fktest1115,fktest1116,fktest1117,fktest1118,fktest1119,fktest1120,fktest1121,fktest1122,fktest1123,fktest1124,fktest1125,fktest1126,fktest1127,fktest1128,fktest1129,fktest1130,fktest1131,fktest1132,fktest1133,fktest1134,fktest1135,fktest1136,fktest1137,fktest1138,fktest1139,fktest1140,fktest1141,fktest1142,fktest1143,fktest1144,fktest1145,fktest1146,fktest1147,fktest1148,fktest1149,fktest1150,fktest1151,fktest1152,fktest1153,fktest1154,fktest1155,fktest1156,fktest1157,fktest1158,fktest1159,fktest1160,fktest1161,fktest1162,fktest1163,fktest1164,fktest1165,fktest1166,fktest1167,fktest1168,fktest1169,fktest1170,fktest1171,fktest1172,fktest1173,fktest1174,fktest1175,fktest1176,fktest1177,fktest1178,fktest1179,fktest1180,fktest1181,fktest1182,fktest1183,fktest1184,fktest1185,fktest1186,fktest1187,fktest1188,fktest1189,fktest1191,fktest1192,fktest1193,fktest1194,fktest1195,fktest1196,fktest1198,fktest1199,fktest1307,fktest1308,fktest1309,fktest1310,fktest1311,fktest1312,fktest1313,fktest1314,fktest1315,fktest1316,fktest1317,fktest1318,fktest1319,fktest1320,fktest1321,fktest1322,fktest1323,fktest1324,fktest1325,fktest1326,fktest1327,fktest1328,fktest1329,fktest1330,fktest1331,fktest1332,fktest1333,fktest1335,fktest1336,fktest1337,fktest1338,fktest1339,fktest1340,fktest1341,fktest1342,fktest1343,fktest1344,fktest1345,fktest1346,fktest1347,fktest1348,fktest1349,fktest1350,fktest1351,fktest1352,fktest1353,fktest1354,fktest1355,fktest1356,fktest1357,fktest1358,fktest1359,fktest1360,fktest1361,fktest1362,fktest1363,fktest1364,fktest1365,fktest1367,fktest1368,fktest1369,fktest1370,fktest1371,fktest1372,fktest1373,fktest1374,fktest1375,fktest1376,fktest1377,fktest1378,fktest1379,fktest1380,fktest1381,fktest1382,fktest1383,fktest1384,fktest1385,fktest1386,fktest1387,fktest1388,fktest1389,fktest1390,fktest1391,fktest1392,fktest1393,fktest1394,fktest1395,fktest1396,fktest1397,fktest1398,fktest1399,fktest1400,fktest1401,fktest1402,fktest1403,fktest1404,fktest1405,fktest1406,fktest1407,fktest1408,fktest1409,fktest1410,fktest1411,fktest1412,fktest1413,fktest1414,fktest1415,fktest1416,fktest1417,fktest1418,fktest1419,fktest1420,fktest1421,fktest1422,fktest1423,fktest1424,fktest1425,fktest1426,fktest1427,fktest1428,fktest1429,fktest1430,fktest1431,fktest1432,fktest1433,fktest1434,fktest1435,fktest1436,fktest1437,fktest1438,fktest1439,fktest1440,fktest1441,fktest1442,fktest1443,fktest1444,fktest1445,fktest1446,fktest1447,fktest1448,fktest1449,fktest1451,fktest1452,fktest1453,fktest1454,fktest1455,fktest1456,fktest1457,fktest1458,fktest1459,fktest1460,fktest1461,fktest1462,fktest1463,fktest1464,fktest1465,fktest1466,fktest1467,fktest1468,fktest1469,fktest1470,fktest1471,fktest1472,fktest1473,fktest1474,fktest1475,fktest1476,fktest1477,fktest1478,fktest1479,fktest1480,fktest1481,fktest1482,fktest1483,fktest1484,fktest1485,fktest1486,fktest1487,fktest1488,fktest1489,fktest1490,fktest1491,fktest1492,fktest1493,fktest1494,fktest1495,fktest1496,fktest1497,fktest1498,fktest1499,fktest1500,fktest1501,fktest1502,fktest1503,fktest1504,fktest1505,fktest1506,fktest1507,fktest1508,fktest1509,fktest1510,fktest1511,fktest1512,fktest1513,fktest1514,fktest1515,fktest1516,fktest1517,fktest1518,fktest1519,fktest1520,fktest1521,fktest1522,fktest1523,fktest1524,fktest1525,fktest1526,fktest1527,fktest1528,fktest1529,fktest1530,fktest1531,fktest1532,fktest1533,fktest1534,fktest1535,fktest1536,fktest1537,fktest1538,fktest1539,fktest1540,fktest1541,fktest1542,fktest1543,fktest1544,fktest1545,fktest1546,fktest1547,fktest1548,fktest1549,fktest1550,fktest1551,fktest1552,fktest1553,fktest1200,fktest1201,fktest1202,fktest1203,fktest1204,fktest1205,fktest1206,fktest1207,fktest1208,fktest1209,fktest1210,fktest1211,fktest1212,fktest1213,fktest1214,fktest1215,fktest1216,fktest1217,fktest1218,fktest1219,fktest1300,fktest1220,fktest1221,fktest1222,fktest1223,fktest1224,fktest1225,fktest1226,fktest1227,fktest1228,fktest1229,fktest1230,fktest1231,fktest1232,fktest1233,fktest1235,fktest1236,fktest1237,fktest1238,fktest1239,fktest1240,fktest1241,fktest1242,fktest1243,fktest1244,fktest1245,fktest1246,fktest1247,fktest1248,fktest1250,fktest1251,fktest1252,fktest1253,fktest1254,fktest1255,fktest1256,fktest1257,fktest1258,fktest1259,fktest1563,fktest1562,fktest1561,fktest1560,fktest1559,fktest1558,fktest1557,fktest1556,fktest1555,fktest1554,fktest1260,fktest1261,fktest1262,fktest1263,fktest1264,fktest1265,fktest1266,fktest1267,fktest1268,fktest1269,fktest1270,fktest1271,fktest1272,fktest1273,fktest1274,fktest1275,fktest1276,fktest1277,fktest1278,fktest1279,fktest1280,fktest1282,fktest1283,fktest1284,fktest1286,fktest1287,fktest1288,fktest1289,fktest1290,fktest1291,fktest1292,fktest1294,fktest1295,fktest1296,fktest1297,fktest1298,fktest1299,fktest1573,fktest1572,fktest1571,fktest1570,fktest1569,fktest1568,fktest1567,fktest1566,fktest1565,fktest1564,633347,603884,fktest079,fktest080,fktest084,fktest085,fktest086,fktest087,fktest088,fktest183,fktest217,fktest289,fktest293,fktest1026,fktest1190,fktest1197,fktest1366,fktest1334,fktest1450,fs,ihengyou888,dbzg9168,kscloudfxcrm,tieon2020,496004";
        String[] aaArray = a.split(",");
        String[] bbArray = b.split(",");

        List<String> aaList = Arrays.asList(aaArray);
        List<String> bbList = Arrays.asList(bbArray);


    }
}
