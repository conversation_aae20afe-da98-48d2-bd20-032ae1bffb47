package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.util.component.CusComponentCovert;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

/**
 * Created by zhangyu on 2020/5/8
 */
public class NewTest {

    public static void main(String[] args) {
        String a = "WEB_PAGE-1111";
        String replace = a.replace("WEB_PAGE-", "");
        System.out.println(replace);
    }

    @Test
    public void testTenantIds() {
        List<Integer> list = Lists.newArrayList(7689, 632829, 598335, 604891, 595453, 606802, 488989, 632477, 580984, 419804, 627115, 307447, 664226, 597566, 629927, 507719, 127914, 501449, 662757, 472252, 90706, 516148, 49148, 199529, 526380, 606457, 488995, 589227, 615315, 594192, 389877, 568645, 496991, 549366, 617168, 643224, 633761, 17824, 620093, 481495, 636965, 562487, 626246, 55002, 542661, 511164, 424059, 616899, 636781, 565556, 85421, 648162, 632768, 592135, 111024, 624115, 331894, 461402, 570814, 623264, 503575, 629257, 619615, 32099, 542956, 603207, 59472, 541219, 581718, 635758, 597902, 622836, 588110, 646222, 200501, 61456, 642824, 6030, 597846, 35187, 595995, 78722, 210294, 77617, 57515, 339525, 633975, 381492, 598144, 642028, 102177, 570967, 611667, 256184, 653632, 455014, 489207, 101003, 324519, 448142, 491187, 82897, 99598, 322246, 663598, 689193, 158833);
    }

    @Test
    public void testCovertComponent() {
        CusComponentCovert covertCusComponentCovert = new CusComponentCovert();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("apiName", "aaa");
        jsonObject.put("type", "111");
        jsonObject.put("limit", "1.0");
        covertCusComponentCovert.setCusPageJSONObject(jsonObject);

        JSONObject jsonObject1 = covertCusComponentCovert.buildComponent();
        System.out.println(jsonObject1);
    }

    @Test
    public void testURL() throws MalformedURLException {
        URL url = new URL("http://www.runoob.com/html/html-tutorial.html");
        System.out.println("URL 是 " + url.toString());
        System.out.println("协议是 " + url.getProtocol());
        System.out.println("文件名是 " + url.getFile());
        System.out.println("主机是 " + url.getHost());
        System.out.println("路径是 " + url.getPath());
        System.out.println("端口号是 " + url.getPort());
        System.out.println("默认端口号是 "
                + url.getDefaultPort());
        System.out.println(url.getQuery());
    }


    @Test
    public void testFormat() {

        String language = "%s day";
        String format = String.format(language, 10);
        System.out.println(format);

    }

    @Test
    public void testJson() {
        String customerLayoutJson = "{ \"layout\" : [ { \"components\" : [ [ \"filters\" ] ], \"columns\" : [ { \"width\" : \"100%\" } ] }, { \"components\" : [ [ \"grid_REAPINAME_1603855689784\", \"BI_5f2288a654a8ad000129b04b-CRM\", \"BI_5f3a4da4fae2f400010b0826-CRM\", \"BI_5f3a4cb502ac1100011ddc51-CRM\", \"BI_5f6fe868e37c010001a54941-CRM\", \"BI_5f2a22a3e33879000179eeb9-CRM\", \"BI_5f2a6864e33879000179f533-CRM\", \"BI_5f2a68ebe33879000179f558-CRM\", \"BI_5f2a14a2264b2300012a1bba-CRM\" ], [ \"BI_5f21358ee9f4f70001b22b4e-CRM\", \"biCard_16038547603960\" ] ], \"columns\" : [ { \"width\" : \"66%\" }, { \"width\" : \"34%\" } ] } ], \"components\" : [ { \"dataId\" : \"BI_5f3a4da4fae2f400010b0826\", \"api_name\" : \"BI_5f3a4da4fae2f400010b0826-CRM\", \"cardId\" : \"BI_5f3a4da4fae2f400010b0826\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"续费率-大区季度\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f3a4da4fae2f400010b0826\" }, { \"dataId\" : \"BI_5f3a4cb502ac1100011ddc51\", \"api_name\" : \"BI_5f3a4cb502ac1100011ddc51-CRM\", \"cardId\" : \"BI_5f3a4cb502ac1100011ddc51\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"续费率--大区月度\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f3a4cb502ac1100011ddc51\" }, { \"dataId\" : \"BI_5f2a6864e33879000179f533\", \"api_name\" : \"BI_5f2a6864e33879000179f533-CRM\", \"cardId\" : \"BI_5f2a6864e33879000179f533\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"2020--应续50+健康状态分布\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/report/BI_5f2a6864e33879000179f533\" }, { \"dataId\" : \"BI_5f2a14a2264b2300012a1bba\", \"api_name\" : \"BI_5f2a14a2264b2300012a1bba-CRM\", \"cardId\" : \"BI_5f2a14a2264b2300012a1bba\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"2020--应续50+流失明细\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/report/BI_5f2a14a2264b2300012a1bba\" }, { \"dataId\" : \"BI_5f21358ee9f4f70001b22b4e\", \"api_name\" : \"BI_5f21358ee9f4f70001b22b4e-CRM\", \"cardId\" : \"BI_5f21358ee9f4f70001b22b4e\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"续费收入达成\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f21358ee9f4f70001b22b4e\" }, { \"newHeader\" : \"金额续费率_50+\", \"dataId\" : \"BI_5f23ba46e2ddb30001651192\", \"api_name\" : \"BI_5f23ba46e2ddb30001651192-CRM\", \"cardId\" : \"BI_5f23ba46e2ddb30001651192\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"金额续费率_50+\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f23ba46e2ddb30001651192\" }, { \"dataId\" : \"BI_5f2a68ebe33879000179f558\", \"api_name\" : \"BI_5f2a68ebe33879000179f558-CRM\", \"cardId\" : \"BI_5f2a68ebe33879000179f558\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"CSM-应续已续ARR\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/report/BI_5f2a68ebe33879000179f558\" }, { \"dataId\" : \"BI_5f2a22a3e33879000179eeb9\", \"api_name\" : \"BI_5f2a22a3e33879000179eeb9-CRM\", \"cardId\" : \"BI_5f2a22a3e33879000179eeb9\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"2020--50+流失客户统计\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/report/BI_5f2a22a3e33879000179eeb9\" }, { \"dataId\" : \"BI_5f98da849e9e33000197434b\", \"api_name\" : \"biCard_16038547603960\", \"cardId\" : \"PS_Bi\", \"limit\" : 1, \"type\" : \"biCard\", \"title\" : \"续费业绩-分年单统计\", \"propsType\" : 3 }, { \"dataId\" : \"BI_5f2288a654a8ad000129b04b\", \"api_name\" : \"BI_5f2288a654a8ad000129b04b-CRM\", \"cardId\" : \"BI_5f2288a654a8ad000129b04b\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"续费率--各大区\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f2288a654a8ad000129b04b\" }, { \"api_name\" : \"filters\", \"limit\" : 1, \"filters\" : [ { \"filterData\" : \"{\\\"dataTypes\\\":[],\\\"dateType\\\":\\\"本月\\\",\\\"empsAndDeps\\\":[{\\\"id\\\":8128,\\\"type\\\":0}],\\\"dateId\\\":\\\"4\\\",\\\"startTime\\\":\\\"0\\\",\\\"endTime\\\":\\\"0\\\",\\\"isAll\\\":false,\\\"value\\\":{\\\"value1\\\":\\\"[{\\\\\\\"id\\\\\\\":8128,\\\\\\\"type\\\\\\\":\\\\\\\"p\\\\\\\"}]\\\"}}\", \"filterType\" : \"selector\" }, { \"filterData\" : \"{\\\"dataTypes\\\":[],\\\"dateType\\\":\\\"本月\\\",\\\"empsAndDeps\\\":[{\\\"id\\\":8128,\\\"type\\\":0}],\\\"dateId\\\":\\\"4\\\",\\\"startTime\\\":\\\"0\\\",\\\"endTime\\\":\\\"0\\\",\\\"isAll\\\":false,\\\"value\\\":{\\\"dateRangeID\\\":\\\"4\\\"}}\", \"filterType\" : \"date\" }, { \"filterData\" : \"{\\\"enableEmpFilterOfGlobalFilter\\\":0,\\\"enableDateFilterOfGlobalFilter\\\":1}\", \"filterType\" : \"pageDefault\" } ], \"title\" : \"筛选器\", \"type\" : \"filters\" }, { \"dataId\" : \"BI_5f6fe868e37c010001a54941\", \"api_name\" : \"BI_5f6fe868e37c010001a54941-CRM\", \"cardId\" : \"BI_5f6fe868e37c010001a54941\", \"appId\" : \"CRM\", \"limit\" : 1, \"filters\" : [ ], \"title\" : \"续费率_云动业务部\", \"type\" : \"biCard\", \"tools\" : [ ], \"url\" : \"https://www.fxiaoke.com/h5app/bi-report?fromapp=1#/?id=BI_5f6fe868e37c010001a54941\" }, { \"components\" : [ [ \"BI_5f23ba46e2ddb30001651192-CRM\" ], [ \"biCard_16038557094670\" ] ], \"api_name\" : \"grid_REAPINAME_1603855689784\", \"showHeader\" : true, \"limit\" : 0, \"header\" : \"栅格容器\", \"nameI18nKey\" : \"webpage_homepage.grid\", \"ratioType\" : 0, \"widthRatios\" : [ 1, 1 ], \"type\" : \"grid\" }, { \"newHeader\" : \"金额续费率_30-49\", \"dataId\" : \"BI_5f98e5373129cd0001a7d2ee\", \"api_name\" : \"biCard_16038557094670\", \"cardId\" : \"PS_Bi\", \"type\" : \"biCard\", \"title\" : \"金额续费率_30-49\", \"propsType\" : 2 } ], \"filters\" : [ { \"filterData\" : \"{\\\"dataTypes\\\":[],\\\"dateType\\\":\\\"本月\\\",\\\"empsAndDeps\\\":[{\\\"id\\\":8128,\\\"type\\\":0}],\\\"dateId\\\":\\\"4\\\",\\\"startTime\\\":\\\"0\\\",\\\"endTime\\\":\\\"0\\\",\\\"isAll\\\":false,\\\"value\\\":{\\\"value1\\\":\\\"[{\\\\\\\"id\\\\\\\":8128,\\\\\\\"type\\\\\\\":\\\\\\\"p\\\\\\\"}]\\\"}}\", \"filterType\" : \"selector\" }, { \"filterData\" : \"{\\\"dataTypes\\\":[],\\\"dateType\\\":\\\"本月\\\",\\\"empsAndDeps\\\":[{\\\"id\\\":8128,\\\"type\\\":0}],\\\"dateId\\\":\\\"4\\\",\\\"startTime\\\":\\\"0\\\",\\\"endTime\\\":\\\"0\\\",\\\"isAll\\\":false,\\\"value\\\":{\\\"dateRangeID\\\":\\\"4\\\"}}\", \"filterType\" : \"date\" }, { \"filterData\" : \"{\\\"enableEmpFilterOfGlobalFilter\\\":0,\\\"enableDateFilterOfGlobalFilter\\\":1}\", \"filterType\" : \"pageDefault\" } ] }";
        JSONObject jsonObject = JSONObject.parseObject(customerLayoutJson);
        System.out.println(jsonObject);
    }

    @Test
    public void name() {
        System.out.println("a".equals(null));
    }

    @Test
    public void md5() throws IOException {
        String encoding = "UTF-8";
        File file = new File("/Users/<USER>/文本文档/aa.txt");
        StringBuilder stringBuilder = new StringBuilder();
        if (file.isFile() && file.exists()) { //判断文件是否存在
            InputStreamReader read = new InputStreamReader(
                    new FileInputStream(file), encoding);//考虑到编码格式
            BufferedReader bufferedReader = new BufferedReader(read);
            String lineTxt = null;
            while ((lineTxt = bufferedReader.readLine()) != null) {
                stringBuilder.append(lineTxt);
            }
            read.close();
        }
        String string = stringBuilder.toString();
        long startTime = System.currentTimeMillis();
        String md5Hex = DigestUtils.md5Hex(string);
        System.out.println(System.currentTimeMillis() - startTime);
    }
}
