package com.facishare.webpage.customer.console;

import com.facishare.webpage.customer.api.console.MenuService;
import com.facishare.webpage.customer.api.console.arg.QueryMenuArg;
import com.facishare.webpage.customer.api.console.result.QueryMenuResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class MenuServiceImplTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private MenuService menuService;

    @Test
    public void testQueryMenu() {
        QueryMenuArg arg = new QueryMenuArg();
        arg.setEmployeeId(1000);
        QueryMenuResult result = menuService.queryMenu(arg);
        System.out.println(result);
    }
}