package com.facishare.webpage.customer.groovy.layout

import com.alibaba.fastjson.JSONObject
import com.facishare.cep.plugin.enums.ClientTypeEnum
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.api.constant.Status
import com.facishare.webpage.customer.api.model.HomePageLayoutCard
import com.facishare.webpage.customer.api.model.HomePageLayoutTO
import com.facishare.webpage.customer.api.model.LayoutType
import com.facishare.webpage.customer.api.model.Scope
import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg
import com.facishare.webpage.customer.api.model.arg.SetHomePageLayoutStatusArg
import com.facishare.webpage.customer.controller.model.arg.homepage.*
import com.facishare.webpage.customer.controller.model.result.homepage.GetHomePageLayoutListResult
import com.google.common.collect.Lists
import com.google.gson.Gson
import com.google.gson.GsonBuilder

/**
 * Created by zhangyu on 2020/4/2
 */
class CustomerLayoutHelperService extends HomePageBasicTest {

    private static Gson gson = new GsonBuilder().setPrettyPrinting().create();


    def "getUserCustomerLayout"() {
        given:
        def arg = new GetUserCustomerLayoutArg()
        arg.setAppType(BizType.CUSTOMER.type)
        arg.setLayoutApiName(apiName)
        when:
        def result = userHomePageAction.getUserCustomerLayout(buildUserInfo(), null, buildClientInfo(ClientTypeEnum.Mac), arg)
        def layout = covertCustomerManage.convertUserLayout(buildUserInfo(), null, apiName, 3, customerAppId, customerLayout, Locale.CHINA)
        then:
        result.homePageLayout.customerLayout != null
        result.homePageLayout.customerLayout == layout
    }

    def "modifyCustomerLayout"() {
        given:
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppType(BizType.CUSTOMER.type)
        arg.setAppId(customerAppId)
        def homePageLayoutTO = buildHomePageLayoutTO(true)
        arg.homePageLayout = homePageLayoutTO
        when:
        def result = tenantHomePageAction.modifyHomePageLayout(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)
        def layoutEntity = homePageLayoutDao.getHomePageByTenantIdAndApiName(tenantId, "apiName", null)
        then:
        "apiName" == arg.homePageLayout.layoutApiName
        layoutEntity.customerLayout == JSONObject.toJSONString(arg.homePageLayout.customerLayout)
        layoutEntity.name == arg.homePageLayout.name
        layoutEntity.description == arg.homePageLayout.description
        layoutEntity.dataVersion == arg.homePageLayout.dataVersion
        layoutEntity.pageLayoutType == arg.homePageLayout.pageLayoutType
        result.layoutApiName == layoutEntity.apiName
    }

    def "modifyHomePageLayout"() {
        given:
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppType(BizType.CRM.type)
        def homePageLayoutTO = buildHomePageLayoutTO(false)
        arg.homePageLayout = homePageLayoutTO
        when:
        def result = tenantHomePageAction.modifyHomePageLayout(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)
        def layoutEntity = homePageLayoutDao.getHomePageLayoutById(result.getWebPageId(), null);
        then:
        layoutEntity.name == arg.homePageLayout.name
        layoutEntity.description == arg.homePageLayout.description
        result.webPageId == layoutEntity.layoutId
    }

    def "modifyAppHomePageLayout"() {
        given:
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppType(BizType.APP.type)
        def homePageLayoutTO = buildHomePageLayoutTO(true)
        arg.homePageLayout = homePageLayoutTO
        when:
        def result = tenantHomePageAction.modifyHomePageLayout(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)
        def layoutEntity = homePageLayoutDao.getHomePageLayoutById(result.getWebPageId(), null);
        then:
        layoutEntity.name == arg.homePageLayout.name
        layoutEntity.description == arg.homePageLayout.description
        result.webPageId == layoutEntity.layoutId
    }

    def "getVendorHomePageLayout"() {
        given:
        def arg = new GetHomePageLayoutByIdArg()
        arg.layoutId = vendorLayoutId
        arg.appType = BizType.APP.type
        when:
        def result = tenantHomePageAction.getHomePageLayoutById(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)
        def layout = covertCustomerManage.convertTenantLayout(vendorLayout)
        then:
        def homePageLayout = result.homePageLayout
        homePageLayout.customerLayout == layout
        homePageLayout.layoutId == vendorLayoutId
    }

    def "setHomePageLayoutStatus"() {
        given:
        def arg = new SetHomePageLayoutStatusArg()
        arg.layoutApiName = apiName
        arg.appType = BizType.CUSTOMER.type
        arg.status = -99
        when:
        def result = tenantHomePageAction.setHomePageLayoutStatus(buildUserInfo(), arg)
        then:
        def layoutEntity = homePageLayoutDao.getHomePageByTenantIdAndApiName(tenantId, apiName, null)
        result.success == true
        layoutEntity.status == arg.status
    }

    def "getHomePageLayoutList"() {
        given:
        def arg = new GetHomePageListArg()
        arg.setApiName(apiName)
        arg.setAppType(BizType.CUSTOMER.type)
        when:

        GetHomePageLayoutListResult result = tenantHomePageAction.getHomePageLayoutList(buildUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)
        def homePageLayoutList = result.getHomePageLayoutList()

        then:
        homePageLayoutList.size() == 1
        homePageLayoutList.get(0).layoutType == layoutType
        homePageLayoutList.get(0).status == status
        homePageLayoutList.get(0).description == description
        homePageLayoutList.get(0).name == name
        homePageLayoutList.get(0).appId == customerAppId
        homePageLayoutList.get(0).creator == "张三"
        homePageLayoutList.get(0).system == sourceType.equals(SourceType.CUSTOMER) ? false : true
        homePageLayoutList.get(0).dataVersion == 200
    }

    def "getUserHomePageLayoutByLayoutId"(){
        given:
        def arg = new GetUserHomePageLayoutArg()
        arg.setLayoutId(vendorLayoutId)

        when:
        def result = userHomePageAction.getUserHomePageLayoutByLayoutId(buildUserInfo(), buildOuterUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)

        then:
        result != null
        result.getHomePageLayout() != null

    }

    def "getDropListItems"(){
        given:
        def arg = new GetDropListItemsArg()
        arg.setAppId("ObjectDetailPage_Account")
        arg.setAppType(4)
        when:
        def result = tenantHomePageAction.getDropListItems(buildUserInfo(), buildClientInfo(), arg)
        then:
        result.getAppDropListItemList().size() != 0
        result.getDropListItemList().size() != 0
    }

    private HomePageLayoutTO buildHomePageLayoutTO(boolean isCustomerLayout) {
        HomePageLayoutTO homePageLayoutTO = new HomePageLayoutTO()
        if (isCustomerLayout) {
            homePageLayoutTO.setCustomerLayout(customerLayout)
        } else {
            homePageLayoutTO.homePageLayouts = Lists.newArrayList(gson.fromJson(homePageCard1, HomePageLayoutCard.class))
            def scope = new Scope()
            scope.dataType = 1
            scope.dataId = 1000
            homePageLayoutTO.scopes = Lists.newArrayList(scope)
        }

        homePageLayoutTO.name = "name"
        homePageLayoutTO.description = description
        homePageLayoutTO.status = Status.ENABLE
        homePageLayoutTO.layoutType = LayoutType.ENTERPRISE
        homePageLayoutTO.dataVersion = 200
        homePageLayoutTO.pageLayoutType = 1
        homePageLayoutTO.layoutApiName = "apiName"
        return homePageLayoutTO
    }

    def "homePageLayoutJson"(){

    }

}
