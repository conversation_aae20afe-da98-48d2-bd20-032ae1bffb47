package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.dao.TenantMenuDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:fs-webpage-customer-dao.xml")
public class DaoTest {
    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private TenantMenuDao tenantMenuDao;

    @Test
    public void findAllEi(){
        List<Integer> eiList = tenantMenuDao.findAllEi("FSAID_1149100911");
        System.out.println(eiList);
        assertNotNull(eiList);
    }
}
