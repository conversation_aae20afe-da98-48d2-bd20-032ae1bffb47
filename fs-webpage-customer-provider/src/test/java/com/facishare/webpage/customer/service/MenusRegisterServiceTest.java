package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.arg.CreateMenus;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.api.service.MenusRegisterService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * Created by shecheng on 19/12/23.
 */


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class MenusRegisterServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private MenusRegisterService menusRegisterService;

    @Test
    public void testCreate() {
        CreateMenus.Arg arg = new CreateMenus.Arg();
        arg.setTenantId(71574);
        arg.setCollectionId("waiqinapp");
        Menu menu = new Menu();
        menu.setName("自注册");
        menu.setNameI18nKey("selfRegister");
        menu.setId("123");

        Url url = new Url();
        url.setIOSUrl("native://FieldworkHistoryListViewController?{\\\"isKisVersion\\\":true}");
        url.setAndroidUrl("native://com.facishare.fs.biz_function.subbiz_outdoorsignin.OutdoorRecordListActivity?{\\\"isKisVersion\\\":true}");
        menu.setUrl(url);

        Icon icon = new Icon();
        icon.setIcon_1("https://a2.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201703_22_229d9516369c4d3e872952a34620f92d.png&size=150_150&ea=appCenter");
        menu.setIcon(icon);

        arg.setMenus(Lists.newArrayList(menu));
        menusRegisterService.createMenus(arg);
    }

}
