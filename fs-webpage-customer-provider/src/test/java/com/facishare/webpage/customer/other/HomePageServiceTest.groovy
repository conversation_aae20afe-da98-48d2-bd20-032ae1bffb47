package com.facishare.webpage.customer.other

import com.facishare.cep.plugin.model.UserInfo
import com.facishare.webpage.customer.api.InterErrorCode
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.Constant
import com.facishare.webpage.customer.api.constant.ScopeType
import com.facishare.webpage.customer.api.constant.Status
import com.facishare.webpage.customer.api.exception.WebPageException
import com.facishare.webpage.customer.api.model.HomePageLayoutCard
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter
import com.facishare.webpage.customer.api.model.HomePageLayoutTO
import com.facishare.webpage.customer.api.model.Scope
import com.facishare.webpage.customer.controller.TenantHomePageAction
import com.facishare.webpage.customer.controller.impl.TenantHomePageActionImpl
import com.facishare.webpage.customer.controller.model.arg.homepage.ModifyVendorHomePageArg
import com.facishare.webpage.customer.core.util.BIUrlUtil
import com.facishare.webpage.customer.dao.EmployeeCurrentHomePageLayoutDaoImpl
import com.facishare.webpage.customer.dao.HomePageLayoutDaoImpl
import com.facishare.webpage.customer.facade.HomePageServiceImpl
import com.facishare.webpage.customer.controller.model.arg.homepage.CheckCanAddHomePageLayoutArg
import com.facishare.webpage.customer.controller.model.arg.homepage.DeleteHomePageLayoutArg
import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg
import com.facishare.webpage.customer.controller.model.arg.homepage.SetEmployeeCurrentHomePageLayoutArg
import com.facishare.webpage.customer.api.model.arg.SetHomePageLayoutStatusArg
import com.facishare.webpage.customer.service.RemoteService
import com.facishare.webpage.customer.service.impl.HomePageBaseServiceImpl
import com.facishare.webpage.customer.service.impl.UserHomePageBaseServiceImpl
import com.facishare.webpage.customer.util.HomePageLayoutUtil
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Shared
import spock.lang.Specification

/**
 * Created by zhangyu on 2019/9/19
 */
class HomePageServiceTest extends Specification {
    public final FongoRule fongoRule = new FongoRule(false);

    private Datastore datastore;

    private HomePageBaseServiceImpl homePageBaseService;

    private HomePageLayoutDaoImpl homePageLayoutDao;

    private HomePageServiceImpl homePageService;

    private UserHomePageBaseServiceImpl userHomePageBaseService

    private EmployeeCurrentHomePageLayoutDaoImpl employeeCurrentHomePageLayoutDao

    private TenantHomePageAction tenantHomePageAction

    @Autowired
    private BIUrlUtil biUrlUtil;

    def remoteService = Mock(RemoteService.class)

    private ExpectedException exception = ExpectedException.none();

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule);

    @Shared
    HomePageLayoutTO homePageLayout;

    @Shared
    HomePageLayoutTO homePageLayout1;   //正常数据
    @Shared
    HomePageLayoutTO homePageLayout2;   //没有卡片信息
    @Shared
    HomePageLayoutTO homePageLayout3;   //标题为空
    @Shared
    HomePageLayoutTO homePageLayout4;   //已经存在相同的卡片
    @Shared
    HomePageLayoutTO homePageLayout5;   //筛选场景错误
    @Shared
    HomePageLayoutTO homePageLayout6;   //CRM首页名称校验
    @Shared
    HomePageLayoutTO homePageLayout7;   //CRM首页范围校验
    @Shared
    HomePageLayoutTO homePageLayout8;   //保存门户首页

    int tenantId = 2
    int employeeId = 1000
    String appId = Constant.APP_CRM
    int appType = 1


    def setup() {

        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test")
        homePageLayoutDao = new HomePageLayoutDaoImpl()
        homePageLayoutDao.setDatastore(datastore)
        employeeCurrentHomePageLayoutDao = new EmployeeCurrentHomePageLayoutDaoImpl()
        employeeCurrentHomePageLayoutDao.setDatastore(datastore)
        homePageBaseService = new HomePageBaseServiceImpl()
        homePageBaseService.setHomePageLayoutDao(homePageLayoutDao)
        homePageService = new HomePageServiceImpl()
        homePageService.setHomePageBaseService(homePageBaseService)
        userHomePageBaseService = new UserHomePageBaseServiceImpl()
        userHomePageBaseService.employeeCurrentHomePageLayoutDao = employeeCurrentHomePageLayoutDao
        tenantHomePageAction = new TenantHomePageActionImpl()
        tenantHomePageAction.setHomePageBaseService(homePageBaseService)
        tenantHomePageAction.userHomePageBaseService = userHomePageBaseService
        tenantHomePageAction.remoteService = remoteService

        createMessage()
    }

    def createMessage() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = String.valueOf(employeeId)
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout = homePageBaseService.insertHomePageLayout(appId, BizType.CRM.type, tenantId, employeeId, homePageLayoutTO)

    }
    def setMessage1() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout1 = homePageLayoutTO
    }
    def setMessage2() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        homePageLayout2 = homePageLayoutTO
    }
    def setMessage3() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout3 = homePageLayoutTO
    }
    def setMessage4() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)

        def homePageLayoutCard2 = new HomePageLayoutCard()
        homePageLayoutCard2.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard2.setType(2)
        homePageLayoutCard2.setTitle("图表")
        homePageLayoutCard2.setRow(0)
        homePageLayoutCard2.setColumn(1)
        homePageLayoutCard2.setWidth(1)
        homePageLayoutCard2.setHeight(1)
        homePageLayoutCard2.setMobileHeight(2)
        homePageLayoutCard2.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard2, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout4 = homePageLayoutTO
    }
    def setMessage5() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterKey("")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout5 = homePageLayoutTO
    }
    def setMessage6() {
        def homePageLayoutTO = new HomePageLayoutTO()
//        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout6 = homePageLayoutTO
    }
    def setMessage7() {
        def homePageLayoutTO = new HomePageLayoutTO()
        homePageLayoutTO.name = "测试首页"
        homePageLayoutTO.description = "测试首页"
        homePageLayoutTO.layoutType = 2
        def scope = new Scope()
        scope.dataId = "123456"
        scope.dataType = ScopeType.Employee.type
//        homePageLayoutTO.scopes = Lists.newArrayList(scope)
        homePageLayoutTO.system = false
        homePageLayoutTO.currentLayout = false
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout7 = homePageLayoutTO
    }

    def setMessage8(){
        def homePageLayoutTO = new HomePageLayoutTO()
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        def list = Lists.newArrayList(homePageLayoutCard, homePageLayoutCard1)
        homePageLayoutTO.setHomePageLayouts(list)
        homePageLayout8 = homePageLayoutTO
    }


    def "getHomePageLayoutByLayoutId"() {
        given:
        def layoutId = homePageLayout.layoutId
        def arg = new GetHomePageLayoutByIdArg()
        arg.setLayoutId(layoutId)
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)

        def templeId = UUID.randomUUID().toString()

        when:
        def homePageLayoutEntity = homePageLayoutDao.makeHomePageFormal(layoutId, employeeId, templeId, appType)
        homePageLayout = HomePageLayoutUtil.buildHomePageLayoutItem(homePageLayoutEntity)
        homePageLayout.setTempleId(templeId)
        def result = tenantHomePageAction.getHomePageLayoutById(userInfo, arg)
        then:
        homePageLayout == result.homePageLayout
    }
    def "modifyHomePageLayout1"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage1())
        when:
        def layoutId = tenantHomePageAction.modifyHomePageLayout(userInfo, arg).webPageId
        then:
        homePageLayout1.layoutId == layoutId
    }
    def "modifyHomePageLayout2"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage2())
        when:
        String exceptionMessage = "";
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.CARD_CANNOT_BE_EMPTY
    }
    def "modifyHomePageLayout3"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage3())
        when:
        String exceptionMessage = "";
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.TITLE_CANNOT_BE_BLANK
    }
    def "modifyHomePageLayout4"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage4())
        when:
        String exceptionMessage = "";
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.SAME_CARD
    }
    def "modifyHomePageLayout5"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage5())
        when:
        String exceptionMessage = ""
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.FILTERING_SCENE_ERRORS
    }
    def "modifyHomePageLayout6"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage6())
        when:
        String exceptionMessage = ""
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.HOMEPAGE_NAME_NOT_EMPTY
    }
    def "modifyHomePageLayout7"() {
        given:
        def userInfo = new UserInfo()
        userInfo.setEmployeeId(employeeId)
        userInfo.setEnterpriseId(tenantId)
        def arg = new ModifyHomePageLayoutArg()
        arg.setAppId(appId)
        arg.setHomePageLayout(setMessage7())
        when:
        String exceptionMessage = ""
        try {
            tenantHomePageAction.modifyHomePageLayout(userInfo, arg)
        } catch (Exception e) {
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.HOMEPAGE_SCOPE_NOR_EMPTY
    }
    def "updateHomePageStatus"() {
        given:
        def layoutId = homePageLayout.layoutId
        def employeeId = employeeId
        def templeId = "75968fba-0b2b-4c13-a26e-e1625d655ca6"

        when:
        def result = homePageBaseService.makeHomePageFormal(layoutId, employeeId, templeId, appType)
        def pageLayoutEntity = homePageLayoutDao.getHomePageLayoutById(layoutId)
        then:
        result == true
        pageLayoutEntity.status == Status.FORMAL
        pageLayoutEntity.layoutId == layoutId
        pageLayoutEntity.appTemplateId == templeId

    }
    def "getHomePageLayoutList"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        when:
        def result = tenantHomePageAction.getHomePageLayoutList(userInfo)
        then:
        result.homePageLayoutList == Lists.newArrayList(homePageLayout)
    }
    def "setHomePageLayoutStatus"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        def arg = new SetHomePageLayoutStatusArg()
        arg.setLayoutId(homePageLayout.layoutId)
        arg.status = 99
        when:
        def pageLayoutStatus = tenantHomePageAction.setHomePageLayoutStatus(userInfo, arg)
        then:
        pageLayoutStatus.success == true

    }
    def "deleteHomePageLayout"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        def arg = new DeleteHomePageLayoutArg()
        arg.setLayoutId(homePageLayout.layoutId)
        when:
        def deleteHomePageLayout = tenantHomePageAction.deleteHomePageLayout(userInfo, arg)
        then:
        deleteHomePageLayout.success == true
    }
    def "checkCanAddHomePageLayout"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        def arg = new CheckCanAddHomePageLayoutArg()
        arg.layoutType = 2
        when:
        def homePageLayout = tenantHomePageAction.checkCanAddHomePageLayout(userInfo, arg)
        then:
        homePageLayout.canAddHomePageLayout == true
    }
    def "checkCanAddHomePageLayout2"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        def arg = new CheckCanAddHomePageLayoutArg()
        arg.layoutType = 2
        when:
        for (i in 0..20){
            createMessage()
        }
        String exception = ""
        try {
            tenantHomePageAction.checkCanAddHomePageLayout(userInfo, arg)
        }catch (WebPageException e){
            exception = e.message
        }
        then:
        exception == InterErrorCode.MAX_HOMEPAGELAYOUT
    }
    def "setEmployeeCurrentHomePageLayout"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId

        def arg = new SetEmployeeCurrentHomePageLayoutArg()
        arg.setLayoutId(homePageLayout.layoutId)
        when:
        def currentHomePageLayout = tenantHomePageAction.setEmployeeCurrentHomePageLayout(userInfo, arg)
        then:
        currentHomePageLayout.success == true
    }
    def "getEmployeeHomePageLayoutList"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        when:
        remoteService.getDepartmentIdsByEmployee(_,_) >>> [Lists.newArrayList()]
        remoteService.getGroupIdsByEmployee(_,_) >>> [Lists.newArrayList()]
        remoteService.getRoleIdsByEmployee(_,_) >>>[Lists.newArrayList()]

        def result = tenantHomePageAction.getEmployeeHomePageLayoutList(userInfo)

        then:
        result.homePageLayoutTOList == Lists.newArrayList(homePageLayout)
    }

    def "modifyVendorHomePage"(){
        given:
        def userInfo = new UserInfo()
        userInfo.enterpriseId = tenantId
        userInfo.employeeId = employeeId
        def arg = new ModifyVendorHomePageArg()
        arg.setHomePageLayout(setMessage8())
        when:
        def result = tenantHomePageAction.modifyVendorHomePage(userInfo, arg)

        then:
        result.getLayoutId() != null || result.getLayoutId() != ""
    }
}
