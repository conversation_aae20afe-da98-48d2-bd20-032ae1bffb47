package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.model.core.*;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Created by zhangyi on 2019/11/6.
 */
public class MenuConfigTest {
    public static List<MenuItemConfigObject> menuConfig112;
    public static List<MenuItemConfigObject> menuConfig;

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    public static void main(String[] args) {

        String path = "D:\\menu\\test.txt";
        String path2 = "D:\\menu\\test2.txt";
        String path3 = "D:\\menu\\test3.txt";
        String path4 = "D:\\menu\\test4.txt";

        String add = "{\"PriceBookObj\":\"web\",\"PriceBookProductObj\":\"web\",\"OrderPaymentObj\":\"none\",\"ProductObj\":\"web\",\"GoalValueObj\":\"none\",\"SignUserCertifyObj\":\"web\",\"SignRecordObj\":\"web\",\"CustomerAccountObj\":\"web\",\"PromotionObj\":\"web\",\"WarehouseObj\":\"web\",\"RebateUseRuleObj\":\"web\",\"IndustryPriceBookObj\":\"web\",\"IndustryPriceBookProductObj\":\"web\",\"SalesScopeObj\":\"web\",\"SalesScopeProductObj\":\"web\",\"SpecificationObj\":\"web\",\"SpecificationValueObj\":\"web\",\"SPUObj\":\"web\",\"OpportunityObj\":\"mobile\",\"TieredPriceBookObj\":\"web\",\"SalesOrderProductObj\":\"none\",\"ProductConstraintObj\":\"web\"}";


        String indexStr = "";
        List<IndexIcon> indexIcons = JSON.parseArray(indexStr, IndexIcon.class);
        indexIcons = indexIcons.stream().map(x -> {
            Icon icon = x.getIcon();
            if (x.getIndex() < 9) {
                icon.setIcon_2("https://a9.fspage.com/FSR/fs-qixin/static/objicon/faceicon/obj_0" + x.getIndex() + 1 + ".png");
            } else {
                icon.setIcon_2("https://a9.fspage.com/FSR/fs-qixin/static/objicon/faceicon/obj_" + x.getIndex() + 1 + ".png");
            }
            x.setIcon(icon);
            return x;
        }).collect(Collectors.toList());


        System.out.println(JSON.toJSONString(indexIcons));

        Map<String, String> addMap = JSON.parseObject(add, Map.class);
        String iconStr = "";
        List<PreObject> preObjects = JSON.parseArray(iconStr, PreObject.class);
        preObjects.stream().map(x -> {
            Icon icon = x.getIcon();
            icon.setIcon_1(null);
            x.setIcon(icon);
            return x;
        }).collect(Collectors.toList());
        Map<String, PreObject> preObjectMap1 = preObjects.stream().collect(Collectors.toMap(PreObject::getApiName, preObject -> preObject, (key1, key2) -> key2));
        try {
            IChangeableConfig config = ConfigFactory.getConfig(path);
            IChangeableConfig config2 = ConfigFactory.getConfig(path2);
            IChangeableConfig config3 = ConfigFactory.getConfig(path3);
            IChangeableConfig config4 = ConfigFactory.getConfig(path4);
            menuConfig112 = JSON.parseArray(config.getAll().get("menu_items_default_config"), MenuItemConfigObject.class);
            menuConfig = JSON.parseArray(config2.getAll().get("menu_items_default_config"), MenuItemConfigObject.class);
            List<String> icon_path = JSON.parseArray(config3.getAll().get("icon_path"), String.class);
            List<String> object_icon = JSON.parseArray(config4.getAll().get("object_icon"), String.class);
            menuConfig112.stream()
                    .filter(x -> x.getItemType() != null && x.getItemType().equals("predef_obj"))
                    .forEach(x -> {
                        if (preObjectMap1.get(x.getApiName()) != null) {
                            PreObject preObject = preObjectMap1.get(x.getApiName());
                            Icon icon = preObject.getIcon();
                            String iconPath = x.getIconPathHome() == null ? x.getIconPathMenu() : x.getIconPathHome();
                            icon.setIcon_1(iconPath);
                            preObject.setIcon(icon);
                            preObjectMap1.put(x.getApiName(), preObject);
                        } else {
                            PreObject preObject = new PreObject();
                            preObject.setApiName(x.getApiName());
//                        preObject.setAddDeviceTypes(Lists.newArrayList("IOS","Android","web"));
                            Icon icon = new Icon();
                            String iconPath = x.getIconPathHome() == null ? x.getIconPathMenu() : x.getIconPathHome();
                            if (StringUtils.isNotEmpty(iconPath)) {
                                icon.setIcon_1(iconPath);
                                preObject.setIcon(icon);
                            }
                            if (addMap.get(x.getApiName()) != null) {
                                String type = addMap.get(x.getApiName());
                                if (type.equals("none")) {
                                    preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS", "Android", "web"));
                                } else if (type.equals("web")) {
                                    preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS", "Android"));
                                }
                            }
                            preObjectMap1.put(x.getApiName(), preObject);
                        }
                    });

//            System.out.println(JSON.toJSONString(preObjectMap1.values().stream().filter(x -> x != null && x.getIcon() != null).collect(Collectors.toList())));

//            for (int i = 0; i < icon_path.size(); i++) {
//                IndexIcon indexIcon= new IndexIcon();
//                indexIcon.setIndex(i);
//                Icon icon = new Icon();
//                icon.setIcon_1(object_icon.get(i));
//                icon.setIcon_2(icon_path.get(i));
//                indexIcon.setIcon(icon);
//                indexIcons.add(indexIcon);
//            }

//            System.out.println( JSON.toJSONString(indexIcons));

//            List<PreObject> menuMetaList = menuConfig112.stream()
//                    .filter(x -> x.getItemType() != null && x.getItemType().equals("predef_obj"))
//                    .filter(x -> x.getIconPathHome() != null || x.getIconPathMenu() != null)
//                    .map(x -> {
//                        PreObject preObject = new PreObject();
//                        preObject.setApiName(x.getApiName());
////                        preObject.setAddDeviceTypes(Lists.newArrayList("IOS","Android","web"));
//                        Icon icon = new Icon();
//                        String iconPath = x.getIconPathHome() == null? x.getIconPathMenu():x.getIconPathHome();
//                        if(StringUtils.isNotEmpty(iconPath)){
//                            icon.setIcon_1(iconPath);
//                            preObject.setIcon(icon);
//                        }
//                        if(addMap.get(x.getApiName())!=null){
//                            String type = addMap.get(x.getApiName());
//                            if(type.equals("none")){
//                                preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS","Android","web"));
//                            }else if(type.equals("web")){
//                                preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS","Android"));
//                            }
//                        }
//
//                        return preObject;
////                        MenuMeta menuMeta = new MenuMeta();
////                        menuMeta.setApiName(x.getApiName());
////                        menuMeta.setName(x.getDisplayName());
////                        List<String> ret = Lists.newArrayList();
////                        if (StringUtils.equals(x.getDeviceType(), "all")) {
////                            ret.add("IOS");
////                            ret.add("Android");
////                            ret.add("web");
////                            menuMeta.setRoleCodes(ret);
////                        } else if (StringUtils.isNotEmpty(x.getDeviceType())) {
////                            ret.add(x.getDeviceType());
////                            menuMeta.setRoleCodes(ret);
////                        }
////                        return menuMeta;
//                    }).collect(Collectors.toList());
//            Map<String, PreObject> preObjectMap = menuMetaList.stream().collect(Collectors.toMap(PreObject::getApiName, x -> x));
//            List<PreObject> menuMetaListNew = Lists.newArrayList();
//            menuConfig = menuConfig.stream()
//                    .filter(x -> x.getItemType() != null && x.getItemType().equals("predef_obj"))
//                    .filter(x -> x.getIconPathHome() != null || x.getIconPathMenu() != null)
//                    .collect(Collectors.toList());
//            menuConfig.forEach(x->{
//                if(preObjectMap.get(x.getApiName())!=null){
//                    PreObject preObject = preObjectMap.get(x.getApiName());
//                    String iconPath = x.getIconPathHome() == null? x.getIconPathMenu():x.getIconPathHome();
//                    if(preObject.getIcon()!=null){
//                        if(StringUtils.isNotEmpty(iconPath)){
//                            Icon icon = preObject.getIcon();
//                            icon.setIcon_2(iconPath);
//                            preObject.setIcon(icon);
//                        }
//                    }else {
//                        if(StringUtils.isNotEmpty(iconPath)){
//                            Icon icon = new Icon();
//                            icon.setIcon_2(iconPath);
//                            preObject.setIcon(icon);
//                        }
//                    }
//                }else {
//                    PreObject preObject = new PreObject();
//                    preObject.setApiName(x.getApiName());
////                    preObject.setAddDeviceTypes(Lists.newArrayList("IOS","Android","web"));
//                    if(addMap.get(x.getApiName())!=null){
//                        String type = addMap.get(x.getApiName());
//                        if(type.equals("none")){
//                            preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS","Android","web"));
//                        }else if(type.equals("web")){
//                            preObject.setHideAddDeviceTypes(Lists.newArrayList("IOS","Android"));
//                        }
//                    }
//                    Icon icon = new Icon();
//                    String iconPath = x.getIconPathHome() == null? x.getIconPathMenu():x.getIconPathHome();
//                    if(StringUtils.isNotEmpty(iconPath)){
//                        icon.setIcon_2(iconPath);
//                        preObject.setIcon(icon);
//                        menuMetaListNew.add(preObject);
//                    }
//                }
//            });
//            menuMetaListNew.addAll(preObjectMap.values());
//            System.out.println("112 Json:" + JSON.toJSONString(menuMetaListNew));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Data
    @NoArgsConstructor
    public static class MenuItemConfigObject {
        public MenuItemConfigObject(Boolean validatePrivilege, Boolean validateDescribe, String deviceType) {
            this.deviceType = deviceType;
            this.validatePrivilege = validatePrivilege;
            this.validateDescribe = validateDescribe;
        }

        @JSONField(name = "api_name")
        private String apiName;
        @JSONField(name = "displayName")
        private String displayName;
        @JSONField(name = "number")
        private Integer number;
        @JSONField(name = "icon_index")
        private Integer iconIndex;
        @JSONField(name = "icon_path_home")
        private String iconPathHome;
        @JSONField(name = "icon_path_menu")
        private String iconPathMenu;
        //适用终端类型，all代表所有，web代表只web端使用,mobile代表只终端使用
        @JSONField(name = "device_type")
        private String deviceType;
        //对象类型,predef_obj代表对象、not_obj代表不是对象
        @JSONField(name = "item_type")
        private String itemType;
        //validate_privilege，false代表验证功能权限
        @JSONField(name = "validate_privilege")
        private Boolean validatePrivilege;
        //validate_describe，false代表不验证describe
        @JSONField(name = "validate_describe")
        private Boolean validateDescribe;
        @JSONField(name = "url")
        private String url;
        @JSONField(name = "mobile_config")
        private MobileConfig mobileConfig;
        //是否使用默认url
        @JSONField(name = "useDefaultUrl")
        private Boolean useDefaultUrl;

        @Data
        public class MobileConfig {
            @JSONField(name = "mobile_add_action")
            private String mobileAddAction;
            @JSONField(name = "mobile_list_action")
            private String mobileListAction;
        }
    }


    @Test
    public void testIcon() {
        String path1 = "D:\\menu\\test.txt";
        String path2 = "D:\\menu\\test3.txt";
        IChangeableConfig config1 = ConfigFactory.getConfig(path1);
        IChangeableConfig config2 = ConfigFactory.getConfig(path2);
        List<MenuItemConfigObject> menuItemConfigObjects = JSON.parseArray(config1.getAll().get("menu_items_default_config"), MenuItemConfigObject.class);
        List<String> icon_path = JSON.parseArray(config2.getAll().get("icon_path"), String.class);
        Map<String, String> map = new HashMap<>();
        menuItemConfigObjects.stream().forEach(x -> {
            String iconPath = x.getIconPathHome() == null ? x.getIconPathMenu() : x.getIconPathHome();
            if (StringUtils.isNotEmpty(iconPath)) {
                map.put(x.getApiName(), iconPath);
            }
        });

        for (int i = 0; i < icon_path.size(); i++) {
            map.put(String.valueOf(i), icon_path.get(i));
        }


        System.out.println(JSON.toJSONString(map));
    }


    @Test
    public void testBuildMenu() {
        String path = "/Users/<USER>/670-preObj.txt";
        IChangeableConfig config = ConfigFactory.getConfig(path);
        List<MenuItemConfigObject> menuItemConfigObjects = JSON.parseArray(config.getAll().get("menu_items_default_config"), MenuItemConfigObject.class);
        List<Menu> menus = Lists.newArrayList();
        menuItemConfigObjects.stream()
                .forEach(x -> {
                    Menu menu = new Menu();
                    menu.setId(x.getApiName());
                    Icon icon = new Icon();
                    icon.setIcon_1("");
                    icon.setIcon_2("");
                    menu.setIcon(icon);
                    menu.setDeviceTypes(Lists.newArrayList("IOS", "Android", "web"));
                    menu.setName(x.getDisplayName());
                    menu.setNameI18nKey(getSpecialDisPlayName(x.getApiName(),"",""));
                    PersonPrivilege personPrivilege = new PersonPrivilege();
//                    personPrivilege.setRoleCodes(Lists.newArrayList());
//                    personPrivilege.setFunctionCode(Lists.newArrayList());
//                    personPrivilege.setAppId("");
                    menu.setPersonPrivilege(personPrivilege);
                    TenantPrivilege tenantPrivilege = new TenantPrivilege();
//                    tenantPrivilege.setAppId("");
//                    tenantPrivilege.setLicenseModuleCodes(Lists.newArrayList());
//                    tenantPrivilege.setLicenseProductCodes(Lists.newArrayList());
                    menu.setTenantPrivilege(tenantPrivilege);
                    Url url = new Url();
                    url.setWebUrl(x.getUrl());
                    if (x.getMobileConfig() != null) {
                        url.setAndroidUrl(x.getMobileConfig().getMobileListAction());
                        url.setIOSUrl(x.getMobileConfig().getMobileListAction());
                    } else {
                        url.setUseServerUrl(x.getUseDefaultUrl() == null ? false : x.getUseDefaultUrl());
                        url.setIOSUrl(x.getUrl());
                        url.setAndroidUrl(x.getUrl());
                    }
                    menu.setUrl(url);
                    menus.add(menu);
                });


        List<String> apiNames = menus.stream().map(menu -> menu.getId()).collect(Collectors.toList());
        System.out.println(apiNames);
    }


    @Test
    public void testBuildMenu2() {
        String path = "D:\\menu\\gray_menu_rule";
        IChangeableConfig config = ConfigFactory.getConfig(path);
        Map<String, Object> maps = JSON.parseObject(config.getAll().get("gray_menu_rule"), Map.class);
        List<Menu> menus = Lists.newArrayList();
        maps.forEach((x,y)->{
            Menu menu = new Menu();
            Map<String,Object> value = JSON.parseObject(y.toString(), Map.class);
            menu.setId(x);
            Icon icon = new Icon();
            icon.setIcon_1(value.get("icon_path_menu").toString());
            icon.setIcon_2("");
            menu.setIcon(icon);
            menu.setDeviceTypes(Lists.newArrayList("web"));
            menu.setName(value.get("display_name").toString());
            menu.setNameI18nKey(getSpecialDisPlayName(value.get("api_name").toString(),"",""));
            PersonPrivilege personPrivilege = new PersonPrivilege();
//                    personPrivilege.setRoleCodes(Lists.newArrayList());
//                    personPrivilege.setFunctionCode(Lists.newArrayList());
//                    personPrivilege.setAppId("");
            menu.setPersonPrivilege(personPrivilege);
            TenantPrivilege tenantPrivilege = new TenantPrivilege();
//                    tenantPrivilege.setAppId("");
//                    tenantPrivilege.setLicenseModuleCodes(Lists.newArrayList());
                    tenantPrivilege.setLicenseProductCodes(Lists.newArrayList("dealer_edition","promotion_sales_edition"));
            menu.setTenantPrivilege(tenantPrivilege);
            Url url = new Url();
            url.setWebUrl(value.get("url").toString());
           url.setUseServerUrl(true);
            menu.setUrl(url);
            menus.add(menu);
        });
        System.out.println(JSON.toJSONString(menus));
    }

    @Test
    public void testBuildSpecialMenu() {
        String path = "D:\\menu\\special_config_menuItem";
        IChangeableConfig config = ConfigFactory.getConfig(path);
        List<Map> special_config_menuItem = JSON.parseArray(config.getAll().get("special_config_menuItem"), Map.class);
        List<Menu> menus = Lists.newArrayList();
        special_config_menuItem.forEach(value->{
            Menu menu = new Menu();
            menu.setId(value.get("M8").toString());
            Icon icon = new Icon();
            icon.setIcon_1(value.get("M10").toString());
            icon.setIcon_2("");
            menu.setIcon(icon);
            menu.setDeviceTypes(Lists.newArrayList("IOS", "Android"));
            menu.setName(value.get("M7").toString());
            menu.setNameI18nKey(getSpecialDisPlayName(value.get("M8").toString(),"",""));
            PersonPrivilege personPrivilege = new PersonPrivilege();
//                    personPrivilege.setRoleCodes(Lists.newArrayList());
//                    personPrivilege.setFunctionCode(Lists.newArrayList());
//                    personPrivilege.setAppId("");
            menu.setPersonPrivilege(personPrivilege);
            TenantPrivilege tenantPrivilege = new TenantPrivilege();
//                    tenantPrivilege.setAppId("");
//                    tenantPrivilege.setLicenseModuleCodes(Lists.newArrayList());
//            tenantPrivilege.setLicenseProductCodes();
            menu.setTenantPrivilege(tenantPrivilege);
            Url url = new Url();
            url.setWebUrl(value.get("M15")==null?"":value.get("M15").toString());
            url.setUseServerUrl(true);
            menu.setUrl(url);
            menus.add(menu);
        });
        System.out.println(JSON.toJSONString(menus));
    }




    public static String getSpecialDisPlayName(String apiName, Object var1, Object var2) {
        Object result;
        switch (Optional.ofNullable(apiName).orElse("")) {
            case "CrmInfo":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_CRMINFO);
                break;
            case "DataBoard":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_DATABOARD);
                break;
            case "Report":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_REPORT);
                break;
            case "ReportPermissionMgr":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_REPORTPERMISSIONMGR);
                break;
            case "SubscMgr":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_SUBSCMGR);
                break;
            case "ReportLog":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_REPORTLOG);
                break;
            case "LeadsPoolObj": {
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_LEADSPOOLOBJ);
                if (StringUtils.isBlank((String) result)) {
                    result = var2;
                }
            }
            break;
            case "HighSeasObj": {
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_HIGHSEASOBJ);
                if (StringUtils.isBlank((String) result)) {
                    result = var2;
                }
            }
            break;
            case "GoalBoard":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_GOALBOARD);
                break;
            case "CrmServiceManager":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_CRMSERVICEMANAGER);
                break;
            case "CrmRival":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_CRMRIVAL);
                break;
            case "InventoryObj":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_INVENTORYOBJ);
                break;
            case "StatThemeMgr":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_STATTHEMEMGR);
                break;
            case "CrmRemind":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_CRMREMIND);
                break;
            case "DuplicateCheckObj":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_DUPLICATECHECKOBJ);
                break;
            case "SaleRecord":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_SALERECORD);
                break;
            case "NearByCustomer":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_NEARBYCUSTOMER);
                break;
            case "CrmToDo":
                result = I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_CRMTODO);
                break;
            default:
                result = "";
        }
        return result == null ? "" : String.valueOf(result);
    }

}
