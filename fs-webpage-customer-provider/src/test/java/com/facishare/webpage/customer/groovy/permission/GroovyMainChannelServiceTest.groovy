package com.facishare.webpage.customer.groovy.permission

import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds
import com.facishare.paas.license.Result.LicenseVersionResult
import com.facishare.paas.license.Result.ModuleInfoResult
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.ProductVersionPojo
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.facishare.webpage.customer.api.model.MainChannelMenu
import com.facishare.webpage.customer.api.model.arg.QueryMainChannelMenuArg
import com.facishare.webpage.customer.api.model.core.Menu
import com.facishare.webpage.customer.api.service.MainChannelService
import com.facishare.webpage.customer.config.MainChannelMenuConfig
import com.facishare.webpage.customer.model.FilterArg
import com.facishare.webpage.customer.model.FilterResult
import com.facishare.webpage.customer.permission.AppOpenPermissionService
import com.facishare.webpage.customer.permission.RolePermissionService
import com.google.common.collect.Lists
import org.spockframework.spring.SpringBean
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
class GroovyMainChannelServiceTest extends Specification {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private MainChannelService mainChannelService;

    @Resource
    private MainChannelMenuConfig mainChannelMenuConfig;

    @SpringBean
    private LicenseClient licenseClient = Stub()

    @SpringBean
    private EnterpriseEditionService enterpriseEditionService = Stub()

    @SpringBean
    private AppOpenPermissionService appOpenPermissionService = Stub()

    @SpringBean
    private com.facishare.organization.adapter.api.permission.service.PermissionService orgPermissionService = Stub()

    @Resource
    private RolePermissionService rolePermissionService


    def buildJdyEdition() {
        LicenseVersionResult result = new LicenseVersionResult()
        List<ProductVersionPojo> productVersionPojos = Lists.newArrayList()
        ProductVersionPojo pojo = new ProductVersionPojo()
        pojo.setCurrentVersion("jdy_edition")
        productVersionPojos.add(pojo)
        result.setResult(productVersionPojos)
        return result
    }

    def setup() {
        LicenseVersionResult result = new LicenseVersionResult()
        ProductVersionPojo pojo = new ProductVersionPojo()
        pojo.setCurrentVersion("jdy_edition")
        pojo.setProductType("0")
        result.setResult(Lists.newArrayList(pojo))
        licenseClient.queryProductVersion(_) >> result

        ModuleInfoResult moduleResult = new ModuleInfoResult()
        moduleResult.setResult(Lists.newArrayList())
        licenseClient.queryModule(_) >> moduleResult

        GetEnterpriseDataResult dataResult = new GetEnterpriseDataResult()
        EnterpriseData enterpriseData = new EnterpriseData()
        enterpriseData.setSource(1)
        dataResult.setEnterpriseData(enterpriseData)

        enterpriseEditionService.getEnterpriseData(_) >> dataResult

        com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds.Result roleResult = new BatchGetRoleCodesByEmployeeIds.Result()
        Map<String, List<String>> employeeIdRolesMap = new HashMap<>()
        employeeIdRolesMap.put("1000", Lists.newArrayList("99", "47", "60"))
        roleResult.setEmployeeIdRolesMap()
        orgPermissionService.batchGetRoleCodesByEmployeeIds(_) >> roleResult

    }

    def "queryMainChannelMenu is Empty"() {
        given:
        FilterResult emptyFilterResult = new FilterResult()
        emptyFilterResult.setMenus(Lists.newArrayList())
        appOpenPermissionService.filterMenusWithPermission(_) >> emptyFilterResult

        orgPermissionService.(_) >> emptyFilterResult

        QueryMainChannelMenuArg arg = new QueryMainChannelMenuArg();
        arg.setTenantId(71574);
        arg.setEnterpriseAccount("fsceshi003");
        arg.setEmployeeId(1000);
        arg.setLocale(Locale.CHINESE);
        when:
        List<MainChannelMenu> mainChannelMenus = mainChannelService.queryMainChannelMenu(arg);
        then:
        mainChannelMenus.size()>0
    }

    def "test rolePermissionService"() {
        List<Menu> menus = mainChannelMenuConfig.getMainChannelMenuByProductCode("fsceshi003", "jdy_edtion");
        FilterArg arg = new FilterArg()
        arg.setMenus(menus)
        arg.setTenantId(71574)
        arg.setEmployeeId(1000)
        arg.setEnterpriseRegisterSource(1)
        FilterResult result = rolePermissionService.filterMenusWithPermission(arg);
        expect:
        result.getMenus().size() > 0
    }
}
