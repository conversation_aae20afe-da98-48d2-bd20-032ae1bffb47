package com.facishare.webpage.customer.test;

import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
public class StreamTest {

    @Test
    public void parallelStreamTest() {
        List<Integer> array = Lists.newArrayList();
        for (int i = 0; i < 1000; i++) {
            array.add(i);
        }
        int count = 0;
        TraceContext.get().setLocale("en");
        while (count < 10) {
            long startTime1 = System.currentTimeMillis();
            System.out.println(TraceContext.get().getLocale());

            List<String> newArray = array.stream().map(x -> {
                try {
                    return MonitorTaskWrapper.wrap(() -> {
                        try {
                            Thread.currentThread().sleep(10);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        System.out.println(TraceContext.get().getLocale());
                        return String.valueOf(x);
                    }).call();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                return null;
            }).parallel().collect(Collectors.toList());
            System.out.println(System.currentTimeMillis() - startTime1);

            /*long startTime2 = System.currentTimeMillis();
            List<String> newArray2 = array.stream().map(x -> {
                try {
                    Thread.currentThread().sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                System.out.println(TraceContext.get().getLocale());
                return String.valueOf(x);
            }).collect(Collectors.toList());
            System.out.println(System.currentTimeMillis() - startTime2);*/

            count++;
        }


    }
}
