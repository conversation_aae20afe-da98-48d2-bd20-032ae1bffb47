package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.metadata.model.AppComponentData;
import com.github.jedis.support.JedisCmd;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhangyu on 2021/1/22
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/fs-webpage-customer-redis.xml")
public class RedisCache {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private JedisCmd jedisCmd;

    @Test
    public void redisAdd() {
        AppComponentData appComponentData = AppComponentData.builder().appType(1).appId("111").build();
        jedisCmd.set("aa", JSONObject.toJSONString(appComponentData));
    }

    @Test
    public void redisGet() {
        ScanParams scanParams = new ScanParams();
        scanParams.match("a*");
        scanParams.count(100);

        ScanResult<String> scan = jedisCmd.scan(String.valueOf(1), scanParams);
        System.out.println(scan.getResult());
    }
}
