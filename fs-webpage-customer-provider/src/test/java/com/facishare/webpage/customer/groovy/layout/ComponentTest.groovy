package com.facishare.webpage.customer.groovy.layout

import com.facishare.webpage.customer.controller.model.arg.component.GetComponentArg
import com.facishare.webpage.customer.controller.model.arg.component.GetCrossNavigationMenuArg

/**
 * Created by zhangyu on 2020/7/13
 */
class ComponentTest extends HomePageBasicTest {

    def "getComponentList"() {
        given:
        def arg = new GetComponentArg()
        arg.setAppType(4)
        arg.setAppId("ObjectDetailPage_AccountObj")
        when:
        def result = componentAction.getComponentList(buildUserInfo(), buildClientInfo(), arg)
        then:
        result.components.size() != 0

    }

    def "getCrossNavigationMenu"() {
        given:
        def arg = new GetCrossNavigationMenuArg()
        arg.setAppId(appId)
        when:
        def result = componentAction.getCrossNavigationMenu(buildUserInfo(), buildClientInfo(), arg)
        then:
        result.applicationMenus != null
    }

    def "getInnerNavigationMenu"() {
        when:
        def result = componentAction.getInnerNavigationMenu(buildUserInfo(), buildClientInfo())
        then:
        result != null
    }

}
