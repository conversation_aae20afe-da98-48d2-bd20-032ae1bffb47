package com.facishare.webpage.customer.homepage.test;

import com.facishare.webpage.customer.api.model.HomePageComponent;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Test;

import java.util.List;
import java.util.UUID;

/**
 * Created by zhangyu on 2019/9/18
 */
public class CompentTest {
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    @Test
    public void testCompent() {

        List<HomePageComponent> list = Lists.newArrayList();

        /*for (int i = 0; i < 5; i++) {*/
            HomePageComponent homePageComponent = new HomePageComponent();
            homePageComponent.setId(UUID.randomUUID().toString());
            homePageComponent.setCardId("1411");
            homePageComponent.setComponentType(1);
            homePageComponent.setParentId("123");
            homePageComponent.setTitle("测试");
            homePageComponent.setType(2);
            homePageComponent.setUrl("12345");
            list.add(homePageComponent);
//        }
        String json = gson.toJson(homePageComponent);
        System.out.println(json);
    }
    @Test
    public void testEquals(){

    }
    @Test
    public void testGetHomePage(){
        /*ModifyHomePageLayoutArg arg = new ModifyHomePageLayoutArg();
        HomePageLayoutVO homePageLayoutVO = new HomePageLayoutVO();
        HomePageLayoutCard homePageLayoutCard = new HomePageLayoutCard();
        homePageLayoutCard.setCardId("PS_Schedule");
        homePageLayoutCard.setColumn(1);
        homePageLayoutCard.setHeight(1);
        homePageLayoutCard.setMobileHeight(2);
        homePageLayoutCard.setOrder(1);
        homePageLayoutCard.setRow(2);
        homePageLayoutCard.setTitle("日程");
        homePageLayoutCard.setType(101);
        homePageLayoutCard.setWidth(2);
        List<HomePageLayoutCard> layoutCardList = Lists.newArrayList(homePageLayoutCard);
        homePageLayoutVO.setHomePageLayouts(layoutCardList);
        arg.setAppId("PRM");
        arg.setHomePageLayout(homePageLayoutVO);
        String json = gson.toJson(arg);
        System.out.println(json);*/


    }

    @Test
    public void testEqual(){
        System.out.println("11".equals(null));
    }
}
