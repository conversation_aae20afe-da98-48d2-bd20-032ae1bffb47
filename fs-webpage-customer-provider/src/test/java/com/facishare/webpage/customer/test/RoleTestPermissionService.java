package com.facishare.webpage.customer.test;


import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.config.MainChannelMenuConfig;
import com.facishare.webpage.customer.migrate.config.CrmMigrateConfigManager;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.facishare.webpage.customer.permission.RolePermissionService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class RoleTestPermissionService {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    //@Resource
    //private RolePermissionService rolePermissionService;

    //@Resource
    //private MainChannelMenuConfig mainChannelMenuConfig;

    @Resource
    private CrmMigrateConfigManager crmMigrateConfigManager;

//    @Test
//    public void test() {
//        List<Menu> menus = mainChannelMenuConfig.getMainChannelMenuByProductCode("fsceshi003", "jdy_edtion", Lists.newArrayList());
//        FilterArg arg = new FilterArg();
//        arg.setMenus(menus);
//        arg.setTenantId(71574);
//        arg.setEmployeeId(1000);
//        arg.setEnterpriseRegisterSource(1);
//        FilterResult result = rolePermissionService.filterMenusWithPermission(arg);
//    }

    /**
     * 测试添加企业
     */
    @Test
    public void test2() {
        //crmMigrateConfigManager.addOrRemoveNewCRMEnterpriseId2Config(1,false);
    }
}
