package com.facishare.webpage.customer.groovy.menu

import com.facishare.qixin.relation.rest.FsDownstreamService
import com.facishare.qixin.sysdb.serivce.DataSourceService
import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.api.model.core.Icon
import com.facishare.webpage.customer.api.service.TenantPageTempleService
import com.facishare.webpage.customer.config.AddMenuItemConfig
import com.facishare.webpage.customer.config.AppMenuConfig
import com.facishare.webpage.customer.core.config.IconPathConfig
import com.facishare.webpage.customer.config.model.AddMenuItemVo
import com.facishare.webpage.customer.constant.MenuType
import com.facishare.webpage.customer.constant.WebPageConstants
import com.facishare.webpage.customer.controller.UserMenuAction
import com.facishare.webpage.customer.controller.impl.UserMenuActionImpl
import com.facishare.webpage.customer.core.config.ObjectConfig
import com.facishare.webpage.customer.dao.*
import com.facishare.webpage.customer.dao.entity.MenuDataEntity
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity
import com.facishare.webpage.customer.dao.entity.UserMenuEntity
import com.facishare.webpage.customer.facade.TenantPageTempleServiceImpl
import com.facishare.webpage.customer.groovy.MockResource
import com.facishare.webpage.customer.service.TenantMenuService
import com.facishare.webpage.customer.service.UserMenuService
import com.facishare.webpage.customer.service.impl.TenantMenuServiceImpl
import com.facishare.webpage.customer.service.impl.UserMenuServiceImpl
import com.facishare.webpage.customer.system.datasource.MenuDataServiceImpl
import com.facishare.webpage.customer.util.FilterMenuItemUtil
import com.facishare.webpage.customer.util.TempleIdUtil
import com.google.common.collect.Lists
import spock.lang.Shared

/**
 * Created by zhangyu on 2020/3/30
 */
class MenuBasicTest extends MockResource {

    public TenantMenuDao tenantMenuDao

    public UserMenuService userMenuService

    public UserMenuAction userMenuAction

    public UserMenuDao userMenuDao

    public TenantConfigDao tenantConfigDao;

    public TenantPageTempleService tenantPageTempleService

    public TenantMenuService tenantMenuService

    public DataSourceService menuDataService

    public FilterMenuItemUtil filterMenuItemUtil

    def fsDownstreamService = Mock(FsDownstreamService.class)

    def iconPathConfig = Mock(IconPathConfig.class) {
        getPngIconPath(_) >> "https://www.ceshi112.com/XV/Home/Index#crm/index"
        getSvgIconPath(_) >> "https://www.ceshi112.com/XV/Home/Index#crm/index"
    }

    def objectConfig = Mock(ObjectConfig.class) {
        getIconByApiName(_) >> new Icon()
        getIconByIndex(_) >> new Icon()
        hiddenAddWithDeviceType(_, _) >> false
        showApiNames(_, _) >> Lists.newArrayList("SPUObj", " SalesScopeObj", " SignRecordObj", " SalesScopeProductObj", " IndustryPriceBookProductObj", " SignUserCertifyObj", " IndustryPriceBookObj", " InvoiceApplicationLinesObj", " AdvertisementObj", " PriceBookProductObj", " DevicePartObj", " QuoteLinesObj", " NewOpportunityContactsObj", " NewOpportunityLinesObj", " CreditFileObj", " PaymentPlanObj", " SpecificationObj", " StockDetailsObj", " PriceBookObj", " GoodsReceivedNoteObj", " StockObj", " AccountObj", " DeviceObj", " FundReturnBackObj", " OrderPaymentObj", " SupplierObj", " CheckinsImgObj", " SpecificationValueObj", " RefundObj", " CheckinsObj", " RebateIncomeDetailObj", " DealerStockObj", " WarehouseObj", " DealerOrderObj", " VisitingObj", " MarketingEventObj", " OutboundDeliveryNoteObj", " ErpWarehouseObj", " SerialNumberObj", " StockCheckNoteObj", " ExchangeReturnNoteObj", " PurchaseOrderObj", " NewOpportunityObj", " GoalBoard", " QuoteObj", " AIProductReportMainObj", " LeadsPoolObj", " BatchObj", " StatementObj", " ReturnedGoodsInvoiceObj", " ActiveRecordObj", " DeliveryNoteObj", " AccountFinInfoObj", " SalesOrderObj", " PromotionObj", " ContractObj", " CustomerAccountObj", " OpportunityObj", " CustomerReceivedNoteObj", " PaymentObj", " ErpStockObj", " HighSeasObj", " TieredPriceBookObj", " AccountAddrObj", " InvoiceApplicationObj", " AIProductReportDetailObj", " PrepayDetailObj", " PartnerObj", " PersonnelObj", " RequisitionNoteObj", " ProductObj", " SalesOrderProductObj", " LeadsObj", " ContactObj", " GoalValueObj", " CheckRecordObj", " CasesObj", " RebateUseRuleObj", " ServiceRecordObj", " DealerGoodsReceivedNoteProductObj", " MarketingActivityObj", " AppraiseObj", " SkillLevelModelObj", " DealerReturnOrderProductObj", " ServiceSkillObj", " StoreReceivedNoteProductObj", " DealerDeliveryNoteProductObj", " UnitInfoObj", " DealerDeliveryNoteObj", " ShelfReportDetailObj", " CasesServicePersonnelObj", " MemberGrowthValueDetailObj", " AIRefObj", " WebImVisitorObj", " CasesServiceProjectObj", " CheckinsVerifyObj", " CheckinsVerifyDetailObj", " LedgerDetailObj", " DealerReturnOrderObj", " ReturnNoticeNoteObj", " DeviceComponentsObj", " DealerGoodsReceivedNoteObj", " ChargeApproveObj", " TelesalesRecordObj", " StoreStockObj", " DealerCheckinsObj", " InternalSignCertifyObj", " EmployeeWarehouseObj", " DealerOutboundDeliveryNoteObj", " WechatWorkExternalUserObj", " MemberIntegralDetailObj", " StoreSalesVolumeProductObj", " ServiceProjectObj", " FeeDetailObj", " StoreReceivedNoteObj", " CheckinsImgDetailObj", " DealerReturnNoticeNoteObj", " ExchangeGoodsNoteObj", " WechatFanObj", " RefundMaterialBillObj", " CoinAccountDetailObj", " FundReturnBackOb", " AccountSignCertifyObj", " LedgerObj", " MarketingBehaviorObj", " EmployeeSkillObj", " DealerReturnNoticeNoteProductObj", " CasesDeviceObj", " DealerOutboundDeliveryNoteProductObj", " NecessarySkillObj", " StoreSalesVolumeObj", " ShelfReportObj", " PointsRewardDetailObj", " CoinAccountObj", " BehaviorIntegralDetailObj", " AIMainObj", " MemberObj", " ReceiveMaterialBillObj", " ProductConstraintObj", " AvailableRangeObj", " PartnerReportObj", " CommonUnitObj", " StockReportingObj", " StockReportingDetailsObj", " PurchaseReportingObj", " PurchaseDetailsObj", " ForecastRecordObj", " ForecastSumObj", " DealerPointsRewardDetailObj");
    }

    def appMenuConfig = Mock(AppMenuConfig.class) {
        getMaxMenuLimit(_, _) >> 30
        getCommonlyUseMenusByAppId(_) >> Lists.newArrayList("CrmRemind", "DataBoard", "LeadsObj", "AccountObj", "ContactObj", "OpportunityObj", "CrmInfo")
        getGrayMenuApiNamesByAppId(_) >> Lists.newArrayList()
        isTempleTenant(_,_) >> true
    }

    def addMenuItemConfig = Mock(AddMenuItemConfig.class) {

        getAddMenuItems("CRM") >> buildAddMenuItemVoList()
        getHiddenManageApiNames() >> getHiddenManageApiNames()
    }


    public int tenantId = 71574
    public String systemMenuId = TempleIdUtil.buildId(71574)
    public String tenantMenuId = TempleIdUtil.buildId(71574)
    public int appType = BizType.CRM.type
    public String appId = WebPageConstants.APP_CRM
    public List<String> scopes = Lists.newArrayList("R-00000000000000000000000000000019", "R-00000000000000000000000000000009",
            "R-00000000000000000000000000000026", "R-00000000000000000000000000000004", "R-00000000000000000000000000000015", "R-00000000000000000000000000000005",
            "R-00000000000000000000000000000016", "R-00000000000000000000000000000017", "R-00000000000000000000000000000006", "R-00000000000000000000000000000018",
            "R-00000000000000000000000000000029", "R-personnelrole", "R-00000000000000000000000000000002", "R-00000000000000000000000000000024", "R-00000000000000000000000000000003",
            "R-00000000000000000000000000000014", "R-00000000000000000000000000000020", "R-00000000000000000000000000000010")
    public String name = "CRM"

    public String description = "默认菜单"

    public Integer creatorId = 1000

    public Long createTime = 1583121600000

    public Integer updaterId = 1000

    public Long updateTime = 1583121600000

    public boolean isChange = true

    public Integer status = 1

    public List<MenuDataEntity> menuDataEntityList = buildTenantMenuData()

    public String hiddenSystemPreObj = "GoalValueObj"

    public String hiddenSystemUdObj = "test1__c"

    public String hiddenTenantPreObj = "GoalBoard"

    public String hiddenTenantUdObj = "test1__c"

    public String addBPMMonitor = "BPMMonitor"

    public String addUDTest__c = "test__c";

    public String addCRMTodo = "CrmToDo"

    public String addCRMRemind = "CrmRemind"

    public String hiddenGroup = TempleIdUtil.guid

    @Shared
    TenantMenuEntity tenantMenuEntity11     //系统预置菜单
    @Shared
    TenantMenuEntity tenantMenuEntity12     //租户级菜单

    @Shared
    UserMenuEntity userMenuEntity12  //租户级菜单个人级菜单

    def setup() {

        tenantMenuDao = new TenantMenuDaoImpl()
        tenantMenuDao.datastore = datastore

        userMenuDao = new UserMenuDaoImpl()
        userMenuDao.datastore = datastore

        tenantConfigDao = new TenantConfigDaoImpl()
        tenantConfigDao.datastore = datastore

        tenantPageTempleService = new TenantPageTempleServiceImpl()

        menuDataService = new MenuDataServiceImpl()

        menuDataService.tenantMenuDao = tenantMenuDao
        filterMenuItemUtil = new FilterMenuItemUtil()
        filterMenuItemUtil.objectConfig = objectConfig

        userMenuService = new UserMenuServiceImpl()
        userMenuService.objectConfig = objectConfig
        userMenuService.userMenuDao = userMenuDao
        userMenuService.tenantConfigDao = tenantConfigDao


        tenantMenuService = new TenantMenuServiceImpl()
        tenantMenuService.tenantMenuDao = tenantMenuDao
        tenantMenuService.tenantConfigDao = tenantConfigDao
        tenantMenuService.appMenuConfig = appMenuConfig
        tenantMenuService.userMenuDao = userMenuDao
        tenantMenuService.metaMenuService = metaMenuService
        tenantMenuService.addMenuItemConfig = addMenuItemConfig
        tenantMenuService.groupMetaConfig = groupMetaConfig

        userMenuAction = new UserMenuActionImpl()
        userMenuAction.tenantMenuService = tenantMenuService
        userMenuAction.tenantPageTempleService = tenantPageTempleService
        userMenuAction.remoteService = remoteService
        userMenuAction.userMenuService = userMenuService
        userMenuAction.fsDownstreamService = fsDownstreamService
        userMenuAction.languageService = languageService
        userMenuAction.iconPathConfig = iconPathConfig
        userMenuAction.appMenuConfig = appMenuConfig
        userMenuAction.metaMenuService = metaMenuService
        userMenuAction.filterMenuItemUtil = filterMenuItemUtil
        userMenuAction.addMenuItemConfig = addMenuItemConfig
        userMenuAction.homePageCommonService = homePageCommonService
        userMenuAction.searchWordsService = searchWordsService
        userMenuAction.permissionFactory = permissionFactory

        createMessage()
    }

    def createMessage() {

        def tenantMenuEntity = new TenantMenuEntity()
        tenantMenuEntity.name = name
        tenantMenuEntity.status = status
        tenantMenuEntity.description = description
        tenantMenuEntity.appId = appId
        tenantMenuEntity.appType = appType
        tenantMenuEntity.change = isChange
        tenantMenuEntity.createTime = createTime
        tenantMenuEntity.creatorId = creatorId
        tenantMenuEntity.scopes = scopes
        tenantMenuEntity.sourceType = SourceType.SYSTEM
        tenantMenuEntity.tenantId = tenantId
        tenantMenuEntity.updaterId = updaterId
        tenantMenuEntity.updateTime = updateTime
        tenantMenuEntity.menuDataEntities = menuDataEntityList
        tenantMenuEntity.id = systemMenuId
        tenantMenuEntity11 = tenantMenuDao.findAndModify(tenantId, tenantMenuEntity)

        def tenantMenuEntity1 = new TenantMenuEntity()
        tenantMenuEntity1.name = name
        tenantMenuEntity1.status = status
        tenantMenuEntity1.description = description
        tenantMenuEntity1.appId = appId
        tenantMenuEntity1.appType = appType
        tenantMenuEntity1.change = isChange
        tenantMenuEntity1.createTime = createTime
        tenantMenuEntity1.creatorId = creatorId
        tenantMenuEntity1.scopes = scopes
        tenantMenuEntity1.sourceType = SourceType.CUSTOMER
        tenantMenuEntity1.tenantId = tenantId
        tenantMenuEntity1.updaterId = updaterId
        tenantMenuEntity1.updateTime = updateTime
        tenantMenuEntity1.menuDataEntities = menuDataEntityList
        tenantMenuEntity1.id = tenantMenuId
        tenantMenuEntity12 = tenantMenuDao.findAndModify(tenantId, tenantMenuEntity1)

        UserMenuEntity userMenuEntity2 = new UserMenuEntity();
        userMenuEntity2.setTenantMenuId(tenantMenuId)
        userMenuEntity2.setMenuDataEntities(buildTenantUserMenuData())
        userMenuEntity2.setUpdateTime(updateTime)
        userMenuEntity2.setCreateTime(createTime)
        userMenuEntity2.setEmployeeId(creatorId)
        userMenuEntity2.setTenantId(tenantId)
        userMenuEntity2.setStatus(0)
        userMenuDao.save(userMenuEntity2)
        userMenuEntity12 = userMenuDao.save(userMenuEntity2)

    }

    private List<MenuDataEntity> buildTenantUserMenuData() {
        def entity1 = new MenuDataEntity()
        entity1.apiName = "MarketingEventObj"
        entity1.type = "PRE_OBJ"
        entity1.name = "市场活动"
        entity1.groupApiName = ""
        entity1.isHidden = false
        entity1.orderNumber = 0

        def entity2 = new MenuDataEntity()
        entity2.apiName = "LeadsObj"
        entity2.type = "PRE_OBJ"
        entity2.name = "销售线索"
        entity2.groupApiName = ""
        entity2.isHidden = false
        entity2.orderNumber = 1

        def entity3 = new MenuDataEntity()
        entity3.apiName = hiddenGroup
        entity3.type = "group"
        entity3.name = "分组1"
        entity3.groupApiName = ""
        entity3.isHidden = true
        entity3.orderNumber = 2

        def entity4 = new MenuDataEntity()
        entity4.apiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity4.type = "group"
        entity4.name = "分组2"
        entity4.groupApiName = ""
        entity4.isHidden = false
        entity4.orderNumber = 3

        def entity5 = new MenuDataEntity()
        entity5.apiName = hiddenTenantPreObj
        entity5.type = "NO_OBJ"
        entity5.name = "目标完成情况"
        entity5.groupApiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity5.isHidden = true
        entity5.orderNumber = 0


        def entity6 = new MenuDataEntity()
        entity6.apiName = "GoalValueObj"
        entity6.type = "NO_OBJ"
        entity6.name = "目标"
        entity6.groupApiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity6.isHidden = false
        entity6.orderNumber = 1

        def entity7 = new MenuDataEntity()
        entity7.apiName = "layout_8WFKHSfQT2__c"
        entity7.type = MenuType.PAGE_TEMPLATE
        entity7.name = "自定义页面测试"
        entity7.groupApiName = ""
        entity7.isHidden = false
        entity7.orderNumber = 5

        def entity8 = new MenuDataEntity()
        entity8.apiName = hiddenTenantUdObj
        entity8.type = MenuType.UD_OBJ
        entity8.name = "自定义对象1"
        entity8.groupApiName = ""
        entity8.isHidden = false
        entity8.orderNumber = 6

        List<MenuDataEntity> menuDataEntityList = Lists.newArrayList(entity1, entity2, entity3, entity4, entity5, entity6, entity7, entity8)

        return menuDataEntityList
    }

    private List<MenuDataEntity> buildTenantMenuData() {

        def entity2 = new MenuDataEntity()
        entity2.apiName = "LeadsObj"
        entity2.type = MenuType.PRE_OBJ
        entity2.name = "销售线索"
        entity2.groupApiName = ""
        entity2.isHidden = false
        entity2.orderNumber = 1

        def entity3 = new MenuDataEntity()
        entity3.apiName = "AccountObj"
        entity3.type = MenuType.PRE_OBJ
        entity3.name = "客户"
        entity3.groupApiName = ""
        entity3.isHidden = true
        entity3.orderNumber = 2

        def entity4 = new MenuDataEntity()
        entity4.apiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity4.type = MenuType.GROUP
        entity4.name = "分组2"
        entity4.groupApiName = ""
        entity4.isHidden = false
        entity4.orderNumber = 3

        def entity5 = new MenuDataEntity()
        entity5.apiName = "GoalBoard"
        entity5.type = MenuType.NO_OBJ
        entity5.name = "目标完成情况"
        entity5.groupApiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity5.isHidden = false
        entity5.orderNumber = 0


        def entity6 = new MenuDataEntity()
        entity6.apiName = "GoalValueObj"
        entity6.type = MenuType.NO_OBJ
        entity6.name = "目标"
        entity6.groupApiName = "group-a6494bf9-525f-4b40-9390-20d7d187cfce"
        entity6.isHidden = false
        entity6.orderNumber = 1

        def entity7 = new MenuDataEntity()
        entity7.apiName = "layout_8WFKHSfQT2__c"
        entity7.type = MenuType.PAGE_TEMPLATE
        entity7.name = "自定义页面测试"
        entity7.groupApiName = ""
        entity7.isHidden = false
        entity7.orderNumber = 5

        def entity8 = new MenuDataEntity()
        entity8.apiName = "test1__c"
        entity8.type = MenuType.UD_OBJ
        entity8.name = "自定义对象1"
        entity8.groupApiName = ""
        entity8.isHidden = true
        entity8.orderNumber = 6

        List<MenuDataEntity> menuDataEntityList = Lists.newArrayList(entity2, entity3, entity4, entity5, entity6, entity7, entity8)

        return menuDataEntityList
    }


    List<AddMenuItemVo> buildAddMenuItemVoList() {

        def addMenuItemVo1 = new AddMenuItemVo()
        addMenuItemVo1.setApiName("CrmRemind")
        addMenuItemVo1.setHiddenManage(true)
        addMenuItemVo1.setTopMenu(true)

        def addMenuItemVo2 = new AddMenuItemVo()
        addMenuItemVo2.setApiName("BPMMonitor")
        addMenuItemVo2.setAfterApiName("CrmToDo")
        addMenuItemVo2.setHiddenManage(true)

        def addMenuItemVo3 = new AddMenuItemVo()
        addMenuItemVo3.setApiName("CrmToDo")
        addMenuItemVo3.setAfterApiName("CrmRemind")
        addMenuItemVo3.setHiddenManage(true)

        def addMenuItemVo4 = new AddMenuItemVo()
        addMenuItemVo4.setApiName("DuplicateCheckObj")

        def addMenuItemVo5 = new AddMenuItemVo()
        addMenuItemVo5.setApiName("NearByCustomer")

        List<AddMenuItemVo> addMenuItemVoList = Lists.newArrayList(addMenuItemVo1, addMenuItemVo3, addMenuItemVo2, addMenuItemVo4, addMenuItemVo5)

        return addMenuItemVoList
    }

    List<String> getHiddenManageApiNames() {
        def menuItemVoList = buildAddMenuItemVoList()

        List<String> apiNames = Lists.newArrayList()
        for (AddMenuItemVo addMenuItemVo : menuItemVoList) {
            if (addMenuItemVo.isHiddenManage()){
                apiNames.add(addMenuItemVo.getApiName())
            }
        }
        return apiNames
    }


}
