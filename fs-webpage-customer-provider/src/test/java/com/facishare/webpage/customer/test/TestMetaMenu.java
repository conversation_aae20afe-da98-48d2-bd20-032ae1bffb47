package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.core.model.MenuSourceTypeConst;
import com.facishare.webpage.customer.dao.entity.WebMainChannelMenuEntity;
import com.facishare.webpage.customer.metadata.model.ConfigMenuData;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

/**
 * Created by zhangyu on 2020/5/28
 */
public class TestMetaMenu {

    public static void main(String[] args) {
        ConfigMenuData configMenuData = new ConfigMenuData();
        Menu menu = new Menu();
        menu.setName("1111");
        configMenuData.setMenu(menu);

        List<MetaMenuData> metaMenuDataList = Lists.newArrayList();

        metaMenuDataList.add(configMenuData);

        System.out.println(metaMenuDataList.get(0).getName());
    }

    @Test
    public void testSplit() {
        String apiName = "object_15Qyb__c";

        String[] s = apiName.split("_");
        System.out.println(s[0]);

        String s1 = apiName.replaceFirst(s[0] + "_", "");
        System.out.println(s1);


    }

    @Test
    public void testCovertCustomerMenu() {
        CustomerMenu customerMenu = CustomerMenu.builder().menuApiName("test__c").
                apiName("layoutApiName__c").
                menuType(MenuSourceTypeConst.PAGE_MENU_TYPE).
                name("自定义菜单项").
                objectApiName("").
                objectDisplayName("").
                objectRecordName("").
                objectRecordTypeApiName("").
                scopeList(Lists.newArrayList()).build();
        System.out.println(JSONObject.toJSONString(customerMenu));
    }

    @Test
    public void listTest() {
        WebMainChannelMenuEntity webMainChannelMenuEntity1 = new WebMainChannelMenuEntity();
        webMainChannelMenuEntity1.setAppId("1");
        WebMainChannelMenuEntity webMainChannelMenuEntity2 = new WebMainChannelMenuEntity();
        webMainChannelMenuEntity2.setAppId("2");
        WebMainChannelMenuEntity webMainChannelMenuEntity3 = new WebMainChannelMenuEntity();
        webMainChannelMenuEntity3.setAppId("3");
        WebMainChannelMenuEntity webMainChannelMenuEntity4 = new WebMainChannelMenuEntity();
        webMainChannelMenuEntity4.setAppId("4");

        WebMainChannelMenuEntity addWebMainChannelMenuEntity = new WebMainChannelMenuEntity();
        addWebMainChannelMenuEntity.setAppId("5");

        WebMainChannelMenuEntity afterWebMainChannelMenuEntity = new WebMainChannelMenuEntity();
        afterWebMainChannelMenuEntity.setAppId("1");

        List<WebMainChannelMenuEntity> webMainChannelMenuEntityList = Lists.newArrayList(
                webMainChannelMenuEntity1, webMainChannelMenuEntity2, webMainChannelMenuEntity3, webMainChannelMenuEntity4);
        int index = webMainChannelMenuEntityList.indexOf(afterWebMainChannelMenuEntity);

        webMainChannelMenuEntityList.add(index + 1, addWebMainChannelMenuEntity);

        System.out.println(webMainChannelMenuEntityList);
    }
}
