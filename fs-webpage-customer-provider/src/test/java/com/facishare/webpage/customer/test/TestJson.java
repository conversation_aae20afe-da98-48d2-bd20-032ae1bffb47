package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.HomePageLayoutTool;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.SaveWebPageTempleArg;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.junit.Test;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2019/9/19
 */
@Data
public class TestJson {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    @J<PERSON>NField(name = "Name")
    private String name;
    @J<PERSON><PERSON>ield(name = "Age")
    private int age;
    @J<PERSON><PERSON>ield(name = "Dog")
    private Dog dog;
    @Data
    class Dog{
        @JSONField(name = "Message")
        private String message;
    }

    @Test
    public void json(){
        TestJson testJson = new TestJson();
        testJson.setName("aaa");
        testJson.setAge(12);
        Dog dog = new Dog();
        dog.setMessage("123456");
        testJson.setDog(dog);
        String string = JSON.toJSONString(testJson);
        System.out.println(string);
    }
    @Test
    public void testTemplate(){
        PageTempleEntity pageTempleEntity = new PageTempleEntity();
        pageTempleEntity.setTempleId(UUID.randomUUID().toString());
        pageTempleEntity.setAppId("PRM");
        pageTempleEntity.setName("首页");
        pageTempleEntity.setSourceType("system");
        pageTempleEntity.setScopes(Lists.newArrayList("OR-1001","OR-1002"));
        pageTempleEntity.setSourceId(UUID.randomUUID().toString());
        pageTempleEntity.setAppPageId(UUID.randomUUID().toString());
        pageTempleEntity.setWebMenuId(UUID.randomUUID().toString());
        pageTempleEntity.setWebPageId(UUID.randomUUID().toString());
        pageTempleEntity.setCreatorId(1007);
        pageTempleEntity.setCreateTime(System.currentTimeMillis());
        pageTempleEntity.setUpdaterId(1007);
        pageTempleEntity.setUpdateTime(System.currentTimeMillis());
        pageTempleEntity.setTenantId(2);
        pageTempleEntity.setType("web");
        String json = gson.toJson(pageTempleEntity);
        System.out.println(json);
    }
    @Test
    public void buildSaveWebPageTempleArg(){
        SaveWebPageTempleArg arg = new SaveWebPageTempleArg();
        List<String> outerRoleIdList = Lists.newArrayList();
        {
            outerRoleIdList.add("5b6817bbe4b066655a6397e4");
            outerRoleIdList.add("5b73e865e4b05966d4359bd1");
            outerRoleIdList.add("5bb03a77e4b0fecbcae791b6");
            outerRoleIdList.add("5ce3a9a5e4b02fcc17744a19");
            outerRoleIdList.add("5b7a657fe4b071c64778aaae");
            outerRoleIdList.add("5b6a5921e4b0199a46f03d55");
            outerRoleIdList.add("5b7639a2e4b05348496cf46b");
        }
        arg.setOuterRoleIdList(outerRoleIdList);
        String json = gson.toJson(arg);
        System.out.println(json);
    }

    @Test
    public void homePageLayoutTool() {
        String json = "[\n" +
                "    {\n" +
                "        \"ToolID\":\"Tool_Duplicate\",\n" +
                "        \"ToolName\":\"查重\",\n" +
                "        \"ToolType\":3,\n" +
                "        \"IsShow\":true,\n" +
                "        \"URL\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"ToolID\":\"Tool_Import\",\n" +
                "        \"ToolName\":\"导入\",\n" +
                "        \"ToolType\":3,\n" +
                "        \"IsShow\":true,\n" +
                "        \"URL\":\"\"\n" +
                "    }\n" +
                "]";
        JSONArray jsonArray = JSONObject.parseArray(json);
        List<HomePageLayoutTool> homePageLayoutTools = jsonArray.stream().map(o -> {
            HomePageLayoutTool homePageLayoutTool = gson.fromJson(o.toString(), HomePageLayoutTool.class);
            return homePageLayoutTool;
        }).collect(Collectors.toList());


        List<JSONObject> collect = homePageLayoutTools.stream().map(homePageLayoutTool -> {
            String toJson = gson.toJson(homePageLayoutTool);
            JSONObject jsonObject1 = JSONObject.parseObject(toJson, JSONObject.class);
            return jsonObject1;
        }).collect(Collectors.toList());

        System.out.println(collect);
    }

    @Test
    public void testJson() {
        String appPage = "{\"quickCreateMenu\":{},\"components\":[{\"dataId\":\"BI_SaleReport\",\"api_name\":\"BI_SaleReport-FSAID_11490d9e\",\"cardId\":\"BI_SaleReport\",\"appId\":\"FSAID_11490d9e\",\"limit\":1.0,\"header\":\"销售简报\",\"title\":\"销售简报\",\"type\":\"saleReport\"},{\"dataId\":\"BI_58acfc2537aa1badf31d169a\",\"api_name\":\"BI_58acfc2537aa1badf31d169a-FSAID_11490d9e\",\"cardId\":\"BI_58acfc2537aa1badf31d169a\",\"appId\":\"FSAID_11490d9e\",\"limit\":1.0,\"header\":\"销售漏斗（商机金额）\",\"title\":\"销售漏斗（商机金额）\",\"type\":\"biCard\"}],\"layout_structure\":{\"layout\":[{\"components\":[[\"BI_SaleReport-FSAID_11490d9e\",\"BI_58acfc2537aa1badf31d169a-FSAID_11490d9e\"]],\"columns\":[{\"width\":\"100%\"}]}]}}";
        System.out.println(JSONObject.parseObject(appPage));
    }

    @Test
    public void testHomePageJSON() {
        String homePageComponent = "{\"biCard_16086958281580\":{\"api_name\":\"biCard_16086958281580\",\"limit\":1,\"type\":\"biCard\",\"props\":{\"dataId\":\"BI_5fe2bb632e0cdb00019d057e\",\"api_name\":\"biCard_16086958281580\",\"appId\":\"FSAID_11490d9e\",\"cardId\":\"PS_Bi\",\"type\":3,\"title\":\"代理通-商机2.0预置\",\"propsType\":3}},\"filters\":{\"api_name\":\"filters\",\"limit\":1,\"type\":\"filters\",\"props\":{\"buttons\":[],\"dataId\":\"filters\",\"api_name\":\"filters\",\"related_list_name\":\"\",\"cardId\":\"filters\",\"header\":\"Filter\",\"nameI18nKey\":\"webpage_homepage.filter\",\"title\":\"Filter\"}},\"biCard_16086958543820\":{\"api_name\":\"biCard_16086958543820\",\"limit\":1,\"type\":\"biCard\",\"props\":{\"dataId\":\"BI_5fe2bd782e0cdb00019d05d6\",\"api_name\":\"biCard_16086958543820\",\"appId\":\"FSAID_11490c84\",\"cardId\":\"PS_Bi\",\"type\":3,\"title\":\"订货通-联系人\",\"propsType\":3}},\"biCard_16086958543822\":{\"api_name\":\"biCard_16086958543822\",\"limit\":1,\"type\":\"biCard\",\"props\":{\"dataId\":\"BI_5fe2bbe42de354000104df97\",\"api_name\":\"biCard_16086958543822\",\"appId\":\"FSAID_11490c84\",\"cardId\":\"PS_Bi\",\"type\":2,\"title\":\"代理通-商机2.0\",\"propsType\":2}},\"biCard_16086958543821\":{\"api_name\":\"biCard_16086958543821\",\"limit\":1,\"type\":\"biCard\",\"props\":{\"dataId\":\"BI_5fe2bdfd2de354000104e036\",\"api_name\":\"biCard_16086958543821\",\"appId\":\"FSAID_11490c84\",\"cardId\":\"PS_Bi\",\"type\":2,\"title\":\"订货通-联系人图\",\"propsType\":2}}}";
        System.out.println(JSONObject.parseObject(homePageComponent));
    }
}
