package com.facishare.webpage.customer.test;


import com.fxiaoke.enterpriserelation2.arg.ListUpstreamByOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.OuterTenantSimpleData;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TestCross {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private EnterpriseRelationService enterpriseRelationService;

    private String appId = "x_web_user_defined";
    @Test
    public void testCorss(){
        ListUpstreamByOuterTenantIdOutArg arg = new ListUpstreamByOuterTenantIdOutArg();
        List<Integer> list = Lists.newArrayList(79410);
        arg.setTenantIds(Lists.newArrayList(list));
        HeaderObj headerObj = HeaderObj.newInstance(0);
        headerObj.setAppId("x_web_user_defined");
        RestResult<List<OuterTenantSimpleData>> listRestResult;
        List<Integer> tenantIds = Lists.newArrayList();
        try {
            RestResult<Map<Integer, List<OuterTenantSimpleData>>> mapRestResult =
                    enterpriseRelationService.batchListUpstreamByTenantIds(headerObj, arg);
            Map<Integer, List<OuterTenantSimpleData>> data = mapRestResult.getData();
            for (int tenantId : list) {
                List<OuterTenantSimpleData> outerTenantSimpleData = data.get(tenantId);
                for (OuterTenantSimpleData outerTenantSimple : outerTenantSimpleData) {
                    tenantIds.add(outerTenantSimple.getTenantId());
                }
            }

        } catch (Exception e) {

        }
        System.out.println(tenantIds);
    }

}
