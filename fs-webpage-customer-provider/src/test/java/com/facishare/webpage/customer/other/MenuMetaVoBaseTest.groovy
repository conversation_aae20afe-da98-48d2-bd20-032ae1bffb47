package com.facishare.webpage.customer.other

import com.alibaba.fastjson.JSON
import com.facishare.cep.plugin.model.OuterUserInfo
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.organization.api.service.EmployeeProviderService
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.qixin.converter.QXEIEAConverter
import com.facishare.qixin.objgroup.common.service.PaaSAppAuthService
import com.facishare.qixin.objgroup.common.service.PaasApiBusV3Service
import com.facishare.qixin.permission.filter.PermissionFilterManager
import com.facishare.qixin.relation.rest.RelationApiNamesConfig
import com.facishare.uc.api.service.EnterpriseEditionService
import com.facishare.webpage.customer.api.model.result.BaseApiResult
import com.facishare.webpage.customer.api.service.TenantPageTempleService
import com.facishare.webpage.customer.dao.TenantMenuDao
import com.facishare.webpage.customer.dao.TenantMenuDaoImpl
import com.facishare.webpage.customer.dao.entity.MenuDataEntity
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity

import com.facishare.webpage.customer.service.RemoteService
import com.facishare.webpage.customer.service.TenantMenuService
import com.facishare.webpage.customer.service.impl.RemoteServiceImpl
import com.facishare.webpage.customer.service.impl.TenantMenuServiceImpl
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.AppAssociationObjectResult
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule;
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia;
import spock.lang.Specification

/**
 * Created by shecheng on 19/10/21.
 */
class MenuMetaVoBaseTest extends Specification {

    public final FongoRule fongoRule = new FongoRule(false)
    public Datastore datastore;
    public TenantMenuDao tenantMenuDao
    public TenantMenuService tenantMenuService
    public RemoteService remoteService
    public String appId = "FSAID_11490d9e"
    public String objectApiName1 = "AccountObj"
    public String objectName1 = "客户"
    public String type_menu = "menu"
    public String type_group = "group"

    public String objectApiName2 = "ContactObj"
    public String objectName2 = "联系人"

    public String menuDetails = "[{\"api_name\":\"LeadsPoolObj\",\"iconPathMenu\":\"A_201907_02_77abe36d634647d4a589c89c18c52d61.svg\",\"deviceType\":[\"web\",\"mobile\"],\"iconPathHome\":\"A_201907_02_77abe36d634647d4a589c89c18c52d61.svg\",\"displayName\":\"线索池\",\"beginVersion\":\"0\",\"endVersion\":\"0\",\"url\":{\"mobile\":\"crm/cluepool\",\"web\":\"crm/cluepool\",\"H5\":\"crm/cluepool\",\"default\":\"crm/cluepool\"},\"isActive\":true,\"itemType\":\"menu\",\"privilegeAction\":[\"List\"]},{\"api_name\":\"HighSeasObj\",\"iconPathMenu\":\"A_201907_02_3d067987f2b640b0b2d36f16b96a8ffb.svg\",\"deviceType\":[\"web\",\"mobile\"],\"iconPathHome\":\"A_201908_06_e91e10410a0b4f8b9466d21b74bced4e.svg\",\"displayName\":\"公海\",\"beginVersion\":\"0\",\"endVersion\":\"0\",\"url\":{\"mobile\":\"crm/highseas\",\"web\":\"crm/highseas\",\"H5\":\"crm/highseas\",\"default\":\"crm/highseas\"},\"isActive\":true,\"itemType\":\"menu\",\"privilegeAction\":[\"List\"]}]"
    public ObjectDescribe objectDescribe1
    public ObjectDescribe objectDescribe2
    public List<ObjectDescribe> describes
    public List<AppAssociationObjectResult> appAssociationObjectResultList = Lists.newArrayList()
    public RestResult<List<AppAssociationObjectResult>> objectResult = new RestResult<List<AppAssociationObjectResult>>()
    public Map<String, List<String>> menuConfig = new HashMap<>()
    public Map<String,String> iconPathMap = new HashMap<>()
    public List<String> prmApiNames = Lists.newArrayList("LeadsPoolObj", "HighSeasObj")
    public String menuId = "menuId001"
    public String templeId = "templeId001"
    public String sourceId = "sourceId001"
    public long time = 1572345474000
    public UserInfo userInfo
    public OuterUserInfo outerUserInfo
    public Map<String, List<String>> functionCodes = new HashMap<>()



    def relationApiNamesConfig = Mock(RelationApiNamesConfig.class)
    def qxEIEAConverter = Mock(QXEIEAConverter.class)
    def paasApiBusV3Service = Mock(PaasApiBusV3Service.class)
    def paaSAppAuthService = Mock(PaaSAppAuthService.class)
    def employeeProviderService = Mock(EmployeeProviderService.class)
    def enterpriseEditionService = Mock(EnterpriseEditionService.class)
    def tenantPageTempleService = Mock(TenantPageTempleService.class)
    def permissionFilterManager = Mock(PermissionFilterManager.class)

    private ExpectedException exception = ExpectedException.none()

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule)

    def setup() {
        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test-menu")
        tenantMenuDao = new TenantMenuDaoImpl()
        tenantMenuDao.datastore = datastore
        menuDetailList = JSON.parseArray(menuDetails, MenuDetail.class)

        menuConfig.put(appId, prmApiNames)
//        menuConfigService.menuConfig = menuConfig
//        menuConfigService.svgIconPathMap = menuConfig
//        menuConfigService.menuConfig = menuConfig
//        menuConfigService.menuConfig = menuConfig
        tenantMenuService = new TenantMenuServiceImpl()
        tenantMenuService.tenantMenuDao = tenantMenuDao
        remoteService = new RemoteServiceImpl()
        remoteService.paaSAppAuthService = paaSAppAuthService
        remoteService.relationApiNamesConfig = relationApiNamesConfig
        remoteService.employeeProviderService = employeeProviderService
        remoteService.enterpriseEditionService = enterpriseEditionService
        remoteService.paasApiBusV3Service = paasApiBusV3Service
        remoteService.permissionFilterManager = permissionFilterManager


        tenantMenuService.remoteService = remoteService
        tenantMenuService.qxEIEAConverter = qxEIEAConverter
        tenantMenuService.menuConfigService = menuConfigService
        createMessage()
    }

    def createMessage() {
        userInfo = new UserInfo()
        userInfo.setEnterpriseId(71568)
        userInfo.setEmployeeId(1000)
        outerUserInfo = new OuterUserInfo()
        outerUserInfo.setOutTenantId(74183)
        outerUserInfo.setOutUserId(1000)
        objectDescribe1 = new ObjectDescribe()
        objectDescribe1.setApiName(objectApiName1)
        objectDescribe1.setDisplayName("客户")
        objectDescribe1.setIsActive(true)
        objectDescribe2 = new ObjectDescribe()
        objectDescribe2.setApiName(objectApiName2)
        objectDescribe2.setDisplayName("联系人")
        objectDescribe2.setIsActive(true)

        AppAssociationObjectResult objectData1 = new AppAssociationObjectResult()
        objectData1.setObjectApiName(objectApiName1)
        appAssociationObjectResultList.add(objectData1)
        functionCodes.put(objectApiName1, Lists.newArrayList("List", "Add"))
        AppAssociationObjectResult objectData2 = new AppAssociationObjectResult()
        objectData2.setObjectApiName(objectApiName2)
        appAssociationObjectResultList.add(objectData2)
        functionCodes.put(objectApiName2, Lists.newArrayList("List", "Add"))
        objectResult.data = appAssociationObjectResultList


        for (int i = 0; i < menuDetailList.size(); i++) {
            menuDetailMap.put(menuDetailList.get(i).getApiName(), menuDetailList.get(i))
            functionCodes.put(menuDetailList.get(i).getApiName(), Lists.newArrayList("List"))
        }
        describes = Lists.newArrayList()
        describes.add(objectDescribe1)
        describes.add(objectDescribe2)

        BaseApiResult permissionResult = new BaseApiResult()
        permissionResult.content = Boolean.TRUE

        paasApiBusV3Service.findAllDescribe(_, _) >>> [describes]
        tenantPageTempleService.checkUserPermission(_) >>> [permissionResult]
        relationApiNamesConfig.getSortedApiNames(_) >>> [objectResult]
        qxEIEAConverter.enterpriseIdToEa(_) >>> ["71568"]
        permissionFilterManager.filterFunctionPermission(_) >>> [functionCodes]

        def entity = new TenantMenuEntity()
        entity.setId(menuId)
        entity.setAppTemplateId(templeId)
        entity.setTenantId(userInfo.getEnterpriseId())
        entity.setAppId(appId)
        entity.setStatus(0)
        entity.setSourceType("customer")
        entity.setSourceId(sourceId)
        entity.setCreatorId(userInfo.getEmployeeId())
        entity.setUpdaterId(userInfo.getEmployeeId())
        entity.setCreateTime(time)
        entity.setUpdateTime(time)
        def menuDataEntities = []
        def menu1 = new MenuDataEntity()
        menu1.setApiName(objectApiName1)
        menu1.setIsHidden(false)
        menu1.setType(type_menu)
        menu1.setName(objectName1)
        menu1.setOrderNumber(10)
        menuDataEntities.add(menu1)

        def menu2 = new MenuDataEntity()
        menu2.setApiName(objectApiName2)
        menu2.setIsHidden(false)
        menu2.setType(type_menu)
        menu2.setName(objectName2)
        menu2.setOrderNumber(20)
        menuDataEntities.add(menu2)
        entity.setMenuDataEntities(menuDataEntities)
        def tenantMenuEntity = tenantMenuDao.save(entity)
    }

}
