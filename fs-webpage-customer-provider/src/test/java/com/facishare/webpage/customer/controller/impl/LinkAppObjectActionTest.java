package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.webpage.customer.api.model.arg.GetAllowedAddObjectListArg;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.ListObjectByPageArg;
import com.facishare.webpage.customer.api.model.result.GetAllowedAddObjectListResult;
import com.facishare.webpage.customer.api.model.result.GetLinkAppAssociationObjectListResult;
import com.facishare.webpage.customer.api.service.LinkAppRestService;
import com.facishare.webpage.customer.controller.LinkAppFunctionPermissionAction;
import com.facishare.webpage.customer.service.impl.LinkAppObjectAssociationServiceImpl;
import com.facishare.webpage.customer.util.GeneralUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/2/24
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class LinkAppObjectActionTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private LinkAppFunctionPermissionAction linkAppFunctionPermissionAction;
    @Autowired
    private LinkAppRestService linkAppRestService;
    @Autowired
    QuotaService quotaService;
    @Autowired
    private LinkAppFunctionPermissionActionImpl linkAppFunctionPermissionAction1;

    @Autowired
    private LinkAppObjectAssociationServiceImpl linkAppObjectAssociationService;

    @Test
    public void getLinkAppAssociationObjectList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetLinkAppAssociationObjectListArg arg = new GetLinkAppAssociationObjectListArg();
        arg.setAppId("FSAID_1149103f");
        GetLinkAppAssociationObjectListResult result = linkAppFunctionPermissionAction.listAssociationObjects(userInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void getAllowedAddObjectList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetAllowedAddObjectListArg arg = new GetAllowedAddObjectListArg();
        arg.setAppId("FSAID_1149103f");
        GetAllowedAddObjectListResult result = linkAppFunctionPermissionAction.listAllowedAddObjects(userInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void listObjectByPage() {
        ListObjectByPageArg arg =  new ListObjectByPageArg();
        arg.setLastId(1);
        arg.setLimit(100);
        //linkAppRestService.listObjectByPage(new HashMap<>(), arg);
        //linkAppRestService.listUpstreamObjectByPage(new HashMap<>(), arg);
    }

    @Test
    public void deleteShareRuleByApiName() {
        linkAppFunctionPermissionAction1.deleteShareRuleByApiName("fsceshi003",1007,"onjectId");
        linkAppObjectAssociationService.deleteByPolicysAndApiName("fsceshi003","FSAID_1149103f","onjectId");
        linkAppObjectAssociationService.sendLinkAppObjectAssociationInnerEvent("fsceshi003","FSAID_1149103f","onjectId");
    }

}
