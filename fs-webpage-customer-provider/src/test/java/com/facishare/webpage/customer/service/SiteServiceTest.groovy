package com.facishare.webpage.customer.service

import com.facishare.webpage.customer.api.exception.WebPageException
import com.facishare.webpage.customer.controller.model.arg.portal.*
import com.facishare.webpage.customer.api.model.User
import com.facishare.webpage.customer.dao.*
import com.facishare.webpage.customer.dao.entity.*
import com.facishare.webpage.customer.service.impl.SiteServiceImpl
import spock.lang.Specification

class SiteServiceTest extends Specification {

    SiteServiceImpl siteService
    SiteDraftEntityDao siteDraftEntityDaoMock = Mock(SiteDraftEntityDao)
    ThemeLayoutEntityDao themeLayoutEntityDaoMock = Mock(ThemeLayoutEntityDao)
    TenantMenuDao tenantMenuDaoMock = Mock(TenantMenuDao)
    HomePageLayoutDao homePageLayoutDaoMock = Mock(HomePageLayoutDao)
    SiteDraftService siteDraftServiceMock = Mock(SiteDraftService)

    SiteEntityDao siteEntityDao = Mock()

    def setup() {
        siteService = new SiteServiceImpl(
                siteEntityDao: siteEntityDao,
                siteDraftEntityDao: siteDraftEntityDaoMock,
                themeLayoutEntityDao: themeLayoutEntityDaoMock,
                tenantMenuDao: tenantMenuDaoMock,
                homePageLayoutDao: homePageLayoutDaoMock,
                siteDraftService: siteDraftServiceMock
        )


    }

    def "test publishSiteDraft with valid data"() {
        given:
        User user = new User(tenantId: 74255)
        PublishSiteDraftArg arg = new PublishSiteDraftArg(siteApiName: "site1")
        SiteDraftEntity siteDraftEntity = new SiteDraftEntity(
                siteApiName: "site1",
                draftData: '{"themeLayoutList":[{"apiName":"theme_layout_xx001","layoutStructure":{},"name":"主题布局1"}],"menuList":[{"apiName":"menu_template_xx001","menuItems":[{"number":1,"displayName":"CRM菜单分组","id":"menu_group_xx001","type":"group"},{"number":1,"displayName":"销售订单","appId":"CRM","pid":"menu_group_xx001","id":"SalesOrderObj","referenceApiname":"SalesOrderObj","type":"menu"},{"number":2,"displayName":"百度","metaType":"url","pid":"menu_group_xx001","id":"url_xx001","type":"menu","url":"http://www.baidu.com"}],"name":"菜单模板1","description":"CRM菜单"}],"pageList":[{"apiName":"page_xx001","isHomePage":true,"layoutStructure":{},"name":"默认首页1111","description":"这是默认访问的页面","themeLayoutApiName":"theme_layout_xx001"},{"apiName":"page_xx002","isHomePage":false,"layoutStructure":{},"name":"页面2","themeLayoutApiName":"theme_layout_xx001"}]}',
                appId: "appId123",
                tenantId: 74255,
                creatorId: 1001,
                createTime: System.currentTimeMillis(),
                updaterId: 1001,
                updateTime: System.currentTimeMillis()
        )
        siteDraftEntityDaoMock.findDraftBySiteApiName(_,_) >> siteDraftEntity
        def oldThemeLayoutEntityList = [
                new ThemeLayoutEntity(id: "ThemeLayoutEntityId1",apiName: "api1", name: "layout1", layoutStructure: "{}", siteApiName: "site1", appId: "appId1", status: 1, sourceType: "source1", tenantId: 74255, creatorId: 1001, createTime: System.currentTimeMillis(), updaterId: 1001, updateTime: System.currentTimeMillis()),
                new ThemeLayoutEntity(id: "ThemeLayoutEntityId2",apiName: "api2", name: "layout2", layoutStructure: "{}", siteApiName: "site1", appId: "appId2", status: 1, sourceType: "source2", tenantId: 74255, creatorId: 1002, createTime: System.currentTimeMillis(), updaterId: 1002, updateTime: System.currentTimeMillis())
        ]
        themeLayoutEntityDaoMock.findBySiteApiNameIncludeDisable(_,_) >> oldThemeLayoutEntityList
        def tenantMenuEntity = new TenantMenuEntity(
                id: "menuApiId1",
                apiName: "menuApi1",
                tenantId: 74255,
                appType: 1,
                appId: "appId1",
                siteApiName: "site1",
                priorityLevel: 1,
                appTemplateId: "template1",
                menuDataEntities: [],
                scopes: ["scope1", "scope2"],
                name: "Menu1",
                description: "Description of Menu1",
                creatorId: 1001,
                createTime: System.currentTimeMillis(),
                updaterId: 1001,
                updateTime: System.currentTimeMillis(),
                isChange: false,
                status: 1,
                sourceType: "SYSTEM",
                sourceId: "source1",
                isShowMenuIcon: true,
                hiddenQuickCreate: false
        )

        tenantMenuDaoMock.findBySiteApiNameIncludeDisable(_,_,_) >> [tenantMenuEntity]

        def homePageLayoutEntity = new HomePageLayoutEntity(
                layoutId: "layout1",
                tenantId: 74255,
                apiName: "api1",
                appId: "appId",
                appType: 1,
                appTemplateId: "template1",
                layoutType: 1,
                priorityLevel: 1,
                applyType: 0,
                scopes: ["scope1", "scope2"],
                homePageCardEntityList: ["card1", "card2"],
                sourceType: "SYSTEM",
                sourceId: "source1",
                status: 1,
                name: "HomePage1",
                description: "Description of HomePage1",
                isChange: false,
                creatorId: 1001,
                createTime: new Date(),
                updaterId: 1001,
                updateTime: new Date(),
                customerLayout: "{\"key\":\"value\"}",
                dataVersion: 100,
                pageLayoutType: 3,
                pageMultiType: 1,
                defaultLabelIndex: 0,
                customerLayoutList: ["{\"key\":\"value1\"}", "{\"key\":\"value2\"}"],
                iconIndex: 0,
                fromOldCrmHomePage: false,
                siteApiName: "site1",
                themeLayoutApiName: "theme1",
                isHomePage: true
        )

        // 配置 mock 对象的行为
        homePageLayoutDaoMock.findBySiteApiNameIncludeDisable(_,_,_) >> [homePageLayoutEntity]
        when:
        SiteDraftEntity result = siteService.publishSiteDraft(user, arg)


        then:
        result == siteDraftEntity
        1 * siteDraftEntityDaoMock.findDraftBySiteApiName(user.tenantId, arg.siteApiName) >> siteDraftEntity
        1 * themeLayoutEntityDaoMock.batchSave(_, _)
        1 * tenantMenuDaoMock.batchSave(_, _)
        1 * homePageLayoutDaoMock.batchSave(_, _)
    }

    def "test publishSiteDraft with valid data and no data"() {
        given:
        User user = new User(tenantId: 74255)
        PublishSiteDraftArg arg = new PublishSiteDraftArg(siteApiName: "site1")
        SiteDraftEntity siteDraftEntity = new SiteDraftEntity(
                siteApiName: "site1",
                draftData: '{"themeLayoutList":[{"apiName":"theme_layout_xx001","layoutStructure":{},"name":"主题布局1"}],"menuList":[{"apiName":"menu_template_xx001","menuItems":[{"number":1,"displayName":"CRM菜单分组","id":"menu_group_xx001","type":"group"},{"number":1,"displayName":"销售订单","appId":"CRM","pid":"menu_group_xx001","id":"SalesOrderObj","referenceApiname":"SalesOrderObj","type":"menu"},{"number":2,"displayName":"百度","metaType":"url","pid":"menu_group_xx001","id":"url_xx001","type":"menu","url":"http://www.baidu.com"}],"name":"菜单模板1","description":"CRM菜单"}],"pageList":[{"apiName":"page_xx001","isHomePage":true,"layoutStructure":{},"name":"默认首页1111","description":"这是默认访问的页面","themeLayoutApiName":"theme_layout_xx001"},{"apiName":"page_xx002","isHomePage":false,"layoutStructure":{},"name":"页面2","themeLayoutApiName":"theme_layout_xx001"}]}',
                appId: "appId123",
                tenantId: 74255,
                creatorId: 1001,
                createTime: System.currentTimeMillis(),
                updaterId: 1001,
                updateTime: System.currentTimeMillis()
        )
        siteDraftEntityDaoMock.findDraftBySiteApiName(_,_) >> siteDraftEntity

        when:
        SiteDraftEntity result = siteService.publishSiteDraft(user, arg)

        then:
        result == siteDraftEntity
        1 * siteDraftEntityDaoMock.findDraftBySiteApiName(user.tenantId, arg.siteApiName) >> siteDraftEntity
        1 * themeLayoutEntityDaoMock.batchSave(_, _)
        1 * tenantMenuDaoMock.batchSave(_, _)
        1 * homePageLayoutDaoMock.batchSave(_, _)
    }

    def "test publishSiteDraft with null draft"() {
        given:
        User user = new User(tenantId: 74255)
        PublishSiteDraftArg arg = new PublishSiteDraftArg(siteApiName: "site1")
        siteDraftEntityDaoMock.findDraftBySiteApiName(user.tenantId, arg.siteApiName) >> null

        when:
        siteService.publishSiteDraft(user, arg)

        then:
        thrown(WebPageException)
        1 * siteDraftEntityDaoMock.findDraftBySiteApiName(_, _)
    }

    def "createSiteInfo with valid input should call save method on siteEntityDao"() {
        given:
        def user = User.of(1, 1)
        def siteInfoDTO = new SiteInfoDTO(
                name: "test site",
                apiName: "test_api",
                appId: "test_app",
                clientType: "H5"
        )
        def siteEntity = siteInfoDTO.toEntity()

        when:
        siteService.createSiteInfo(user, siteInfoDTO)

        then:
        1 * siteEntityDao.save(user, siteEntity)
    }

    def "test validateThemeLayout with duplicate apiNames"() {
        given:
        User user = new User(tenantId: 74255)
        List<ThemeLayoutEntity> themeLayoutEntityList = [
                new ThemeLayoutEntity(apiName: "layout1"),
                new ThemeLayoutEntity(apiName: "layout1")
        ]

        when:
        siteService.validateThemeLayout(user, themeLayoutEntityList)

        then:
        thrown(WebPageException)
    }

    def "test validatePage with duplicate apiNames"() {
        given:
        User user = new User(tenantId: 74255)
        List<HomePageLayoutEntity> pageLayoutEntityList = [
                new HomePageLayoutEntity(apiName: "page1"),
                new HomePageLayoutEntity(apiName: "page1")
        ]

        when:
        siteService.validatePage(user, pageLayoutEntityList)

        then:
        thrown(WebPageException)
    }

    def "test updateSitePages with publish"() {
        given:
        def user = new User(tenantId: 74255, userId: 0)
        def arg = new UpdateSitePagesArg(
                siteApiName: "site1",
                isPublish: true,
                themeLayoutList: [new ThemeLayoutDTO(apiName: "theme_layout_xx001", name: "主题布局1")],
                menuList: [new SiteMenuDTO(apiName: "menu_template_xx001", name: "菜单模板1")],
                pageList: [new SitePageDTO(apiName: "page_xx001", name: "页面1")]
        )

        def siteEntity = new SiteEntity(appId: "appId123", apiName: "site1", description: "Test Site")

        when:
        siteService.updateSitePages(user, arg)

        then:
        1 * siteEntityDao.findByApiNameIncludeDisable(74255, "site1") >> siteEntity
        1 * themeLayoutEntityDaoMock.batchSave(user, _)
        1 * tenantMenuDaoMock.batchSave(user, _)
        1 * homePageLayoutDaoMock.batchSave(user, _)
        1 * siteDraftServiceMock.creatOrUpdateSiteDraftInfo(user, _)
    }

    def "test updateSitePages without publish"() {
        given:
        def user = new User(tenantId: 74255, userId: 0)
        def arg = new UpdateSitePagesArg(
                siteApiName: "site1",
                isPublish: false,
                themeLayoutList: [new ThemeLayoutDTO(apiName: "theme_layout_xx001", name: "主题布局1")],
                menuList: [new SiteMenuDTO(apiName: "menu_template_xx001", name: "菜单模板1")],
                pageList: [new SitePageDTO(apiName: "page_xx001", name: "页面1")]
        )

        def siteEntity = new SiteEntity(appId: "appId123", apiName: "site1", description: "Test Site")

        when:
        siteService.updateSitePages(user, arg)

        then:
        1 * siteEntityDao.findByApiNameIncludeDisable(74255, "site1") >> siteEntity
        0 * themeLayoutEntityDaoMock.batchSave(user, _)
        0 * tenantMenuDaoMock.batchSave(user, _)
        0 * homePageLayoutDaoMock.batchSave(user, _)
        1 * siteDraftServiceMock.creatOrUpdateSiteDraftInfo(user, _)
    }

    def "test updateSitePages with site not found"() {
        given:
        def user = new User(tenantId: 74255, userId: 0)
        def arg = new UpdateSitePagesArg(
                siteApiName: "site1",
                isPublish: true,
                themeLayoutList: [new ThemeLayoutDTO(apiName: "theme_layout_xx001", name: "主题布局1")],
                menuList: [new SiteMenuDTO(apiName: "menu_template_xx001", name: "菜单模板1")],
                pageList: [new SitePageDTO(apiName: "page_xx001", name: "页面1")]
        )

        when:
        siteService.updateSitePages(user, arg)

        then:
        1 * siteEntityDao.findByApiNameIncludeDisable(74255, "site1") >> null
        0 * themeLayoutEntityDaoMock.batchSave(user, _)
        0 * tenantMenuDaoMock.batchSave(user, _)
        0 * homePageLayoutDaoMock.batchSave(user, _)
        0 * siteDraftServiceMock.creatOrUpdateSiteDraftInfo(user, _)
    }
}
