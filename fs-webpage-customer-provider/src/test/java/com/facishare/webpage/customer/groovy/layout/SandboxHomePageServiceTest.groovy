package com.facishare.webpage.customer.groovy.layout

import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity
import com.facishare.webpage.customer.groovy.layout.HomePageBasicTest

/**
 * Created by zhangyu on 2020/3/30
 */
class SandboxHomePageServiceTest extends HomePageBasicTest {


    def "copyHomePage"() {
        given:
        def fromTenantId = tenantId
        def toTenantId = 71575
        when:
        def pageLayoutEntities = homePageBaseService.copyHomePage(fromTenantId, toTenantId, BizType.CRM.type)
        def pageLayoutEntity
        for (HomePageLayoutEntity homePageLayoutEntity : pageLayoutEntities) {
            if (homePageLayoutEntity.getSourceType().equals(sourceType)) {
                pageLayoutEntity = homePageLayoutEntity
                break
            }
        }
        then:
        pageLayoutEntity.updateTime == updateTime
        pageLayoutEntity.updaterId == updaterId
        pageLayoutEntity.tenantId == toTenantId
        pageLayoutEntity.sourceType == sourceType
        pageLayoutEntity.homePageCardEntityList == homePageCardEntityList
        pageLayoutEntity.scopes == scopes
        pageLayoutEntity.creatorId == creatorId
        pageLayoutEntity.createTime == createTime
        pageLayoutEntity.change == isChange
        pageLayoutEntity.appType == appType
        pageLayoutEntity.appId == appId
        pageLayoutEntity.getType == name
        pageLayoutEntity.description == description
        pageLayoutEntity.status == status
        pageLayoutEntity.layoutType == layoutType
        pageLayoutEntities.size() == 2
    }

    def "destroyHomePage"(){
        given:
        def tenantId = tenantId
        when:
        def destroyHomePage = homePageBaseService.destroyHomePage(tenantId)
        then:
        destroyHomePage == 1
    }

}
