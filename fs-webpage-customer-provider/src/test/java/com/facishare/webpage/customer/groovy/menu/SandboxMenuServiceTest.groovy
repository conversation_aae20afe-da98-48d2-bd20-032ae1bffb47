package com.facishare.webpage.customer.groovy.menu

import com.facishare.webpage.customer.api.constant.BizType
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity
import com.facishare.webpage.customer.groovy.menu.MenuBasicTest

/**
 * Created by zhangyu on 2020/3/31
 */
class SandboxMenuServiceTest extends MenuBasicTest {

    def "copyTenantMenu"() {
        given:
        def fromTenantId = tenantId
        def toTenantId = 71575
        when:
        def copyTenantMenus = tenantMenuService.copyTenantMenu(fromTenantId, toTenantId, BizType.CRM.type)
        TenantMenuEntity tenantMenuWithCustomer
        for (TenantMenuEntity tenantMenuEntity : copyTenantMenus) {
            if (tenantMenuEntity.sourceType.equals(SourceType.CUSTOMER)) {
                tenantMenuWithCustomer = tenantMenuEntity
                break
            }
        }
        then:
        tenantMenuWithCustomer.menuDataEntities == menuDataEntityList
        tenantMenuWithCustomer.updateTime == updateTime
        tenantMenuWithCustomer.updaterId == updaterId
        tenantMenuWithCustomer.tenantId == toTenantId
        tenantMenuWithCustomer.scopes == scopes
        tenantMenuWithCustomer.creatorId == creatorId
        tenantMenuWithCustomer.createTime == createTime
        tenantMenuWithCustomer.change == isChange
        tenantMenuWithCustomer.appType == appType
        tenantMenuWithCustomer.appId == appId
        tenantMenuWithCustomer.description == description
        tenantMenuWithCustomer.status == status
        tenantMenuWithCustomer.name == name
        copyTenantMenus.size() == 2

    }

    def "destroyMenu"() {
        given:
        def tenantId = tenantId
        when:
        def tenantMenus = tenantMenuService.destroyTenantMenu(tenantId)
        then:
        tenantMenus == 1
    }

}
