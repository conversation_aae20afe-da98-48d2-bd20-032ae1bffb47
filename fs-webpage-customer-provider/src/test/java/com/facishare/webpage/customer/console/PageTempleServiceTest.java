package com.facishare.webpage.customer.console;

import com.facishare.webpage.customer.api.console.PageTempleService;
import com.facishare.webpage.customer.api.console.arg.QueryPageTempleArg;
import com.facishare.webpage.customer.api.console.result.QueryPageTempleResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class PageTempleServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private PageTempleService pageTempleService;

    @Test
    public void testQueryTemple(){
        QueryPageTempleArg arg = new QueryPageTempleArg();
        arg.setTenantId(71568);
        arg.setAppId("FSAID_11490d9e");
        arg.setType("app");
        QueryPageTempleResult result = pageTempleService.queryPageTemple(arg);
        System.out.println(result);
    }

}
