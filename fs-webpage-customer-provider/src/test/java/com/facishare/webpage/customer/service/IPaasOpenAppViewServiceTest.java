package com.facishare.webpage.customer.service;

import com.fxiaoke.appcenter.restapi.arg.QueryAppListForManageArg;
import com.fxiaoke.appcenter.restapi.arg.QueryPaasAppViewsForEaVoArg;
import com.fxiaoke.appcenter.restapi.arg.QueryPaasAppViewsForUserVoArg;
import com.fxiaoke.appcenter.restapi.common.BaseResult;
import com.fxiaoke.appcenter.restapi.common.FsUserVO;
import com.fxiaoke.appcenter.restapi.common.HeaderObj;
import com.fxiaoke.appcenter.restapi.enums.AppAccessTypeEnum;
import com.fxiaoke.appcenter.restapi.result.OpenAppForManageVO;
import com.fxiaoke.appcenter.restapi.result.PaasAppViewsForEaVo;
import com.fxiaoke.appcenter.restapi.service.IPaasOpenAppViewService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/8/21
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-remote.xml")
public class IPaasOpenAppViewServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private IPaasOpenAppViewService iPaasOpenAppViewService;

    @Test
    public void getManagerApplicationDataList() {

        QueryPaasAppViewsForEaVoArg arg = new QueryPaasAppViewsForEaVoArg();
        arg.setFsEa("fsceshi003");
        arg.setLang(Locale.CHINA.toString());
        arg.setAppAccessTypeEnum(AppAccessTypeEnum.WEB);

        HeaderObj headerObj = HeaderObj.newInstance("71574");

        BaseResult<List<PaasAppViewsForEaVo>> result = iPaasOpenAppViewService.queryPaasAppViewsForEaVo(headerObj, arg);
        System.out.println("------------------------------------");
        System.out.println(result.getResult());
        System.out.println("------------------------------------");

    }

    @Test
    public void testGetManagerAppList() {
        QueryAppListForManageArg arg = new QueryAppListForManageArg();
        FsUserVO fsUserVO = new FsUserVO();
        fsUserVO.setEnterpriseAccount("fsceshi003");
        fsUserVO.setUserId(1017);
        arg.setArg1(fsUserVO);
        arg.setVersion("1.0");
        arg.setLang("en");
        HeaderObj headerObj = HeaderObj.newInstance(String.valueOf(71574));
        BaseResult<List<OpenAppForManageVO>> listBaseResult = iPaasOpenAppViewService.queryAppListForManage(headerObj, arg);

        System.out.println("---------------------------------");
        System.out.println(listBaseResult.getResult());
        System.out.println("---------------------------------");

    }

    @Test
    public void testQueryPaasAppViewsForUserVo() {
        HeaderObj headerObj = HeaderObj.newInstance(String.valueOf(80980));
        QueryPaasAppViewsForUserVoArg arg = new QueryPaasAppViewsForUserVoArg();

        FsUserVO fsUserVO = new FsUserVO();
        fsUserVO.setEnterpriseAccount("80980");
        fsUserVO.setUserId(1000);

        arg.setFsUserVO(fsUserVO);
        arg.setAppAccessTypeEnum(AppAccessTypeEnum.WEB);
        arg.setLang(Locale.CHINA.toLanguageTag());

        BaseResult<List<PaasAppViewsForEaVo>> result = iPaasOpenAppViewService.queryPaasAppViewsForUserVo(headerObj, arg);

        System.out.println("----------------------------");
        System.out.println(result.getResult());
        System.out.println("----------------------------");

    }
}
