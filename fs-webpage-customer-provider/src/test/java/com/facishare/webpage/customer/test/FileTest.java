package com.facishare.webpage.customer.test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class FileTest {

    public static void main(String[] args) throws Throwable {
        final String file = "/Users/<USER>/Downloads/test.log";
        final int count = 10000;

        new Thread(() -> {
            File file1 = new File(file);
            try {
                FileOutputStream o1 = new FileOutputStream(file1, true);
                for (int i = 0 ; i < count ; i++) {
                    o1.write(("o1="+i+"\r\n").getBytes());
                    o1.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();

        new Thread(() -> {
            File file2 = new File(file);
            try {
                FileOutputStream o2 = new FileOutputStream(file2, true);
                for (int i = 0 ; i < count ; i++) {
                    o2.write(("o2="+i+"\r\n").getBytes());
                    o2.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();

    }
}
