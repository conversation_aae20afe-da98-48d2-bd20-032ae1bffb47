package com.facishare.webpage.customer.test;

import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.LicenseObjectInfoContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/3/1
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TestLicense {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Test
    public void testLicense(){
        QueryProductArg arg = new QueryProductArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId("71574");
        arg.setLicenseContext(licenseContext);

        LicenseVersionResult result = licenseClient.queryProductVersion(arg);
        System.out.println(result.getResult().stream().map(ProductVersionPojo::getCurrentVersion).collect(Collectors.toList()));

    }

    @Test
    public void testGetApiName(){
        List<String> apNames = Lists.newArrayList();
        LicenseContext licenseContext = buildLicenseContext(79532);
        LicenseObjectInfoContext arg = new LicenseObjectInfoContext();
        arg.setContext(licenseContext);
        arg.setCrmKey("crm_manage_custom_object");
        arg.setModuleType("0");
        Result<Set<String>> queryApiNameByLicense = this.licenseClient.queryApiNameByLicense(arg);
        if (!Objects.isNull(queryApiNameByLicense)) {
            apNames = new ArrayList<>(queryApiNameByLicense.getResult()) ;
        }
        System.out.println(apNames);
    }

    private LicenseContext buildLicenseContext(int tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(tenantId));
        licenseContext.setUserId("-10000");
        return licenseContext;
    }

}
