package com.facishare.webpage.customer.covert;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.config.model.IconData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Yu
 */
public class CovertData {

    @Test
    public void covertIconData() {
        Map<String, IconData> map = Maps.newHashMap();
        for (int i = 1; i <= 30; i++) {
            IconData iconData = new IconData();
            iconData.setScope(Lists.newArrayList("FSAID_11490d9e","FSAID_11491009", BizType.PAAS.getDefaultAppId()));
            iconData.setIcon("https://a9.fspage.com/FSR/fs-qixin/static/utilitybar/icon" + i + ".svg");

            map.put("UtilityBar-" + i, iconData);
        }
        System.out.println(JSONObject.toJSONString(map));

    }
}
