package com.facishare.webpage.customer.controller.impl;

import com.facishare.fcp.biz.AuthInfo;
import com.facishare.fcp.handler.FcpServiceContext;
import com.facishare.fcp.handler.FcpServiceContextManager;
import com.facishare.fcp.protocol.FcpHeader;
import com.facishare.fcp.protocol.FcpMessageType;
import com.facishare.fcp.protocol.FcpRequest;
import com.facishare.fcp.service.DefaultFcpServiceContextManager;
import com.facishare.webpage.customer.api.model.arg.GetAllowedAddObjectListArg;
import com.facishare.webpage.customer.controller.LinkAppFunctionPermissionAction;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Locale;
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TestLinkAppFcpController  {

    @Autowired
    private LinkAppFunctionPermissionAction linkAppFunctionPermissionAction;


    FcpServiceContext context;
    FcpServiceContextManager fcpServiceContextManager;
    @Before
    public void setUp() throws Exception {
        fcpServiceContextManager = DefaultFcpServiceContextManager.getInstance();
        context = fcpServiceContextManager.getContext();
        AuthInfo authInfo = new AuthInfo();
        authInfo.setEmployeeFullId("E.fsceshi003.1017");
        authInfo.setEnterpriseId(71574);
        authInfo.setLocale(Locale.ENGLISH);
        authInfo.setClientInfo("iOS.100765000");
        context.setAuthInfo(authInfo);
        FcpRequest request = new FcpRequest(FcpMessageType.OK);
        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.CALL_ID).setValue("FSAID_11491009").build());
        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.SEQUENCE_ID).setValue(12456).build());
        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.CLIENT_INFO).setValue("iOS.100765000").build());
        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.OS_VERSION).setValue("13.2").build());
//        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.OUT_TENANT_ID).setValue(300016351).build()); //300017215 300017103
//        request.addFcpHeader(new FcpHeader.FcpHeaderBuilder(FcpHeader.FcpHeaderType.OUT_USER_ID).setValue(300099378).build()); //300098652 300098514
        fcpServiceContextManager.setFcpRequest(request);
    }


    @Test
    public void testGetAllowedAddObjectList() {
        GetAllowedAddObjectListArg arg = new GetAllowedAddObjectListArg();
       // linkAppFunctionPermissionAction.listAllowedAddObjects(arg);
    }
}



