package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhangyu on 2020/4/2
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-remote.xml")
public class SandboxService {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private TenantMenuService tenantMenuService;
    @Resource
    private HomePageBaseService homePageBaseService;

    @Test
    public void testCopy(){
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuService.copyTenantMenu(79472, 79554, BizType.CRM.getType());
        System.out.println(tenantMenuEntities);
    }

    @Test
    public void testCopyHomePage(){
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageBaseService.copyHomePage(79472, 79411, BizType.CRM.getType());
        System.out.println(homePageLayoutEntityList);
    }

}
