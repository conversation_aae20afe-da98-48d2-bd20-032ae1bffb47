package com.facishare.webpage.customer.groovy.paas

import com.facishare.organization.adapter.api.permission.enums.role.SystemRoleEnum
import com.facishare.qixin.relation.rest.FsUpstreamService
import com.facishare.webpage.customer.api.constant.SourceType
import com.facishare.webpage.customer.controller.PaaSAppAction
import com.facishare.webpage.customer.controller.impl.PaaSAppActionImpl
import com.facishare.webpage.customer.dao.PaaSAppDao
import com.facishare.webpage.customer.dao.PaaSAppDaoImpl
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity
import com.facishare.webpage.customer.groovy.MockResource
import com.facishare.webpage.customer.metadata.ApplicationService
import com.facishare.webpage.customer.metadata.CrossApplicationServiceImpl
import com.facishare.webpage.customer.metadata.InnerApplicationServiceImpl
import com.facishare.webpage.customer.metadata.factory.ApplicationFactoryBean
import com.facishare.webpage.customer.remote.RoleService
import com.facishare.webpage.customer.remote.TempFileToFormalFile
import com.facishare.webpage.customer.service.PaaSAppService
import com.facishare.webpage.customer.service.impl.PaaSAppServiceImpl
import com.fxiaoke.appcenter.restapi.common.BaseResult
import com.fxiaoke.appcenter.restapi.model.OpenAppComponentDO
import com.fxiaoke.appcenter.restapi.model.OpenAppDO
import com.fxiaoke.appcenter.restapi.result.OpenAppForManageVO
import com.fxiaoke.appcenter.restapi.result.PaasAppViewsForEaVo
import com.fxiaoke.appcenter.restapi.service.IPaasOpenAppViewService
import com.fxiaoke.appcenter.restapi.service.OpenFsUserBindAppService
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.BenchAndDownstreamAppsResult
import com.fxiaoke.enterpriserelation2.service.DownstreamService
import com.google.common.collect.Lists

/**
 * Created by zhangyu on 2020/11/28
 */
class ApplicationBasic extends MockResource {

    public PaaSAppAction paaSAppAction

    public PaaSAppService paaSAppService

    public PaaSAppDao paaSAppDao

    public CrossApplicationServiceImpl crossApplicationService

    public InnerApplicationServiceImpl innerApplicationService

    public ApplicationService applicationService

    public String OUT_WORK = "FSAID_9896e2"

    public String Portal = "FSAID_11491009"

    def tempFileToFormalFile = Mock(TempFileToFormalFile.class) {
        tNPathToAPath(_, _, _) >> ""
    }

    def roleService = Mock(RoleService.class) {
        getSystemRoleIds(_, _) >> Lists.newArrayList(SystemRoleEnum.SYSTEM_MANAGEMENT)
        checkHasPermissionByCode(_, _, _) >> true
    }

    def fsUpstreamService = Mock(FsUpstreamService.class) {
        listOpenedApps(_, _) >> null
    }

    def downstreamService = Mock(DownstreamService.class) {

        def result = new RestResult<BenchAndDownstreamAppsResult>()
        def benchAndDownstreamAppsResult = new BenchAndDownstreamAppsResult()
        benchAndDownstreamAppsResult.setDownstreamLinkApps(buildDownstreamLinkAppsDataList())
        benchAndDownstreamAppsResult.setLinkAppVos(buildLinkAppDataList())
        result.setData(benchAndDownstreamAppsResult)

        listBenchAndDownStreamAppsForTenant(_, _) >> benchAndDownstreamAppsResult

        listBenchAndDownStreamAppsForUser(_, _) >> buildUserBenchAndDownstreamAppsResult()
    }

    RestResult<BenchAndDownstreamAppsResult> buildUserBenchAndDownstreamAppsResult(){
        def result = new RestResult<BenchAndDownstreamAppsResult>()
        result.isSuccess()
        def benchAndDownstreamAppsResult = new BenchAndDownstreamAppsResult()
        benchAndDownstreamAppsResult.downstreamLinkApps = buildDownstreamLinkAppsDataList()
        benchAndDownstreamAppsResult.linkAppVos = buildLinkAppDataList()

        result.setData(benchAndDownstreamAppsResult)

        return result
    }

    //构建互联工作台的数据
    List<BenchAndDownstreamAppsResult.DownstreamLinkAppsData> buildDownstreamLinkAppsDataList() {
        def downstreamLinkAppsData = new BenchAndDownstreamAppsResult.DownstreamLinkAppsData()
        downstreamLinkAppsData.setEa("fsceshi003")
        downstreamLinkAppsData.setEnterpriseName("纷享专用内测企业003")

        def linkAppData1 = new BenchAndDownstreamAppsResult.LinkAppData()
        linkAppData1.id = "FSAID_11490c83"
        linkAppData1.name = "通知公告"
        linkAppData1.icon = "https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_07_0cc5c36d318e4803bd872c8642282de7.png&size=150_150&ea=appCenter"
        linkAppData1.introduction = "群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。"
        linkAppData1.webUrl = "#app/wechatconnect/receive/=/param-%7b%22appId%22%3a%22"

        def linkAppData2 = new BenchAndDownstreamAppsResult.LinkAppData()
        linkAppData2.id = "FSAID_11490c82"
        linkAppData2.name = "互联网盘"
        linkAppData2.icon = "https://a2.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201911_01_e96285077fe84429aa2e90a94485912d.png&size=150_150&ea=appCenter"
        linkAppData2.introduction = "可以共享文件给合作伙伴。"
        linkAppData2.webUrl = "#app/icfiles/receive/=/param-%7b%22appId%22%3a%22"

        def linkAppData3 = new BenchAndDownstreamAppsResult.LinkAppData()
        linkAppData3.id = Portal
        linkAppData3.name = "厂商门户"
        linkAppData3.icon = "https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201911_14_68aa166168fb4159b90fc7e2f79389ec.png&size=150_150&ea=appCenter"
        linkAppData3.introduction = "厂商门户"
        linkAppData3.webUrl = "https://www.ceshi112.com/XV/Home/Index#portal"

        downstreamLinkAppsData.setLinkApps(Lists.newArrayList(linkAppData1, linkAppData2, linkAppData3))


        return Lists.newArrayList(downstreamLinkAppsData)
    }

    //构建互联应用的数据
    List<BenchAndDownstreamAppsResult.LinkAppData> buildLinkAppDataList() {
        def linkAppData1 = new BenchAndDownstreamAppsResult.LinkAppData()
        linkAppData1.id = "FSAID_11490c83"
        linkAppData1.name = "通知公告"
        linkAppData1.icon = ""
        linkAppData1.introduction = "群发通知给合作伙伴，支持评论，掌握已读状态，对未读联系人一键群发提醒。"

        def linkAppData2 = new BenchAndDownstreamAppsResult.LinkAppData()
        linkAppData2.id = "FSAID_11490c82"
        linkAppData2.name = "互联网盘"
        linkAppData2.icon = ""
        linkAppData2.introduction = "可以共享文件给合作伙伴。"

        return Lists.newArrayList(linkAppData1, linkAppData2)
    }

    def openFsUserBindAppService = Mock(OpenFsUserBindAppService.class) {
        queryAppListByFsEnterpriseAccount(_, _) >> null
    }

    def iPaasOpenAppViewService = Mock(IPaasOpenAppViewService.class) {
        queryAppListForManage(_, _) >> buildOpenAppForManageVOResult()
        queryPaasAppViewsForEaVo(_, _) >> buildPaasAppViewsForEaVoResult()
        queryPaasAppViewsForUserVo(_, _) >> buildPaasAppViewsForUserVoResult()
    }

    //构建管理后台应用的返回值
    BaseResult<List<OpenAppForManageVO>> buildOpenAppForManageVOResult() {
        def result = new BaseResult<List<OpenAppForManageVO>>()
        result.success
        result.setResult(buildOpenAppForManageVOList())

        return result
    }

    //构建应用的信息
    List<OpenAppForManageVO> buildOpenAppForManageVOList() {
        def openAppForManageVO1 = new OpenAppForManageVO()
        openAppForManageVO1.appId = "FSAID_5f5e248"
        openAppForManageVO1.appName = "CRM"
        openAppForManageVO1.appDesc = "CRM作为核心业务管理模块 ，真正实现让您将客户装进口袋， 轻松掌握销售过程，提供轻量级的客户管理、销售过程管理、合同管理、市场管理和产品管理等辅助业务人员日常工作，帮助业务人员及时获取客户信息，轻松跟进客户动态，让用户从主动操作变成被动应答，工作更简单，简答的图标让业务人员直观查询业绩目标和执行情况，跳动销售策略和进程，业务信息形成格式化的数据方便管理者，运筹帷幄之中决胜千里之外。"
        openAppForManageVO1.appType = 2
        openAppForManageVO1.appMode = 1
        openAppForManageVO1.appCreater = 1
        openAppForManageVO1.createrType = 2
        openAppForManageVO1.appClass = 4
        openAppForManageVO1.serviceName = "CRM"
        openAppForManageVO1.bindStatus = 1
        openAppForManageVO1.payType = 1
        openAppForManageVO1.payStatus = 1
        openAppForManageVO1.appLogoUrl = "https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_bd51fed9404d4815827b34ca34b47270.png&size=150_150&ea=appCenter&type=WEB"

        def openAppForManageVO2 = new OpenAppForManageVO()
        openAppForManageVO2.appId = "FSAID_98978d"
        openAppForManageVO2.appName = "企信"
        openAppForManageVO2.appDesc = "企信\t企信\t企信\t企信\t企信"
        openAppForManageVO2.appType = 3
        openAppForManageVO2.appMode = 1
        openAppForManageVO2.appCreater = 1
        openAppForManageVO2.createrType = 2
        openAppForManageVO2.appClass = 2
        openAppForManageVO2.serviceName = "企信"
        openAppForManageVO2.bindStatus = 1
        openAppForManageVO2.payType = 2
        openAppForManageVO2.appLogoUrl = "https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_ac426b3b30804f2da7b95f6b1bdcddca.png&size=150_150&ea=appCenter&type=WEB"

        def openAppForManageVO3 = new OpenAppForManageVO()
        openAppForManageVO3.appId = "FSAID_9896e1"
        openAppForManageVO3.appName = "Fieldwork"
        openAppForManageVO3.appDesc = "Fieldwork sign-in is a sign-in app created for fieldwork personnel."
        openAppForManageVO3.appType = 3
        openAppForManageVO3.appMode = 1
        openAppForManageVO3.appCreater = 1
        openAppForManageVO3.createrType = 2
        openAppForManageVO3.appClass = 4
        openAppForManageVO3.serviceName = "Fieldwork"
        openAppForManageVO3.bindStatus = 1
        openAppForManageVO3.payType = 2
        openAppForManageVO3.appLogoUrl = "https://a6.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_8595b6d70d794451b81d298612fecdea.png&size=150_150&ea=appCenter&type=WEB"

        return Lists.newArrayList(openAppForManageVO1, openAppForManageVO2, openAppForManageVO3)
    }

    //构建企业内应用组件的返回值
    BaseResult<List<PaasAppViewsForEaVo>> buildPaasAppViewsForEaVoResult() {
        def result = new BaseResult<List<PaasAppViewsForEaVo>>()
        result.success
        result.setResult(buildPaasAppViewsForEaVoList())

        return result
    }

    //构建企业内应用组件的数据
    List<PaasAppViewsForEaVo> buildPaasAppViewsForEaVoList() {
        def paasAppViewsForEaVo1 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo1.componentName = "外勤"
        paasAppViewsForEaVo1.componentId = "FSAID_9896e2"
        paasAppViewsForEaVo1.imageUrl = "https://a0.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_bc25f8fb843e475aa2b59e7dc2103848.png&size=150_150&ea=appCenter&type=WEB"

        def openAppComponentDO1 = new OpenAppComponentDO()
        openAppComponentDO1.componentId = OUT_WORK
        openAppComponentDO1.componentName = "外勤"
        openAppComponentDO1.appId = "FSAID_9896e1"
        openAppComponentDO1.componentDesc = "外勤签到管理后台，可以统计和考察的客户拜访情况。丰富的统计维度满足管理需求，并可提供可能作弊的设备与账号。"
        openAppComponentDO1.componentLabel = 4
        openAppComponentDO1.componentType = 2

        def openAppDO1 = new OpenAppDO()
        openAppDO1.appId = "FSAID_9896e1"
        openAppDO1.appName = "外勤"
        openAppDO1.appDesc = "开启外勤应用，员工在出差、拜访客户等外出场景下可随时通过手机签到打卡。同时企业可为不同的外勤人员设置不同的规则和动作，还可以提前为员工设置外勤计划，使客户拜访标准化和周计划，同时Web端提供强大的统计能力，供管理员查看员工的外勤执行情况。"
        openAppDO1.appType = 3
        openAppDO1.appMode = 1
        openAppDO1.appCreater = "1"
        openAppDO1.createrType = 2
        openAppDO1.appClass = 4
        openAppDO1.serviceName = "外勤"
        openAppDO1.payType = 2

        paasAppViewsForEaVo1.openAppComponent = openAppComponentDO1
        paasAppViewsForEaVo1.openApp = openAppDO1


        def paasAppViewsForEaVo2 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo2.componentName = "考勤"
        paasAppViewsForEaVo2.componentId = "FSAID_9896e5"
        paasAppViewsForEaVo2.imageUrl = "https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_96b71b99d6674f279d18886715360229.png&size=150_150&ea=appCenter&type=WEB"

        def openAppComponentDO2 = new OpenAppComponentDO()
        openAppComponentDO2.componentId = "FSAID_9896e5"
        openAppComponentDO2.componentName = "考勤"
        openAppComponentDO2.appId = "FSAID_9896e3"
        openAppComponentDO2.componentDesc = "考勤签到管理后台，可多维度统计查看公司职员的出勤情况，加入防作弊功能。"
        openAppComponentDO2.componentLabel = 4
        openAppComponentDO2.componentType = 2

        def openAppDO2 = new OpenAppDO()
        openAppDO2.appId = "FSAID_9896e3"
        openAppDO2.appName = "考勤"
        openAppDO2.appDesc = "开启考勤应用，员工可通过手机一键签到签退，公司可为不同人员灵活设置多种排班规则，考勤数据支持在线查看和导出。考勤同时还拥有智能签到、打卡提醒、同步外勤数据等功能，帮助企业高效统计所有人员的考勤情况。"
        openAppDO2.appType = 3
        openAppDO2.appMode = 1
        openAppDO2.appCreater = "1"
        openAppDO2.createrType = 2
        openAppDO2.appClass = 4
        openAppDO2.serviceName = "考勤"
        openAppDO2.payType = 2

        paasAppViewsForEaVo2.openAppComponent = openAppComponentDO2
        paasAppViewsForEaVo2.openApp = openAppDO2

        def paasAppViewsForEaVo3 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo3.componentName = "假期管理"
        paasAppViewsForEaVo3.componentId = "FSAID_989780"
        paasAppViewsForEaVo3.imageUrl = "https://a3.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201909_02_43900d5e55c54f9dbebd54e99f308cde.png&size=150_150&ea=appCenter&type=WEB"

        def openAppComponentDO3 = new OpenAppComponentDO()
        openAppComponentDO3.componentId = "FSAID_989780"
        openAppComponentDO3.componentName = "假期管理"
        openAppComponentDO3.appId = "FSAID_98977f"
        openAppComponentDO3.componentDesc = "自定义请假/加班事项和申请单位，便于企业内请假/加班申请的管理。"
        openAppComponentDO3.componentLabel = 4
        openAppComponentDO3.componentType = 2

        def openAppDO3 = new OpenAppDO()
        openAppDO3.appId = "FSAID_98977f"
        openAppDO3.appName = "假期管理"
        openAppDO3.appDesc = "假期管理模块可自定义请假/加班事项和申请单位，便于请假/加班申请的管理；自动汇总员工各类型请假/加班和调休的申请时长，供管理员查看。假期管理支持导入员工当年应休总年假天数，同时会根据员工已申请年假自动计算剩余年假天数。在移动端员工可一目了然地查看自己各类请假/加班申请情况和剩余年假天数。"
        openAppDO3.appType = 3
        openAppDO3.appMode = 1
        openAppDO3.appCreater = "1"
        openAppDO3.createrType = 2
        openAppDO3.appClass = 4
        openAppDO3.serviceName = "假期管理"
        openAppDO3.payType = 2

        paasAppViewsForEaVo3.openAppComponent = openAppComponentDO3
        paasAppViewsForEaVo3.openApp = openAppDO3

        return Lists.newArrayList(paasAppViewsForEaVo1, paasAppViewsForEaVo2, paasAppViewsForEaVo3)
    }

    //构建用户侧的组件返回值
    BaseResult<List<PaasAppViewsForEaVo>> buildPaasAppViewsForUserVoResult() {
        def result = new BaseResult<List<PaasAppViewsForEaVo>>()
        result.isSuccess()
        result.setResult(buildUserPaasAppViewsForEaVoList())

        return result
    }

    //构建用户侧的组件返回信息
    List<PaasAppViewsForEaVo> buildUserPaasAppViewsForEaVoList() {
        def paasAppViewsForEaVo1 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo1.componentName = "外勤"
        paasAppViewsForEaVo1.componentId = "FSAID_9896e2"
        paasAppViewsForEaVo1.imageUrl = "https://a0.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_bc25f8fb843e475aa2b59e7dc2103848.png&size=150_150&ea=appCenter&type=WEB"
        paasAppViewsForEaVo1.loginAddress = "#app/checkin/statistics/=/param-%7B%22queryType%22%3A2%7D"

        def openAppComponentDO1 = new OpenAppComponentDO()
        openAppComponentDO1.componentId = "FSAID_9896e2"
        openAppComponentDO1.componentName = "外勤"
        openAppComponentDO1.appId = "FSAID_9896e1"
        openAppComponentDO1.componentDesc = "外勤签到管理后台，可以统计和考察的客户拜访情况。丰富的统计维度满足管理需求，并可提供可能作弊的设备与账号。"
        openAppComponentDO1.componentLabel = 4
        openAppComponentDO1.componentType = 2

        def openAppDO1 = new OpenAppDO()
        openAppDO1.appId = "FSAID_9896e1"
        openAppDO1.appName = "外勤"
        openAppDO1.appDesc = "开启外勤应用，员工在出差、拜访客户等外出场景下可随时通过手机签到打卡。同时企业可为不同的外勤人员设置不同的规则和动作，还可以提前为员工设置外勤计划，使客户拜访标准化和周计划，同时Web端提供强大的统计能力，供管理员查看员工的外勤执行情况。"
        openAppDO1.appType = 3
        openAppDO1.appMode = 1
        openAppDO1.appCreater = "1"
        openAppDO1.createrType = 2
        openAppDO1.appClass = 4
        openAppDO1.serviceName = "外勤"
        openAppDO1.payType = 2
        paasAppViewsForEaVo1.openAppComponent = openAppComponentDO1
        paasAppViewsForEaVo1.openApp = openAppDO1


        def paasAppViewsForEaVo2 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo2.componentName = "考勤"
        paasAppViewsForEaVo2.componentId = "FSAID_9896e5"
        paasAppViewsForEaVo2.imageUrl = "https://a1.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201908_28_96b71b99d6674f279d18886715360229.png&size=150_150&ea=appCenter&type=WEB"
        paasAppViewsForEaVo2.loginAddress = "#app/attendance/list"

        def openAppComponentDO2 = new OpenAppComponentDO()
        openAppComponentDO2.componentId = "FSAID_9896e5"
        openAppComponentDO2.componentName = "考勤"
        openAppComponentDO2.appId = "FSAID_9896e3"
        openAppComponentDO2.componentDesc = "考勤签到管理后台，可多维度统计查看公司职员的出勤情况，加入防作弊功能。"
        openAppComponentDO2.componentLabel = 4
        openAppComponentDO2.componentType = 2

        def openAppDO2 = new OpenAppDO()
        openAppDO2.appId = "FSAID_9896e3"
        openAppDO2.appName = "考勤"
        openAppDO2.appDesc = "开启考勤应用，员工可通过手机一键签到签退，公司可为不同人员灵活设置多种排班规则，考勤数据支持在线查看和导出。考勤同时还拥有智能签到、打卡提醒、同步外勤数据等功能，帮助企业高效统计所有人员的考勤情况。"
        openAppDO2.appType = 3
        openAppDO2.appMode = 1
        openAppDO2.appCreater = "1"
        openAppDO2.createrType = 2
        openAppDO2.appClass = 4
        openAppDO2.serviceName = "考勤"
        openAppDO2.payType = 2
        paasAppViewsForEaVo2.openAppComponent = openAppComponentDO2
        paasAppViewsForEaVo2.openApp = openAppDO2


        def paasAppViewsForEaVo3 = new PaasAppViewsForEaVo()
        paasAppViewsForEaVo3.componentName = "假期管理"
        paasAppViewsForEaVo3.componentId = "FSAID_989780"
        paasAppViewsForEaVo3.imageUrl = "https://a3.ceshi112.com/FSC/EM/Avatar/GetAvatar?path=N_201909_02_43900d5e55c54f9dbebd54e99f308cde.png&size=150_150&ea=appCenter&type=WEB"
        paasAppViewsForEaVo3.loginAddress = "#app/holiday/statistics"

        def openAppComponentDO3 = new OpenAppComponentDO()
        openAppComponentDO3.componentId = "FSAID_989780"
        openAppComponentDO3.componentName = "假期管理"
        openAppComponentDO3.appId = "FSAID_98977f"
        openAppComponentDO3.componentDesc = "自定义请假/加班事项和申请单位，便于企业内请假/加班申请的管理。"
        openAppComponentDO3.componentLabel = 4
        openAppComponentDO3.componentType = 2

        def openAppDO3 = new OpenAppDO()
        openAppDO3.appId = "FSAID_98977f"
        openAppDO3.appName = "假期管理"
        openAppDO3.appDesc = "假期管理模块可自定义请假/加班事项和申请单位，便于请假/加班申请的管理；自动汇总员工各类型请假/加班和调休的申请时长，供管理员查看。假期管理支持导入员工当年应休总年假天数，同时会根据员工已申请年假自动计算剩余年假天数。在移动端员工可一目了然地查看自己各类请假/加班申请情况和剩余年假天数。"
        openAppDO3.appType = 3
        openAppDO3.appMode = 1
        openAppDO3.appCreater = "1"
        openAppDO3.createrType = 2
        openAppDO3.appClass = 4
        openAppDO3.serviceName = "假期管理"
        openAppDO3.payType = 2
        paasAppViewsForEaVo3.openAppComponent = openAppComponentDO3
        paasAppViewsForEaVo3.openApp = openAppDO3


        return Lists.newArrayList(paasAppViewsForEaVo1, paasAppViewsForEaVo2, paasAppViewsForEaVo3)
    }


    def setup() {
        paaSAppDao = new PaaSAppDaoImpl()
        paaSAppDao.datastore = datastore

        crossApplicationService = new CrossApplicationServiceImpl()
        crossApplicationService.remoteCrossService = remoteCrossService
        crossApplicationService.fsUpstreamService = fsUpstreamService
        crossApplicationService.navigateMenuConfig = navigateMenuConfig
        crossApplicationService.componentConfig = componentConfig
        crossApplicationService.downstreamService = downstreamService

        innerApplicationService = new InnerApplicationServiceImpl()
        innerApplicationService.openFsUserBindAppService = openFsUserBindAppService
        innerApplicationService.qxEIEAConverter = qxEIEAConverter
        innerApplicationService.iPaasOpenAppViewService = iPaasOpenAppViewService
        innerApplicationService.navigateMenuConfig = navigateMenuConfig
        innerApplicationService.componentConfig = componentConfig

        def applicationFactoryBean = new ApplicationFactoryBean()
        applicationFactoryBean.innerApplicationService = innerApplicationService
        applicationFactoryBean.crossApplicationService = crossApplicationService

        applicationFactoryBean.init()
        applicationService = applicationFactoryBean.getObject()

        paaSAppService = new PaaSAppServiceImpl()
        paaSAppService.paaSAppDao = paaSAppDao
        paaSAppService.languageService = languageService
        paaSAppService.searchWordsService = searchWordsService
        paaSAppService.homePageCommonService = homePageCommonService
        paaSAppService.organizationCommonService = scopeService
        paaSAppService.remoteService = remoteService
        paaSAppService.applicationService = applicationService

        paaSAppAction = new PaaSAppActionImpl()
        paaSAppAction.paaSAppService = paaSAppService
        paaSAppAction.tempFileToFormalFile = tempFileToFormalFile
        paaSAppAction.roleService = roleService

        createPaaSEntityMessage()
    }

    def createPaaSEntityMessage() {

        def paaSAppEntity1 = new PaaSAppEntity()
        paaSAppEntity1.tenantId = 71574
        paaSAppEntity1.appId = "paaS__c"
        paaSAppEntity1.scopeList = Lists.newArrayList("D-999999")
        paaSAppEntity1.name = "name"
        paaSAppEntity1.description = "description"
        paaSAppEntity1.icon = "icon"
        paaSAppEntity1.status = 1
        paaSAppEntity1.sourceType = SourceType.CUSTOMER
        paaSAppEntity1.creatorId = 1000
        paaSAppEntity1.createTime = System.currentTimeMillis()
        paaSAppEntity1.updaterId = 1000
        paaSAppEntity1.updateTime = System.currentTimeMillis()

        paaSAppDao.savePaaSApp(paaSAppEntity1)
    }


}
