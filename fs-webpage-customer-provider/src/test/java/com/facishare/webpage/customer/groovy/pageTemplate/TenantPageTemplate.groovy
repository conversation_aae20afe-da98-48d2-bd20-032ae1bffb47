package com.facishare.webpage.customer.groovy.pageTemplate

import com.facishare.cep.plugin.enums.ClientTypeEnum
import com.facishare.webpage.customer.api.constant.TempleType
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetUserWebPageArg
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetWebPageTemplesArg

/**
 * Created by zhangyu on 2020/6/23
 */
class TenantPageTemplate extends PageTemplateBasic {

    def "getWebPageTemples - 拉取web配置列表"() {
        given:
        GetWebPageTemplesArg arg = new GetWebPageTemplesArg()
        arg.setAppId(appId)
        when:
        def webPageTemples = tenantPageTempleAction.getWebPageTemples(upUserInfo, arg)
        then:
        webPageTemples != null
        webPageTemples.webPageTemplateList.size() == 1
        def pageTemplate = webPageTemples.webPageTemplateList.get(0)
        pageTemplate.getWebPageId() == webPageTempleEntity.getWebPageId()
        pageTemplate.getWebMenuId() == webPageTempleEntity.getWebMenuId()
        pageTemplate.getType() == TempleType.WEB
    }

    def "getUserWebPage - web端用户态拉取列表"() {
        given:
        GetUserWebPageArg arg = new GetUserWebPageArg()
        arg.setAppId(appId)

        when:
        def result = userPageTempleAction.getUserWebPage(buildUserInfo(), buildOuterUserInfo(), buildClientInfo(ClientTypeEnum.Mac), arg)

        then:
        result != null

    }


}
