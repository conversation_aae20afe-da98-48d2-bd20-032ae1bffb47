package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.controller.model.result.homepage.GetHomePageByApiNameResult;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Created by zhangyu on 2019/11/4
 */
@Data
public class TestJsonToValue {
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private String dateId;

    private List<String> empsAndDeps;

    private boolean isAll;

    private Date startTime;

    private Date endTime;

    private String dateType;

    @Test
    public void testToJson(){
        TestJsonToValue testJsonToValue = new TestJsonToValue();
        testJsonToValue.setDateId("4");
        testJsonToValue.setEmpsAndDeps(Lists.newArrayList());
        testJsonToValue.setAll(true);
        testJsonToValue.setDateType("本月");
        testJsonToValue.setStartTime(new Date());
        testJsonToValue.setEndTime(new Date());

        String json = gson.toJson(testJsonToValue);
        System.out.println(json);

    }

    @Test
    public void testTimeStamp(){
        Timestamp timestamp = new Timestamp(new Date().getTime());
        System.out.println(timestamp);
    }

    @Test
    public void testJSONObject() {

        GetHomePageByApiNameResult.HomePageData homePageData = new GetHomePageByApiNameResult.HomePageData();
        homePageData.setName("测试");
        homePageData.setCurrentPage(true);
        homePageData.setLayoutId(UUID.randomUUID().toString().replace("-", ""));

        String toJSONString = JSONObject.toJSONString(homePageData);

        JSONObject jsonObject = JSONObject.parseObject(toJSONString, JSONObject.class);
        System.out.println(toJSONString);
        System.out.println(jsonObject);
    }

}
