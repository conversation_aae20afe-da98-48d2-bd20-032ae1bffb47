<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

    <import resource="classpath:META-INF/spring/objgroup-common.xml"/>
    <import resource="classpath:spring/spring-cms.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-rest-remote.xml"/>
    <import resource="classpath:enterpriserelation2/enterpriserelation-noappid.xml"/>
    <import resource="classpath:spring/fs-qixin-relation-rest.xml"/>
    <import resource="classpath:appcenterrest/appcenterrest.xml"/>
    <import resource="classpath:spring/applicationContext.xml"/>


</beans>