<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">


    <bean id="datastore" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="fs-webpage-customer-mongo-config"/>

    <bean id="tenantMenuDao" class="com.facishare.webpage.customer.dao.TenantMenuDaoImpl"/>
    <bean id="userMenuDao" class="com.facishare.webpage.customer.dao.UserMenuDaoImpl"/>
    <bean id="homePageLayoutDao" class="com.facishare.webpage.customer.dao.HomePageLayoutDaoImpl"/>
    <bean id="menuEntityDao" class="com.facishare.webpage.customer.dao.MenuEntityDaoImpl"/>
    <bean id="tenantConfigDao" class="com.facishare.webpage.customer.dao.TenantConfigDaoImpl"/>
    <bean id="tenantPageTempleDao" class="com.facishare.webpage.customer.dao.TenantPageTempleDaoImpl"/>
    <bean id="employeeConfigDao" class="com.facishare.webpage.customer.dao.EmployeeConfigDaoImpl"/>
    <bean id="employeeCurrentHomePageLayoutDao"
          class="com.facishare.webpage.customer.dao.EmployeeCurrentHomePageLayoutDaoImpl"/>
    <bean id="userPageTemplateDao" class="com.facishare.webpage.customer.dao.UserPageTemplateDaoImpl">
        <property name="datastore" ref="datastore"/>
    </bean>

</beans>