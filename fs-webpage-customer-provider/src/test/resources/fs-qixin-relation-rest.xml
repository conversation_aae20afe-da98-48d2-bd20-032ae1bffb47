<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        ">
    <!--  er互联  -->
    <bean id="retrofitFactory" class="com.github.zhxing.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-enterpriserelation-rest-api" init-method="init">
        <property name="headers">
            <map>
                <entry key="x-eip-appid" value="x_app_user_defined"/>
            </map>
        </property>
    </bean>

    <bean id="enterpriseRelationRoleService" class="com.facishare.qixin.relation.rest.EnterpriseRelationRoleService"/>
    <bean id="relationApiNamesConfig" class="com.facishare.qixin.relation.rest.RelationApiNamesConfig">
        <property name="configName" value="fs-qixin-relation-apiname-config"/>
    </bean>
    <bean id="fsDownstreamService" class="com.facishare.qixin.relation.rest.FsDownstreamServiceImpl"/>
</beans>