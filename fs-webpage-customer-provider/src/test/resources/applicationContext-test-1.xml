<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <import resource="classpath:META-INF/spring/fs-qixin-common-log.xml"/>

    <bean id="webPageEventService" class="com.facishare.webpage.customer.event.WebPageEventServiceImpl"/>

    <bean id="componentListManager" class="com.facishare.webpage.customer.core.business.ComponentListManager" init-method="init">
        <property name="configName" value="fs-webpage-customer-web-component-list-map"/>
    </bean>

    <bean id="menusConfig" class="com.facishare.webpage.customer.core.config.MenusConfig" init-method="init"/>

    <bean id="menusCollectionConfig" class="com.facishare.webpage.customer.core.config.MenusCollectionConfig" init-method="init"/>

    <bean id="widgetsConfig" class="com.facishare.webpage.customer.core.config.WidgetsConfig" init-method="init"/>

    <bean id="widgetCollectionConfig" class="com.facishare.webpage.customer.core.config.WidgetCollectionConfig" init-method="init"/>

    <bean id="customerCoreConfig" class="com.facishare.webpage.customer.core.config.CustomerCoreConfig"/>

    <bean id="componentConfig" class="com.facishare.webpage.customer.config.ComponentConfig"/>

    <bean id="webMainChannelConfig" class="com.facishare.webpage.customer.config.WebMainChannelConfig"/>

    <bean id="appMenuConfig" class="com.facishare.webpage.customer.config.AppMenuConfig"/>

</beans>